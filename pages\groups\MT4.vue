<template>
	<page :title="$t('groups.groups')">
		<template #actions>
			<v-btn
				v-if="can('group.create')"
				color="primary"
				prepend-icon="i-mdi:plus"
				@click="crud?.create()"
			>
				{{ $t('groups.create-group') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/groups/mt4"
			:headers="headers"
			search-key="name"
			:default-model="{ name: '' }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
		>
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:loading="isClearingCache"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>

			<template #item.icon>
				<v-icon icon="i-material-symbols:group-outline" />
			</template>
			<template #item.mc-so="{ item }:{item: Groups.MT4._Id.GETResponse}">
				{{ item.marginCallLevel }} / {{ item.marginStopOut }} %
			</template>
			<template #item.securityGroupTypes="{ value }">
				<div class="text-caption">
					{{ Object.values(value).join(', ') }}
				</div>
			</template>

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('group.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('group.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<groups-crud-mt4
		ref="crud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type SymbolsCrud from '@/components/Symbols/Crud/Mt4/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import type { Groups } from '~/types'

const table = ref <InstanceType<typeof Datatable> | null>(null)

const crud = ref<InstanceType<typeof SymbolsCrud> | null>(null)

// const searchTypeModel = ref('')

// const debouncedSearchTypeModel = useDebounce(searchTypeModel, 300)

const isClearingCache = ref(false)

// const { undo: back, history: lastOpenedPathHistory } = useRefHistory(lastOpenedType)

const { t } = useI18n()

const headers: Headers = [
	{
		title: '',
		value: 'icon',
	},
	{
		title: t('groups.name'),
		value: 'group',
		nowrap: true,
	},
	{
		title: t('groups.company'),
		value: 'company',
		nowrap: true,
	},
	{
		title: t('groups.mc-so'),
		value: 'mc-so',
		nowrap: true,
	},
	{
		title: t('groups.securities'),
		value: 'securityGroupTypes',
		maxWidth: '20%',
	},
	{
		value: 'actions',
	},
]

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/groups/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
	}).finally(() => {
		isClearingCache.value = false
	})
}
</script>
