<template>
	<component
		:is="component"
		v-model="model"
		v-bind="{ ...computedProps }"
	/>
</template>

<script lang="ts" setup>
import { VCombobox, VSelect, VAutocomplete } from 'vuetify/components'

defineOptions({
	extends: VCombobox,
})

type Props = {
	is?: 'VCombobox' | 'VSelect' | 'VAutocomplete'
	items?: VSelect['$props']['items'] | undefined
	returnObject?: boolean | undefined
	defaultValue: any
}

const p = withDefaults(defineProps<Props>(), {
	items: () => [],
	is: 'VSelect',
	returnObject: false,
})

const model = defineModel<any>()

const defaultModel = defineModel<boolean>('default', {
	type: Boolean,
	default: false,
	required: true,
})

const modelValueHandler = (value: any) => {
	defaultModel.value = value === p.defaultValue
}

const { t } = useI18n()

const computedProps = computed<VSelect['$props']>(() => {
	return {
		...p,
		items: [
			...p.items,
			{
				title: t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.default'),
				value: p.defaultValue,
			},
		],
	}
})

const component = computed(() => {
	switch (p.is) {
		case 'VCombobox':
			return VCombobox
		case 'VSelect':
			return VSelect
		case 'VAutocomplete':
		default:
			return VAutocomplete
	}
})
// @ts-ignore
watch(() => model.value, modelValueHandler, {
	immediate: true,
})

onMounted(() => {
	if (defaultModel.value) {
		model.value = p.defaultValue
	}
})
</script>
