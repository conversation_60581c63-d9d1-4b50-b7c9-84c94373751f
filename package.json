{"name": "ingotbrokers", "version": "1.4.2", "description": "INGOT Brokers Pilot Web App", "keywords": [], "private": true, "type": "module", "repository": {"type": "git", "url": "**************:IngotTech//mt-manager-frontend.git"}, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "release": "semantic-release --no-ci --debug", "prepare": "husky install", "lint": "eslint . --fix", "lint:types": "eslint ./types --fix", "test": "vitest run", "gen:perm": "node ./types/Permissions/generate.js"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.4.4", "@date-io/dayjs": "^3.0.0", "@iconify-json/fe": "^1.1.10", "@iconify-json/material-symbols": "^1.1.85", "@iconify-json/material-symbols-light": "^1.1.25", "@iconify-json/mdi": "^1.2.0", "@iconify-json/mdi-light": "^1.1.10", "@iconify-json/ph": "^1.1.14", "@nuxt/eslint": "^0.6.0", "@nuxt/test-utils": "^3.14.0", "@nuxtjs/i18n": "^8.3.3", "@nuxtjs/robots": "^4.0.2", "@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^12.0.1", "@skmd87/vuetify-confirm": "^1.0.12", "@types/dot-object": "^2.1.6", "@types/lodash": "^4.17.7", "@unocss/nuxt": "^0.61.9", "@unocss/preset-icons": "^0.61.9", "@vue/test-utils": "^2.4.6", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "async-validator": "^4.2.5", "dayjs-nuxt": "^2.1.9", "eslint": "^9.13.0", "happy-dom": "^15.11.0", "husky": "^8.0.3", "nuxt": "^3.12.4", "playwright-core": "^1.45.3", "sass": "1.77", "semantic-release": "^23.1.1", "socket.io-client": "^4.7.5", "vue": "^3.4.35", "vue-easy-dnd": "^2.1.3", "vue-router": "^4.4.2", "vue-tsc": "^2.1.6", "vuetify": "^3.8.1", "vuetify-nuxt-module": "^0.18.3"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@iconify-json/arcticons": "^1.1.108", "@iplookup/country": "^1.0.20240925", "@nuxt/fonts": "^0.7.1", "@nuxt/image": "^1.7.0", "@pinia/nuxt": "^0.5.2", "@sentry/nuxt": "^8.35.0", "@vueuse/integrations": "^11.1.0", "@vueuse/nuxt": "^11.1.0", "@wdns/vuetify-inline-fields": "^1.0.9", "change-case": "^5.4.4", "chart.js": "^3.9.1", "defu": "^6.1.4", "dot-object": "^2.1.5", "eslint-typegen": "^0.3.2", "firebase": "^10.13.2", "hex-color-to-color-name": "^1.0.2", "i18n-iso-countries": "^7.11.3", "lodash": "^4.17.21", "maska": "^3.0.0", "minimatch": "^9.0.5", "mitt": "^3.0.1", "nuxt-jsonld": "^2.0.8", "pinia": "^2.2.0", "pinia-plugin-persistedstate": "^4.1.1", "sortablejs": "1.14", "typescript": "^5.5.4", "unplugin-icons": "^0.21.0", "vue-chart-3": "^3.1.8", "vue3-grid-layout-next": "^1.0.7", "vue3-snackbar": "^2.4.0", "vuevectormap": "^2.0.1"}}