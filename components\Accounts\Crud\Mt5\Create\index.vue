<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Accounts.MT5.Crud.account')"
		item-identity="name"
		:navigation-drawer-props="{ width: 550 }"
		:url="Accounts.MT5._Id.URL(':login')"
		:update-url="false"
		v-bind="$attrs"
	>
		<template #default="{ item, errors }:{item:Item<Accounts.MT5._Id.POSTRequest>, errors:ItemErrors<Accounts.MT5._Id.POSTRequest> }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-combobox
					v-model="item.login"
					type="number"
					hide-spin-buttons
					:items="[{ title: 'next', value: 0 }]"
					:label="$t('Accounts.MT5.Crud.login')"
					:error-messages="errors.login"
					:return-object="false"
				/>
				<v-row>
					<v-col
						cols="12"
						md="6"
					>
						<api-items
							v-slot="props"
							:url="Groups.Lookup.URL"
							:error-messages="errors.group"
						>
							<v-autocomplete
								v-model="item.group"
								:label="$t('Accounts.MT5.Crud.group')"
								:rules="rules({ required: true })"
								v-bind="props"
								clearable
							/>
						</api-items>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-autocomplete
							v-model="item.clientID"
							disabled
							:label="$t('Accounts.MT5.Crud.existing-client')"
							:error-messages="errors.clientID"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="4"
					>
						<v-text-field
							v-model="item.firstName"
							:label="$t('Accounts.MT5.Crud.first-name')"
							:rules="rules({ required: true })"
							:error-messages="errors.firstName"
						/>
					</v-col>
					<v-col
						cols="12"
						md="4"
					>
						<v-text-field
							v-model="item.middleName"
							:label="$t('Accounts.MT5.Crud.middle-name')"
							:error-messages="errors.middleName"
						/>
					</v-col>
					<v-col
						cols="12"
						md="4"
					>
						<v-text-field
							v-model="item.lastName"
							:label="$t('Accounts.MT5.Crud.last-name')"
							:error-messages="errors.lastName"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="4"
					>
						<password
							v-model="item.masterPass"
							v-model:strength="masterPasswordStrength"
							:label="$t('Accounts.MT5.Crud.master-password')"
							show-strength
							:error-messages="errors.masterPass"
							:rules="[rule({ required: true }), () => masterPasswordStrength === 100 || $t('Accounts.MT5.Crud.the-password-strength-is-low')]"
						/>
					</v-col>
					<v-col
						cols="12"
						md="4"
					>
						<password
							v-model="item.investorPass"
							v-model:strength="investorPasswordStrength"
							:label="$t('Accounts.MT5.Crud.investor-password')"
							show-strength
							:error-messages="errors.investorPass"
							:rules="[rule({ required: true }), () => investorPasswordStrength === 100 || 'The password strength is low']"
						/>
					</v-col>
					<v-col
						cols="12"
						md="4"
					>
						<password
							v-model="item.phonePassword"
							:label="$t('Accounts.MT5.Crud.phone-password')"
							:error-messages="errors.phonePassword"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.company"
							:label="$t('Accounts.MT5.Crud.company')"
							:error-messages="errors.company"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.phone"
							:label="$t('Accounts.MT5.Crud.phone')"
							:error-messages="errors.phone"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.eMail"
							:label="$t('Accounts.MT5.Crud.email')"
							:error-messages="errors.eMail"
							:rules="rules({ type: 'email' })"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.city"
							:label="$t('Accounts.MT5.Crud.city')"
							:error-messages="errors.city"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="6"
					>
						<v-select
							v-model="item.country"
							:items="countriesItems"
							:label="$t('Accounts.MT5.Crud.country')"
							:error-messages="errors.country"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.zipCode"
							:label="$t('Accounts.MT5.Crud.zipcode')"
							:error-messages="errors.zipCode"
						/>
					</v-col>
				</v-row>
				<v-row class="mt-n5">
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model="item.state"
							:label="$t('Accounts.MT5.Crud.state')"
							:error-messages="errors.state"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-textarea
							v-model="item.address"
							:error-messages="errors.address"
							:label="$t('Accounts.MT5.Crud.address')"
							rows="1"
						/>
					</v-col>
				</v-row>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { DefaultItem, Item, ItemErrors } from '~/components/Crud.vue'
import Crud from '~/components/Crud.vue'

import { Accounts, Groups } from '~/types'

const { generate: generateInvestorPass } = useGeneratePassword()
const { generate: generateMasterPass } = useGeneratePassword()

const defaultItem: DefaultItem<Accounts.MT5._Id.POSTRequest> = {
	login: 0,
	group: 'managers\\administrators',
	certSerialNumber: 0,
	rights: 2403 as Accounts.MT5.EnUsersRights,
	registration: 0,
	lastAccess: 0,
	lastIP: '',
	name: '',
	company: '',
	account: '',
	country: 'Jordan',
	language: 1,
	city: '',
	state: '',
	zipCode: '',
	address: '',
	phone: '',
	eMail: '',
	id: '',
	status: '',
	comment: '',
	color: **********,
	phonePassword: '',
	leverage: 100,
	agent: 0,
	balance: 0,
	credit: 0,
	interestRate: 0,
	commissionDaily: 0,
	commissionMonthly: 0,
	commissionAgentDaily: 0,
	commissionAgentMonthly: 0,
	balancePrevDay: 0,
	balancePrevMonth: 0,
	equityPrevDay: 0,
	equityPrevMonth: 0,
	lastPassChange: **********,
	leadCampaign: '',
	leadSource: '',
	externalAccountTotal: 0,
	mqid: '0',
	clientID: 0,
	firstName: '',
	lastName: '',
	middleName: '',
	otpSecret: '',
	limitOrders: 0,
	limitPositionsValue: 0,
	investorPass: generateInvestorPass(),
	masterPass: generateMasterPass(),
} satisfies DefaultItem<Accounts.MT5._Id.POSTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const masterPasswordStrength = ref(0)
const investorPasswordStrength = ref(0)

const { items: countriesItems } = useCountries({ value: 'name' })

const generatePasswordsHandler = () => {
	defaultItem.investorPass = generateInvestorPass()
	defaultItem.masterPass = generateMasterPass()
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: (...args) => {
		generatePasswordsHandler()
		nextTick(() => {
			crudRef.value?.formRef?.resetValidation()
			crudRef.value!.create(...args)
		})
	},
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
