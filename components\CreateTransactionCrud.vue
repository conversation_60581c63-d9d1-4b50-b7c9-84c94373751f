<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Transaction"
		:navigation-drawer-props="{ width: 400 }"
		url="/transactions/:id"
		v-bind="$attrs"
		@reset="() => {
			operationId = null;
			actionTypeId = null ;
		}"
	>
		<template #default="{ item, errors }: { item: Nullable<Transactions._Id.POSTRequest>, errors: ItemErrors<Transactions._Id.POSTRequest>}">
			<api-items
				v-slot="props"
				url="/my/operations"
			>
				<v-select
					v-model="operationId"
					:rules="rules({ required: true })"
					:label="$t('CreateTransactionCrud.operation')"
					v-bind="props"
					item-title="displayName"
					item-value="id"
					@update:model-value="($event:any) => { actionTypeId = null; item.actionId = null; item.fromAccount = null; item.toAccount = null; }"
				/>
			</api-items>
			<api-items
				v-slot="props"
				ref="actionTypesApiItemsRef"
				url="/my/action-types"
				:api-options="{
					params: {
						operationId: computed(() => operationId || undefined),
					},
				}"
			>
				<v-select
					v-model="actionTypeId"
					:rules="rules({ required: true })"
					:label="$t('CreateTransactionCrud.action-type')"
					v-bind="props"
					item-title="displayName"
					item-value="id"
					:disabled="!operationId"
					@update:model-value="($event:any) => { item.actionId = null; item.fromAccount = { ...defaultAccount }; item.toAccount = { ...defaultAccount }; }"
				/>
			</api-items>
			<api-items
				v-slot="props"
				ref="actionsApiItemsRef"
				url="/my/actions"
				:api-options="{
					params: {
						actionTypeId: computed(() => actionTypeId || undefined),
					},
				}"
			>
				<v-select
					v-model="item.actionId"
					:rules="rules({ required: true })"
					:label="$t('CreateTransactionCrud.action')"
					v-bind="props"
					item-title="displayName"
					item-value="id"
					:error-messages="errors.actionId"
					:disabled="!actionTypeId"
					@update:model-value="($event:any) => { selectedActionId = $event;item.fromAccount = { ...defaultAccount }; item.toAccount = { ...defaultAccount }; }"
				/>
			</api-items>
			<api-items
				v-if="hasFromAccount"
				v-slot="props"
				ref="fromAccountsApiItemsRef"
				url="/my/accounts"
				:api-options="{
					params: {
						loginNumber: computed(() => fromAccountSearch || undefined),
					} }"
				:map-items="(items) => accountsMapper(items, item.toAccount?.login)"
			>
				<v-autocomplete
					v-model="item.fromAccount"
					v-model:search="fromAccountSearch"
					return-object
					auto-select-first
					:suffix="item.fromAccount?.currency && `(${(item.fromAccount?.currency)} Currency)`"
					:rules="rules({ required: true })"
					:label="$t('CreateTransactionCrud.from-account')"
					clearable
					:placeholder="$t('CreateTransactionCrud.start-typing-to-search')"
					item-title="login"
					v-bind="props"
					:error-messages="errors.fromAccount"
					:disabled="!item.actionId"
					@update:model-value="($event:any) => { item.toAccount = null; }"
				/>
			</api-items>

			<api-items
				v-slot="props"
				ref="toAccountsApiItemsRef"
				url="/my/accounts"
				:map-items="(items) => accountsMapper(items, item.fromAccount?.login)"
				:api-options="{
					params: {
						loginNumber: computed(() => toAccountSearch || undefined),
					},
				}"
			>
				<v-autocomplete
					v-model="item.toAccount"
					v-model:search="toAccountSearch"
					return-object
					auto-select-first
					:suffix="item.toAccount?.currency && `(${(item.toAccount?.currency)} Currency)`"
					item-title="login"
					:rules="rules({ required: true })"
					:label="toAccountLabel"
					clearable
					v-bind="props"
					:placeholder="$t('CreateTransactionCrud.start-typing-to-search')"
					:disabled="!item.actionId"
					:error-messages="errors.toAccount"
				/>
			</api-items>
			<v-text-field
				v-model="item.amount"
				:prefix="amountCurrency"
				type="number"
				hide-spin-buttons
				:label="$t('CreateTransactionCrud.amount')"
				:error-messages="errors.amount"
				:rules="rules({ required: true })"
				:disabled="!item.actionId"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type ApiItems from './ApiItems.vue'
import type Crud from './Crud.vue'
import type { DefaultItem, ItemErrors } from './Crud.vue'
import type { Nullable } from '~/types/Helpers'
import type { My, Transactions } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultAccount = {
	currency: '',
	login: '',
	group: '',
}

const defaultItem: DefaultItem<Transactions._Id.POSTRequest> = {
	actionId: null,
	amount: null,
	fromAccount: {
		...defaultAccount,
	},
	toAccount: {
		...defaultAccount,
	},
}

const operationId = ref<null | number>(null)

const actionTypeId = ref<null | number>(null)

const actionTypesApiItemsRef = ref<null | InstanceType<typeof ApiItems>>(null)

const selectedActionType = computed<My.ActionTypes.GETResponse>(() => actionTypesApiItemsRef.value?.$bind.items.find(item => item.id === actionTypeId.value))

const actionsApiItemsRef = ref<null | InstanceType<typeof ApiItems>>(null)

const selectedActionId = ref<number | null>(null)

const selectedAction = computed<My.Actions.GETResponse>(() => actionsApiItemsRef.value?.$bind.items.find(item => item.id === selectedActionId.value))

const hasFromAccount = computed(() => !selectedAction.value?.onlyReceiver)

const fromAccountSearch = ref('')

const toAccountSearch = ref('')

const fromAccountsApiItemsRef = ref<null | InstanceType<typeof ApiItems>>(null)

const selectedFromAccount = computed(() => fromAccountsApiItemsRef.value?.$bind.items.find(item => item.value === item.fromAccount))

const toAccountsApiItemsRef = ref<null | InstanceType<typeof ApiItems>>(null)

const selectedToAccount = computed(() => toAccountsApiItemsRef.value?.$bind.items.find(item => item.value === item.toAccount))

const { t } = useI18n()

const amountCurrency = computed(() => {
	if (selectedAction.value?.onlyReceiver && selectedActionType.value?.isIncremental) {
		// deposit
		return selectedToAccount.value?.currency
	} else if (selectedAction.value?.onlyReceiver && !selectedActionType.value?.isIncremental) {
		// withdrawal
		return selectedToAccount.value?.currency
	} else {
		// internal transfer
		return selectedFromAccount.value?.currency
	}
})

const toAccountLabel = computed(() => {
	if (selectedAction.value?.onlyReceiver && !selectedActionType.value?.isIncremental) {
		return t('CreateTransactionCrud.from-account')
	}

	return t('CreateTransactionCrud.to-account')
})

const accountsMapper = (items: any[], account: string | undefined) => items
	.filter(i => i.login !== account)
	.map(i => ({
		login: i.login,
		currency: i.currency,
		group: i.group,
		props: {
			subtitle: i.group,
		},
	}))

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
