import type { Pagination } from '../Helpers'

export namespace Alerts {
	export const URL = '/alerts'
	export enum AlertType {
		STOP_DURATION = 1,
		PERCENTAGE = 2,
		SPREAD = 3,
	}
	export interface Record {
		id: number
		name: string
		categoryIds: number[]
		excludedSymbols: string[]
		type: AlertType
		configValue: number
		emails: string[]
		createdAt: string
	}

	export interface GETResponse extends Pagination<Record> {}

	export namespace _Id {
		export const URL = (_Id: number | string) => `/alerts/${_Id}`

		export type GETResponse = Record

		export type PUTRequest = Omit<Record, 'id' | 'createdAt'>

		export type POSTRequest = Omit<Record, 'id' | 'createdAt'>
	}

}
