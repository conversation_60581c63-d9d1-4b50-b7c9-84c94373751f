import piniaConfig from './config/pinia'
import imageConfig from './config/image'
import i18nConfig from './config/i18n'
import robotsConfig from './config/robots'
import gtmConfig from './config/gtm'
import vuetifyConfig from './config/vuetify'
import unoCss from './config/unoCss'
import vueuseConfig from './config/vueuse'
import autoAnimateConfig from './config/autoAnimate'
import testUtilsConfig from './config/testUtils'
import dayjsConfig from './config/dayjs'
import sentryConfig from './config/sentry'

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: [
	],
	modules: [
		['@pinia/nuxt', piniaConfig],
		['@formkit/auto-animate', autoAnimateConfig],
		['@vueuse/nuxt', vueuseConfig],
		['@nuxt/image', imageConfig],
		['@nuxtjs/robots', robotsConfig],
		['@zadigetvoltaire/nuxt-gtm', gtmConfig],
		['@unocss/nuxt', unoCss],
		['@nuxt/test-utils/module', testUtilsConfig],
		['dayjs-nuxt', dayjsConfig],
		'@skmd87/vuetify-confirm',
		// make i18n last so it will allow prev modules to register their locales https://v8.i18n.nuxtjs.org/guide/extend-messages
		['@nuxtjs/i18n', i18nConfig],
		// this should be after i18n https://vuetify-nuxt-module.netlify.app/guide/i18n.html#using-vuetify-translations
		['vuetify-nuxt-module', vuetifyConfig],
		'@nuxtjs/robots',
		'@nuxt/fonts',
		'@nuxt/eslint',
		['@sentry/nuxt/module', sentryConfig],
		// ['nuxt-snackbar', snackbarConfig],
	],
	$development: {

		extends: [

		], routeRules: {

		},
	},
	$production: {

		extends: [
		], routeRules: {

		},
	},
	ssr: false,
	devtools: {
		enabled: true,
	},
	css: [
		// 'vuetify/styles',
		'@/assets/styles/main.scss',
	],
	runtimeConfig: {
		public: {
			apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL,
			socketUrl: process.env.NUXT_PUBLIC_SOCKET_URL,
			devTools: process.env.NUXT_PUBLIC_DEV_TOOLs,
			sentry: {
				dsn: process.env.NUXT_PUBLIC_SENTRY_DSN,
				environment: process.env.NUXT_PUBLIC_SENTRY_ENVIRONMENT,
			},
			fcm: {
				firebaseOptions: {
					apiKey: process.env.NUXT_PUBLIC_FCM_API_KEY,
					authDomain: process.env.NUXT_PUBLIC_FCM_AUTH_DOMAIN,
					projectId: process.env.NUXT_PUBLIC_FCM_PROJECT_ID,
					storageBucket: process.env.NUXT_PUBLIC_FCM_STORAGE_BUCKET,
					messagingSenderId: process.env.NUXT_PUBLIC_FCM_MESSAGING_SENDER_ID,
					appId: process.env.NUXT_PUBLIC_FCM_APP_ID,
				},
				vapidKey: process.env.NUXT_PUBLIC_FCM_VAPID_KEY,
			},
			log: {
				showLog: process.env.NODE_ENV !== 'production',
			},
		},
	},
	alias: {
		config: '/<rootDir>/config',
	},
	build: {
		analyze: !!process.env.NUXT_BUILD_ANALYZE,
	},
	routeRules: {
		// '/admin/**': { ssr: false },
		'/*': { ssr: false },
		'/en/**': { redirect: '/**' },
		'/ar/**': { redirect: '/**' },
	},
	sourcemap: { client: true },
	watch: ['config', '!dist'],
	features: {
		inlineStyles: false,
	},
	experimental: {
		typedPages: true,
		scanPageMeta: true,
	},
	vite: {
		esbuild: {
			tsconfigRaw: {
				compilerOptions: {
					experimentalDecorators: true,
					target: 'esnext',
				},
			},
		},
		optimizeDeps: {
			include: [
				'vue3-grid-layout-next',
				'lodash/isEqual',
				'i18n-iso-countries',
				'hex-color-to-color-name',
				'@vueuse/integrations/useAsyncValidator',
				'@vueuse/integrations/useSortable',
				'@vueuse/integrations/useChangeCase',
				'pinia-plugin-persistedstate',
				'socket.io-client',
				'dot-object',
				'vuevectormap/src/js/component.js',
				'jsvectormap/dist/maps/world',
				'lodash/omit',
				'lodash/pick',
				'lodash/merge',
				'firebase/app',
				'firebase/messaging',
				'vue-chart-3',
			],
		},
	},
	typescript: {
		typeCheck: true,
		strict: true,
		shim: false, // assuming running volar with takeover mode
	},
	fonts: {
		defaults: {
			weights: ['100', '300', '400', '500', '600', '700', '900'],
		},
	},
})
