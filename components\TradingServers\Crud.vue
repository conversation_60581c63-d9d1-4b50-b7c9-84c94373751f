<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Server"
		item-identity="displayName"
		:navigation-drawer-props="{ width: 500 }"
		url="/trading-servers/:id"
		:create-mapper="mapperHandler"
		:update-mapper="mapperHandler"
		v-bind="$attrs"
	>
		<template #default="{ item, errors, isCreateAction }: { item: Item<TradingServers._Id.GETResponse | TradingServers._Id.POSTRequest | TradingServers._Id.PUTRequest>, errors: ItemErrors<TradingServers._Id.GETResponse>, isCreateAction: boolean}">
			<v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				:rules="rules({ required: true })"
				:label="$t('TradingServersCrud.name')"
			/>
			<v-text-field
				v-model="item.displayName"
				:error-messages="errors.displayName"
				:rules="rules({ required: true })"
				:label="$t('TradingServersCrud.display-name')"
			/>
			<api-items
				v-slot="props"
				url="/trading-platforms/lookup"
				:error-messages="errors.tradingPlatformId"
			>
				<v-select
					v-model="item.tradingPlatformId"
					:rules="rules({ required: true })"
					:label="$t('TradingServersCrud.trading-platform')"
					v-bind="props"
					item-title="name"
					item-value="id"
				/>
			</api-items>
			<v-radio-group
				v-model="item.integrationType"
				inline
			>
				<v-radio
					:label="$t('TradingServersCrud.manager-account')"
					:value="TradingServers.IntegrationType.MANAGER_ACCOUNT"
				/>
				<v-radio
					:label="$t('TradingServersCrud.rest-api')"
					:value="TradingServers.IntegrationType.REST_API"
				/>
			</v-radio-group>
			<div v-auto-animate>
				<v-row v-if="item.integrationType === TradingServers.IntegrationType.MANAGER_ACCOUNT">
					<v-col>
						<v-text-field
							v-model="item.ipAddress"
							v-mask="Mask.IP"
							:error-messages="'ipAddress' in errors ? errors.ipAddress : []"
							:rules="[
								rule({ type: 'string', required: true }),
								rule({ type: 'string', pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: (a) => $t('rules.pattern.mismatch', { value: item.ipAddress, pattern: $t('TradingServersCrud.ip-address').toLowerCase() }) }),
							]"
							:label="$t('TradingServersCrud.ip-address')"
						/>
					</v-col>
					<v-col>
						<v-text-field
							v-model="item.port"
							type="number"
							hide-spin-buttons
							:error-messages="'port' in errors ? errors.port : []"
							:rules="rules({ required: true, type: 'number', min: 1, max: 65535, transform: (value) => value ? parseInt(value) : null })"
							:label="$t('TradingServersCrud.port')"
						/>
					</v-col>
				</v-row>
				<v-row v-else>
					<v-col>
						<v-text-field
							v-model="item.apiUrl"
							:error-messages="'apiUrl' in errors? errors.apiUrl: []"
							:rules="rules({ type: 'url', required: true })"
							:label="$t('TradingServersCrud.url')"
						/>
					</v-col>
				</v-row>
			</div>
			<v-row no-gutters>
				<v-col class="pe-6">
					<v-text-field
						v-model="item.webLogin"
						hide-spin-buttons
						:error-messages="errors.webLogin"
						:rules="rules({ required: true })"
						:label="$t('TradingServersCrud.login')"
						type="number"
					/>
				</v-col>
				<v-col>
					<v-text-field
						v-model="item.webPassword"
						:persistent-placeholder="!isCreateAction"
						:placeholder="isCreateAction ? undefined : $t('TradingServersCrud.leave-empty-for-no-change')"
						:error-messages="errors.webPassword"
						:rules="[isCreateAction ? rule({ required: true }) : true]"
						:label="$t('TradingServersCrud.password')"
					/>
				</v-col>
			</v-row>

			<v-switch
				v-model="item.isActive"
				color="success"
				:label="$t('TradingServersCrud.is-active')"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type { DefaultItem, Item, ItemErrors } from '~/components/Crud.vue'
import { Mask } from '~/types/Mask'
import { TradingServers } from '~/types/'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<TradingServers._Id.POSTRequest | TradingServers._Id.PUTRequest> = {
	name: '',
	displayName: '',
	integrationType: 1,
	ipAddress: '',
	webLogin: null,
	webPassword: '',
	apiUrl: '',
	port: null,
	isActive: false,
	tradingPlatformId: null,
}

const mapperHandler = (item: TradingServers._Id.POSTRequest | TradingServers._Id.PUTRequest) => {
	return {
		...item,
		ipAddress: item.integrationType === TradingServers.IntegrationType.MANAGER_ACCOUNT ? item.ipAddress : undefined,
		port: item.integrationType === TradingServers.IntegrationType.MANAGER_ACCOUNT ? item.port : undefined,
		url: item.integrationType === TradingServers.IntegrationType.REST_API ? item.apiUrl : undefined,
	}
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
