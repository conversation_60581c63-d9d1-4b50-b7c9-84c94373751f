// import type { RouteNamedMap } from 'vue-router/auto-routes'
// import type { RouteMap, RouteLocationAsString, RouteLocationAsRelativeTypedList, RouteLocationAsPathTypedList } from 'vue-router'

// // I18n routes
// type RemoveUnderscoreKeys<T> = {
// 	// eslint-disable-next-line @typescript-eslint/no-unused-vars
// 	[K in keyof T as K extends `${infer Prefix}___${infer Suffix}` ? Prefix : K]: T[K]
// }

// type NewRouteNamedMap = RemoveUnderscoreKeys<RouteNamedMap>

// type NewRouteLocationRaw<Name extends keyof NewRouteNamedMap = keyof NewRouteNamedMap> =
// 	| RouteLocationAsString<keyof RouteMap> // TODO: remove ar,en from RouteMap
// 	| RouteLocationAsRelativeTypedList<NewRouteNamedMap>[Name]
// 	| RouteLocationAsPathTypedList<NewRouteNamedMap>[Name]

// /**
//  * The function that resolve locale path.
//  *
//  * @remarks
//  * The parameter sygnatures of this function is same as {@link localePath}.
//  *
//  * @param route - A route location. The path or name of the route or an object for more complex routes.
//  * @param locale - A locale optional, if not specified, uses the current locale.
//  *
//  * @returns Returns the localized URL for a given route.
//  *
//  * @see {@link useLocalePath}
//  *
//  * @public
//  */
// type Locale = string
// type NewLocalePathFunction = (route: NewRouteLocationRaw, locale?: Locale) => string

// declare module '../node_modules/@nuxtjs/i18n/dist/runtime/composables/index' {
// 	/**
// 	 * The `useLocalePath` composable returns a function that resolves a path according to the current locale.
// 	 *
// 	 * @remarks
// 	 * The function returned by `useLocalePath` is the wrapper function with the same signature as {@link localePath}.
// 	 *
// 	 * `useLocalePath` is powered by [vue-i18n-routing](https://github.com/intlify/routing/tree/main/packages/vue-i18n-routing).
// 	 *
// 	 * @param options - An options object, see {@link I18nCommonRoutingOptionsWithComposable}
// 	 *
// 	 * @returns A {@link LocalePathFunction}.
// 	 *
// 	 * @public
// 	 */

// 	function useoptions?: any: NewLocalePathFunction
// }

// declare module '#app' {
// 	interface NuxtApp {
// 		// $options?: any: NewLocalePathFunction;
// 	}
// }
// declare module 'vue' {
// 	interface ComponentCustomProperties {
// 		$options?: any: NewLocalePathFunction
// 	}
// }
// declare module '@vue/runtime-core' {
// 	interface ComponentCustomProperties {
// 		$options?: any: NewLocalePathFunction
// 	}
// }

// export {}
