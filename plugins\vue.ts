import { vMaska } from 'maska/vue'
// import { createVInlineFields } from '@wdns/vuetify-inline-fields'
import { SnackbarService } from 'vue3-snackbar'
import 'vue3-snackbar/styles'

declare module '#app' {
	interface NuxtApp {
		$window: Window
	}
}
declare module 'vue' {
	interface ComponentCustomProperties {
		$window: Window
	}
}
declare module '@vue/runtime-core' {
	interface ComponentCustomProperties {
		$window: Window
	}
}

export default defineNuxtPlugin((nuxtApp) => {
	// const VInlineFields = createVInlineFields({
	// 	saveIcon: 'i-mdi:floppy',
	// 	cancelIcon: 'i-mdi:close',
	// })

	// nuxtApp.vueApp.use(VInlineFields)

	nuxtApp.vueApp.directive('mask', (el, binding, vnode) => {
		// @ts-ignore
		return vMaska(el, binding, vnode)
	})

	nuxtApp.vueApp.use(SnackbarService)

	return {
		provide: {
			window,
		},
	}
})
