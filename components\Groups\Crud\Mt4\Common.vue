<template>
	<v-container fluid>
		<v-switch
			v-model="item.enable"
			hide-details
			color="success"
			:label="$t('Groups.MT4.Crud.Common.enabled')"
			:true-value="1"
			:false-value="0"
		/>
		<v-defaults-provider
			:defaults="{
				global: {
					disabled: item.enable === 0,
				},
			}"
		>
			<v-text-field
				v-model="item.group"
				:hint="loadedGroupName != item.group ? $t('Groups.MT4.Crud.Common.changing-group-name-new-group') : undefined"
				:label="$t('Groups.MT4.Crud.Common.name')"
				:rules="rules({ required: true, whitespace: true })"
			/>
			<v-select
				v-model="item.otpMode"
				:items="otpModeItems"
				:label="$t('Groups.MT4.Crud.Common.one-time-password')"
			>
				<template #append>
					<v-switch
						v-model="rightsModel"
						:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_FORCED_OTP_USAGE"
						hide-details
						color="success"
						:label="$t('Groups.MT4.Crud.Common.force-otp-usage')"
					/>
				</template>
			</v-select>
			<api-items
				v-slot="props"
				url="/lookups/mt-companies"
			>
				<v-select
					v-model="item.company"
					v-bind="props"
					:label="$t('Groups.MT4.Crud.Common.owner')"
				/>
			</api-items>
			<v-text-field
				v-model="item.supportPage"
				:label="$t('Groups.MT4.Crud.Common.support-page')"
			/>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.defaultDeposit"
						:label="$t('Groups.MT4.Crud.Common.deposit-by-default')"
					/>
				</v-col>
				<v-col>
					<api-items
						v-slot="props"
						url="/currencies/lookup"
					>
						<v-combobox
							v-model="item.currency"
							item-value="symbol"
							item-title="symbol"
							v-bind="props"
							:label="$t('Groups.MT4.Crud.Common.deposit-currency')"
						/>
					</api-items>
				</v-col>
			</v-row>
			<v-row class="mt-0">
				<v-col
					cols="12"
					md="6"
					class="pt-0"
				>
					<v-select
						v-model="item.defaultLeverage"
						:items="defaultLeverageItems"
						:label="$t('Groups.MT4.Crud.Common.leverage-by-default')"
						:hint="$t('Groups.MT4.Crud.Common.used-only-for-trade-in-fo')"
					/>
					<v-text-field
						v-model="item.interestRate"
						hide-spin-buttons
						type="number"
						:label="$t('Groups.MT4.Crud.Common.annual-interest-rate')"
						suffix="%"
						:hint="$t('Groups.MT4.Crud.Common.daily-free-margin-credit-')"
					/>
				</v-col>
				<v-col offset-md="1" />
			</v-row>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const otpModeItems = enumToItems(Groups.MT4.OtpMode, 'Groups.MT4.OtpMode')

const computedRights = computed<typeof p.item.rights>({
	get: () => p.item.rights,
	set: (value: number) => {
		emit('update:item', {
			rights: value,
		})
	},
})

const { model: rightsModel } = useMultiSelectEnum(computedRights, Groups.MT4.GroupPermissionFlags)

const defaultLeverageItems = [
	{ title: '1:1000', value: 1000 },
	{ title: '1:500', value: 500 },
	{ title: '1:400', value: 400 },
	{ title: '1:300', value: 300 },
	{ title: '1:200', value: 200 },
	{ title: '1:175', value: 175 },
	{ title: '1:150', value: 150 },
	{ title: '1:125', value: 125 },
	{ title: '1:100', value: 100 },
	{ title: '1:75', value: 75 },
	{ title: '1:50', value: 50 },
	{ title: '1:33', value: 33 },
	{ title: '1:25', value: 25 },
	{ title: '1:20', value: 20 },
	{ title: '1:10', value: 10 },
	{ title: '1:5', value: 5 },
	{ title: '1:3', value: 3 },
	{ title: '1:2', value: 2 },
	{ title: '1:1', value: 1 },
]
</script>
