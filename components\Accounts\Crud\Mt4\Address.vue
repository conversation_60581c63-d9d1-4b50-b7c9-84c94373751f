<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.city"
					:label="$t('Accounts.MT4.Crud.Address.city')"
					:error-messages="errors.city"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<!-- <v-text-field v-model="item.state" :label="State" :error-messages="errors.state" /> -->
				<v-autocomplete
					v-model="item.country"
					auto-select-first
					:items="countriesItems"
					:error-messages="errors.country"
					:label="$t('Accounts.MT4.Crud.Address.country')"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.state"
					:label="$t('Accounts.MT4.Crud.Address.state')"
					:error-messages="errors.state"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.zipcode"
					:label="$t('Accounts.MT4.Crud.Address.zip-code')"
					:error-messages="errors.zipcode"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col cols="12">
				<v-text-field
					v-model="item.address"
					:label="$t('Accounts.MT4.Crud.Address.address')"
					:error-messages="errors.address"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
// import { AccountsTrades } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const { items: countriesItems } = useCountries({ value: 'name' })
</script>
