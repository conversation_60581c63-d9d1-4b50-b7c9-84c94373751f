import type { Socket } from 'socket.io-client'
import { WebSocketService } from './Base'
import type { ClientToServerEvents, ServerToClientEvents } from '~/types/Common/Socket.IO'

export type UpdateHandler = (data: SocketData) => void

export interface SocketData {
	[category: string]: Category
}

// export interface Category {
// 	serverId: number
// 	platform: number
// 	category: string
// 	group: string
// 	symbol: string
// 	totalVolumeBuy: number
// 	totalVolumeSell: number
// 	totalExposureLots: number
// 	totalExposureValue: number
// 	numberOfBuys: number
// 	numberOfSells: number
// 	buyVWAP: number
// 	sellVWAP: number
// 	buyVWAPNumerator: number
// 	sellVWAPNumerator: number
// 	currentPrice: number
// 	currentUnrealizedProfit: number
// 	totalClosedProfitForDay: number
// }

export interface Category {
	TotalVolumeBuy: number
	TotalVolumeSell: number
	TotalExposureLots: number
	TotalExposureValue: number
	CurrentUnrealizedProfit: number
	TotalClosedProfitForDay: number
	Ratio: number
}

export interface Response extends ServerToClientEvents {
	update: UpdateHandler
}

export interface Request extends ClientToServerEvents {}

export class ExposureReport extends WebSocketService<Socket<Response, Request>> {
	constructor() {
		super('exposure-report')
	}

	onUpdate(callback: UpdateHandler) {
		this.socket?.on('update', callback)
		return this
	}
}
