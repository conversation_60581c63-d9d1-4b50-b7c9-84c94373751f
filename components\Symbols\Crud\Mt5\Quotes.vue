<template>
	<v-container>
		<v-row no-gutters>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tickFlagsModel"
					:value="Symbols.MT5.TickFlags.TICK_REALTIME"
					color="primary"
					hide-details
					:label="$t('Symbols.MT5.Crud.Quotes.allow-real-time-quotes')"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tickFlagsModel"
					:value="Symbols.MT5.TickFlags.TICK_FEED_STATS"
					color="primary"
					hide-details
					:label="$t('Symbols.MT5.Crud.Quotes.receive-market-statistics')"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tickFlagsModel"
					:value="Symbols.MT5.TickFlags.TICK_NEGATIVE_PRICES"
					color="primary"
					hide-details
					:label="$t('Symbols.MT5.Crud.Quotes.allow-negative-quotes')"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tickFlagsModel"
					:value="Symbols.MT5.TickFlags.TICK_COLLECTRAW"
					color="primary"
					hide-details
					:label="$t('Symbols.MT5.Crud.Quotes.save-raw-prices')"
					class="compact"
				/>
			</v-col>
		</v-row>

		<fieldset class="mb-2">
			<legend>
				{{ $t('Symbols.MT5.Crud.Quotes.filtration') }}
			</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model.number="item.filterSoft"
						v-mask="Mask.Integer"
						:error-messages="errors.filterSoft"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.soft-filtration-level')"
						suffix="points"
						:hint="$t('Symbols.MT5.Crud.Quotes.must-not-be-less-than-4')"
					/>
					<v-text-field
						v-model.number="item.filterHard"
						v-mask="Mask.Integer"
						:error-messages="errors.filterHard"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.hard-filtration-level')"
						suffix="points"
						:hint="$t('Symbols.MT5.Crud.Quotes.must-not-be-less-than-soft')"
					/>
					<v-text-field
						v-model.number="item.filterDiscard"
						v-mask="Mask.Integer"
						:error-messages="errors.filterDiscard"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.discard-filtration-level')"
						suffix="points"
						:hint="$t('Symbols.MT5.Crud.Quotes.must-not-be-less-than-har')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						v-model.number="item.filterSoftTicks"
						:items="Array.from({ length: 10 }, (_, i) => i + 1)"
						:error-messages="errors.filterSoftTicks"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.filter')"
						suffix="points"
						:hint="$t('Symbols.MT5.Crud.Quotes.wrong-quotes-coming-one')"
					/>
					<v-select
						v-model.number="item.filterHardTicks"
						:items="Array.from({ length: 10 }, (_, i) => i + 1)"
						:error-messages="errors.filterHardTicks"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.filter')"
						suffix="points"
						:hint="$t('Symbols.MT5.Crud.Quotes.wrong-quotes-coming-one')"
					/>
				</v-col>
			</v-row>
		</fieldset>
		<fieldset>
			<legend>
				{{ $t('Symbols.MT5.Crud.Quotes.other') }}
			</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model.number="item.filterGap"
						v-mask="Mask.Integer"
						:error-messages="errors.filterGap"
						:rules="rules({ required: true })"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.gap-mode-level')"
						suffix="points"
						hint="0 = off"
					/>
					<v-text-field
						v-model.number="item.filterSpreadMin"
						v-mask="Mask.Integer"
						:error-messages="errors.filterSpreadMin"
						:rules="rules({ required: true })"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.minimum-spread')"
						suffix="ticks"
						hint="0 = off"
					/>
					<v-text-field
						v-model.number="item.subscriptionsDelay"
						v-mask="Mask.Integer"
						:error-messages="errors.subscriptionsDelay"
						:rules="rules({ required: true, type: 'number', min: 1, max: 60 })"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.delay-for-subscription')"
						suffix="minute"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model.number="item.filterGapTicks"
						v-mask="Mask.Integer"
						:error-messages="errors.filterGapTicks"
						:rules="rules({ required: true })"
						hint="0 = off"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.filter')"
						suffix="points"
					/>
					<v-text-field
						v-model.number="item.filterSpreadMax"
						v-mask="Mask.Integer"
						:error-messages="errors.filterSpreadMax"
						:rules="rules({ required: true })"
						hint="0 = off"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Quotes.maximum-spread')"
						suffix="ticks"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import type { Props, Emit } from './Shared'
import { defaults } from './Shared'
import { Symbols } from '~/types'
import { Mask } from '~/types/Mask'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const computedTickFlags = computed({
	get() {
		return p.item.tickFlags
	},
	set(value: number) {
		emit('update:item', { tickFlags: value })
	},
})

const { model: tickFlagsModel } = useMultiSelectEnum(computedTickFlags, Symbols.MT5.TickFlags)
</script>
