<template>
	<v-container fluid>
		<v-text-field
			v-model="item.name"
			:error-messages="errors.name"
			:label="$t('Accounts.MT4.Crud.Personal.name')"
			:rules="rules({ required: true })"
		/>
		<v-row v-if="!updateAction">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.password"
					:error-messages="errors.password"
					:label="$t('Accounts.MT4.Crud.Personal.password')"
					:rules="rules({ required: true, type: 'string', min: 8 })"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.passwordInvestor"
					:error-messages="errors.passwordInvestor"
					:label="$t('Accounts.MT4.Crud.Personal.investor-password')"
					:rules="rules({ required: true, type: 'string', min: 8 })"
				/>
			</v-col>
		</v-row>

		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.id"
					:error-messages="errors.id"
					:label="$t('Accounts.MT4.Crud.Personal.id-number')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-if="!updateAction"
					v-model.number="item.login"
					type="number"
					:items="[{ title: 'next', value: 0 }]"
					:error-messages="errors.login"
					:label="$t('Accounts.MT4.Crud.Personal.login')"
					:return-object="false"
					hide-spin-buttons
				/>
				<v-text-field
					v-else
					v-model="item.passwordPhone"
					:error-messages="errors.passwordPhone"
					:label="$t('Accounts.MT4.Crud.Personal.phone-password')"
				/>
			</v-col>
		</v-row>

		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model="item.status"
					:items="['RE', 'NR']"
					:error-messages="errors.status"
					:label="$t('Accounts.MT4.Crud.Personal.status')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<background-color
					v-model:background="item.userColor"
					:error-messages="errors.userColor"
					:label="$t('Accounts.MT4.Crud.Personal.color')"
				/>
			</v-col>
		</v-row>

		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<api-items
					v-slot="props"
					:url="Groups.Lookup.URL"
					:error-messages="errors.group"
				>
					<!-- @loaded="updateAction?void 0 :item.group = $event[0] || ''" -->
					<v-autocomplete
						v-model="item.group"
						:label="$t('Accounts.MT4.Crud.Personal.group')"
						v-bind="props"
						auto-select-first
						:rules="rules({ required: true })"
					/>
				</api-items>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.leverage"
					:error-messages="errors.leverage"
					:label="$t('Accounts.MT4.Crud.Personal.leverage')"
					:items="leverageItems"
				/>
			</v-col>
		</v-row>

		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.taxes"
					:error-messages="errors.taxes"
					:label="$t('Accounts.MT4.Crud.Personal.tax-rate')"
					suffix="%"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.agentAccount"
					:error-messages="errors.agentAccount"
					:label="$t('Accounts.MT4.Crud.Personal.agent-account')"
				/>
			</v-col>
		</v-row>

		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.leadSource"
					:error-messages="errors.leadSource"
					:label="$t('Accounts.MT4.Crud.Personal.lead-source')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-switch
					v-model="item.enable"
					hide-details
					color="success"
					:error-messages="errors.enable"
					:label="$t('Accounts.MT4.Crud.Personal.enable')"
					:true-value="1"
					:false-value="0"
				/>
			</v-col>
		</v-row>
		<v-textarea
			v-model="item.comment"
			rows="1"
			:error-messages="errors.comment"
			:label="$t('Accounts.MT4.Crud.Personal.comment')"
		/>
		<v-defaults-provider :defaults="{ VCheckbox: { trueValue: 1, falseValue: 0 } }">
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.enableReadOnly"
						:error-messages="errors.enableReadOnly"
						:label="$t('Accounts.MT4.Crud.Personal.read-only')"
						color="primary"
						class="compact"
						hide-details
					/>
					<v-checkbox
						v-model="item.enableChangePassword"
						:error-messages="errors.enableChangePassword"
						:label="$t('Accounts.MT4.Crud.Personal.allow-password-change')"
						color="primary"
						class="compact"
						hide-details
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.sendReports"
						hide-details
						color="primary"
						class="compact"
						:label="$t('Accounts.MT4.Crud.Personal.send-reports')"
					/>
					<v-checkbox
						v-model="item.enableOtp"
						hide-details
						color="primary"
						class="compact"
						:label="$t('Accounts.MT4.Crud.Personal.one-time-password')"
					/>
				</v-col>
			</v-row>
			<v-checkbox
				v-if="updateAction"
				v-model="item.enableFlags"
				:true-value="Accounts.MT4.EnUserFlags.USER_FLAGS_SPONSORED_HOSTING"
				:false-value="Accounts.MT4.EnUserFlags.USER_FLAGS_NONE"
				:error-messages="errors.enableFlags"
				:label="$t('Accounts.MT4.Crud.Personal.sponsored-mt-virtual-host')"
				color="primary"
				class="compact"
				hide-details
			/>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-unused-vars */

import type { VCheckbox } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts, Groups } from '~/types'
// import { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const leverageItems = [
	{ title: '1:1000', value: 1000 },
	{ title: '1:500', value: 500 },
	{ title: '1:400', value: 400 },
	{ title: '1:300', value: 300 },
	{ title: '1:200', value: 200 },
	{ title: '1:175', value: 175 },
	{ title: '1:150', value: 150 },
	{ title: '1:125', value: 125 },
	{ title: '1:100', value: 100 },
	{ title: '1:75', value: 75 },
	{ title: '1:50', value: 50 },
	{ title: '1:33', value: 33 },
	{ title: '1:25', value: 25 },
	{ title: '1:20', value: 20 },
	{ title: '1:10', value: 10 },
	{ title: '1:5', value: 5 },
	{ title: '1:3', value: 3 },
	{ title: '1:2', value: 2 },
	{ title: '1:1', value: 1 },
]
</script>
