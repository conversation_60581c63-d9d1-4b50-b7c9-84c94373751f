import { defineStore } from 'pinia'
import { initializeApp } from 'firebase/app'
import { getMessaging, getToken, onMessage, type Messaging, isSupported } from 'firebase/messaging'
import type { FirebaseOptions, FirebaseApp } from 'firebase/app'

declare module '#GlobalEvents' {
	interface Events {
		'notification:received': any
	}
}
interface State {
	firebaseOptions: FirebaseOptions
	vapidKey: string
	app: FirebaseApp
	messaging: Messaging
	token: string
	isPermitted: boolean
}

const log = useLog('FCM', {
	namespaceStyle: {
		backgroundColor: '#FF5722',
	},
})

export const useFcmStore = defineStore({
	id: 'fcmStore',
	state: (): State => ({
		firebaseOptions: {
			apiKey: '',
			authDomain: '',
			projectId: '',
			storageBucket: '',
			messagingSenderId: '',
			appId: '',
		},
		vapidKey: '',
		app: {} as FirebaseApp,
		messaging: {} as Messaging,
		token: useLocalStorage('fcmToken', '').value,
		isPermitted: false,
	}),
	actions: {
		init() {
			const { fcm } = useRuntimeConfig().public

			if (!isSupported()) {
				log('Firebase Cloud Messaging is not supported in this browser.')
				return
			} else {
				log('Firebase Cloud Messaging is supported in this browser.')
			}

			this.firebaseOptions = fcm.firebaseOptions

			this.vapidKey = fcm.vapidKey

			this.app = initializeApp(fcm.firebaseOptions)

			// Initialize Firebase Cloud Messaging and get a reference to the service
			this.messaging = getMessaging(this.app)

			this.startListening()

			this.getToken()
		},
		startListening() {
			onMessage(this.messaging, (payload) => {
				log('Message received. ', payload)

				// show native browser notification
				const notification = new Notification(payload.notification?.title as string, {
					badge: '',
					icon: '/images/logo/notification.png',
					...payload.notification,
				})
				notification.onclick = (event) => {
					log('Notification clicked.', event)
					const data = payload.data

					if (data?.url) {
						if (data.url.startsWith('http')) {
							window.open(data.url)
						} else {
							const router = useRouter()
							router.push(data.url)
						}
					}
					// // Perform action based on action identifier
					// if (event.action === 'open-url') {
					// 	window.open(data.url)
					// } else if (event.action === 'dismiss') {
					// 	// Dismiss action: No further action needed
					// }
				}
			})

			const { emit } = useGlobalEvents()

			if ('serviceWorker' in navigator) {
				navigator.serviceWorker.register('/firebase-messaging-sw.js')
					.then((registration) => {
						log('Service Worker registered with scope:', registration.scope)

						// Listen for incoming messages from the Service Worker
						navigator.serviceWorker.addEventListener('message', (event) => {
							const data = event.data
							if (data.type === 'UPDATE') {
								log('Received a message from Service Worker:', data.message)
								switch (data.message) {
									case 'MESSAGE_RECEIVED':
										emit('notification:received', data.payload)
										break
									default:
										break
								}
							}
						})
					})
					.catch((error) => {
						console.error('Service Worker registration failed:', error)
					})
			}

			return this
		},

		getToken() {
			return getToken(this.messaging, { vapidKey: this.vapidKey }).then((currentToken: string | null) => {
				if (currentToken) {
					this.isPermitted = true
					if (currentToken !== this.token) {
						log('currentToken:', currentToken)
						this.token = currentToken

						// Save token to storage
						useLocalStorage('fcmToken', currentToken).value = currentToken

						this.syncToken()
					}
				} else {
					this.isPermitted = false
					// Show permission request UI
					log('No registration token available. Request permission to generate one.')
				}
			}).catch((err: any) => {
				console.error('An error occurred while retrieving token. ', err)
			})
		},
		syncToken() {
			log('Syncing token with backend')
		},
	},
})
