<template>
	<v-container>
		<v-list>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.registration-date') }}</b>
				<template #append>
					{{ dateTime(item.registrationDate) }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.last-seen-online') }}</b>
				<template #append>
					{{ dateTime(item.lastConnectionDate) }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.last-ip') }}</b>
				<template #append>
					<template v-if=" item.lastIp">
						{{ intToIP(item.lastIp) }}
					</template>
					<template v-else>
						{{ $t('Accounts.MT4.Crud.Info.not-available') }}
					</template>
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.balance') }}</b>
				<template #append>
					{{ item.balance }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.previous-balance') }}</b>
				<template #append>
					{{ item.previousBalance }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.previous-equity') }}</b>
				<template #append>
					{{ item.previousEquity }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.previous-month-equity') }}</b>
				<template #append>
					{{ item.previousMonthEquity }}
				</template>
			</v-list-item>
			<v-list-item>
				<b>{{ $t('Accounts.MT4.Crud.Info.meta-quote-id') }}</b>
				<template #append>
					{{ item.mqid }}
				</template>
			</v-list-item>
		</v-list>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
// import { AccountsTrades } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const intToIP = (int: number) => {
	return (
		((int >>> 24) & 0xFF) + '.'
		+ ((int >>> 16) & 0xFF) + '.'
		+ ((int >>> 8) & 0xFF) + '.'
		+ (int & 0xFF)
	)
}
</script>
