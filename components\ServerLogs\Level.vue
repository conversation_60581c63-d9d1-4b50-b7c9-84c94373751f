<template>
	<v-chip
		:text="text"
		size="small"
		:color="color"
		rounded="lg"
	/>
</template>

<script lang="ts" setup>
const p = defineProps<{
	level: 0 | 1 | 2 | 3 | 4 | 5
}>()

const text = computed(() => {
	switch (p.level) {
		case 0:
			return 'Verbose'
		case 1:
			return 'Debug'
		case 2:
			return 'Information'
		case 3:
			return 'Warning'
		case 4:
			return 'Error'
		case 5:
			return 'Fatal'
		default:
			return 'Unknown'
	}
})

const color = computed(() => {
	switch (p.level) {
		case 0:
			return 'pink'
		case 1:
			return 'green'
		case 2:
			return 'info'
		case 3:
			return 'orange'
		case 4:
			return 'error'
		case 5:
			return 'red'
		default:
			return 'grey'
	}
})
</script>

<style>

</style>
