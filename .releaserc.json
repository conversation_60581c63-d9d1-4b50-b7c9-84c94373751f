{"branches": ["+([0-9])?(.{+([0-9]),x}).x", "main", {"name": "develop", "prerelease": "dev"}, {"name": "uat", "prerelease": "uat"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json", ".output/**/*", "dist/**/*"], "message": "chore(release): ${nextRelease.version} [skip ci]:\n\n${nextRelease.notes}"}], "@semantic-release/github", ["@semantic-release/exec", {"prepareCmd": "npm run generate"}]]}