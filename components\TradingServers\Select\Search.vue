<template>
	<api-items
		v-slot="{ items, loading, error, errorMessages }"
		:url="Groups.Lookup.URL"
		:api-options="{
			query: {
				serverName: item.name,
			},
		}"
	>
		<v-autocomplete
			ref="autocompleteRef"
			v-model:search="searchModel"
			:model-value="model"
			class="hide-selection"
			:label="$t('TradingServerSelectSearch.search-for-group')"
			multiple
			menu-icon=""
			:loading="loading"
			single-line
			:items="items"
			:error="error"
			:error-messages="errorMessages"
			density="compact"
			hide-details="auto"
			auto-select-first
			:append-inner-icon="searchModel?'i-mdi:keyboard-esc':'i-mdi:magnify'"
			:disabled="p.disabled"
			@click:append-inner="searchModel = undefined"
			@keydown.esc.stop="searchModel = ''"
			@update:model-value="selectGroup"
		>
			<template #prepend-item>
				<v-sheet
					color="surface"
					class="position-sticky"
					style="z-index: 1;top: -8px;"
				>
					<div class="d-flex ">
						<v-btn
							variant="text"
							tile

							rounded="0"
							width="50%"
							:disabled="isAllFilteredSelected"
							prepend-icon="i-mdi:select-all"
							@click="selectAllFiltered"
						>
							{{ $t('TradingServerSelectSearch.select-all') }}
						</v-btn>
						<v-divider
							vertical
							inset
						/>
						<v-btn
							variant="text"
							tile
							rounded="0"

							width="50%"
							:disabled="!model.length"
							prepend-icon="i-mdi:select-off"
							@click="deselectAllFiltered"
						>
							{{ $t('TradingServerSelectSearch.deselect-all') }}
						</v-btn>
					</div>
					<v-divider />
				</v-sheet>
			</template>
		</v-autocomplete>
	</api-items>
</template>

<script lang="ts" setup>
// import { refDebounced } from '@vueuse/core'
import type { VAutocomplete } from 'vuetify/components'
import { Groups, type TradingServers } from '~/types'

const selection = ref<string | null>(null)
const searchModel = ref<string | undefined>()
// const debouncedSearchModel = refDebounced<string | undefined>(searchModel, 400)

type Props = {
	disabled?: boolean
	modelValue: string[]
	item: TradingServers.Lookup.SingleRecord
}

const p = withDefaults(defineProps<Props>(), {
	disabled: false,
})

const autocompleteRef = ref<InstanceType<typeof VAutocomplete> | null>(null)

const emit = defineEmits(['update:modelValue'])

const selectGroup = (groups: any[]) => {
	if (!groups) {
		return
	}
	model.value = groups
	nextTick(() => {
		selection.value = null
		// searchModel.value = ''
	})
}

const model = computed({
	get: () => p.modelValue,
	set: (value: string[]) => emit('update:modelValue', value),
})

const filteredItems = computed(() => autocompleteRef.value?.filteredItems || [])

const isAllFilteredSelected = computed(() =>
	filteredItems.value.every((item: any) => model.value.includes(item.value)),
)

const selectAllFiltered = () => {
	const filtered = useCloned(filteredItems.value).cloned.value

	const selected = filtered?.map((item: any) => item.value)

	selectGroup(selected)
}

const deselectAllFiltered = () => {
	selectGroup([])
}
</script>

<style lang="scss">
.hide-selection {

	.v-select__selection,
	.v-autocomplete__selection {
		display: none;
	}

	.v-field {
		&.v-field--active {
			.v-label.v-field-label {
				visibility: visible !important;
			}

		}

		&.v-field--focused {
			.v-label.v-field-label {
				visibility: hidden !important;
			}
		}
	}

}
</style>
