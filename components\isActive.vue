<template>
	<v-chip v-bind="{ ...chipProps, ...$attrs }" />
</template>

<script lang="ts" setup>
import { VChip } from 'vuetify/components'

const { t } = useI18n()
const p = defineProps({
	modelValue: {
		type: Boolean,
		required: true,
	},
})

const chipProps = computed<InstanceType<typeof VChip>['$props']>(() => ({
	color: p.modelValue ? 'active' : 'inactive',
	text: p.modelValue ? t('isActive.active') : t('isActive.inactive'),
	rounded: true,
}))
</script>
