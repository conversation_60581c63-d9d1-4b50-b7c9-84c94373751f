{"isActive": {"active": "Active", "inactive": "In Active"}, "crud": {"error-while-getting": "Error while getting data", "cancel": "Cancel", "retry": "Retry", "save": "Save", "create-item": "Create {itemName}", "update-item": "Update {itemName}", "item-deleted": "{name} has been deleted successfully", "item-created": "{name} has been created successfully", "item-updated": "{name} has been updated successfully", "reset": "Reset", "are-you-sure-you-want-to-reset-the-form": "Are you sure you want to reset the form?", "update-item-identity": "Update {itemName} \"{itemIdentity}\""}, "breadcrumb": {"home": "Home", "users": "Users", "roles": "Roles", "trading-servers": "Trading Servers", "actions": "Actions", "manage-access": "Manage Access", "reports": "Reports", "prices": "Historical Prices", "deals": "Historical Deals", "currencies": "Currencies", "action-types": "Action Types", "operations": "Operations", "transactions": "Transactions", "my": "My", "profile": "Profile", "online-traders": "Online Traders", "bid-ask-last-ticks": "Bid/Ask/Last Ticks", "1-min-history": "Prices History", "security": "Security", "transactions-workflow": "Transactions Workflow", "journals": "Journals", "symbols": "Symbols", "groups": "Groups", "accounts": "Accounts", "manager": "Manager", "trading": "Trading", "hedging": "Hedging", "server-logs": "Server Logs", "symbols-categories": "Symbols Categories", "alerts": "<PERSON><PERSON><PERSON>", "exposure": "Exposure Report"}, "api-items": {"request-error": "Error while getting data"}, "default": {"main": "Main", "dashboard": "Dashboard", "transactions": "Transactions", "reports": "Reports", "prices": "Prices", "deals": "Deals", "online-traders": "Online Traders", "1-min-history": "Prices History", "bid-ask-last-ticks": "Bid/Ask/Last Ticks", "journals": "Journals", "currencies": "Currencies", "content-management": "Content Management", "transactions-workflow": "Transactions Workflow", "users": "Users", "trading-servers": "Trading Servers", "symbols": "Symbols", "groups": "Groups", "accounts": "Accounts", "trading-accounts": "Trading", "manager-accounts": "Manager"}, "TradingServerSelectGroups": {"there-is-no-any-group-yet": "There is no any group yet"}, "TradingServerSelectPanel": {"all-groups-selected": "All Groups Selected", "groups-selected": "{0} Groups Selected", "groups-selected-length": "{0} Groups Selected", "no-group-selected": "No Group Selected", "allow-for-all-groups": "Allow for all groups"}, "TradingServerSelectSearch": {"search-for-group": "Search for group...", "select-all": "Select All", "deselect-all": "Deselect All"}, "ActionsCrud": {"action-type": "Action Type", "add-new-action-type": "Add New Action Type", "description": "Description", "comment": "Comment", "only-receiver": "Sender / Receiver", "display-name": "Display Name"}, "CreateTransactionCrud": {"operation": "Operation", "action-type": "Action Type", "action": "Action", "from-account": "From Account", "start-typing-to-search": "Start typing to search...", "amount": "Amount", "start-typing-to-search-0": "Start typing to search...", "to-account": "To Account"}, "Crud": {"you-have-unsaved-changes-are-you-sure-you-want-to-": "You have unsaved changes. Are you sure you want to close?", "are-you-sure-you-want-to-delete": "Are you sure you want to delete this {itemName}?", "are-you-sure-you-want-to-delete-identity": "Are you sure you want to delete \"{itemIdentity}\" {itemName}?"}, "CurrenciesCrud": {"symbol": "Symbol", "name": "Name"}, "Datatable": {"end-of-data": "End of Data", "load-more": "Load More ...", "never": "Never", "updated-at": "Updated At:", "created-at": "Created At:", "last-updated-since-item-updatedat": "Last updated {0}", "created-since-item-createdat": "Created {0}", "apply": "Apply", "reset": "Reset", "filter": "Filter", "search": "Search", "reset-to-default": "Reset to De<PERSON>ult", "columns": "Column's"}, "DateTimeInput": {"now": "Now", "close": "Close"}, "DealsMinMaxPrice": {"error": "Error"}, "DropZone": {"drag-and-drop-excel-file-here": "Drag & Drop Excel File Here", "and-drop-excel-file-here": "& Drop Excel File Here", "or": "Or", "click-to-upload": "Click to Upload"}, "OperationCrud": {"name": "Name", "display-name": "Display Name", "trading-platform": "Trading Platform", "mt-reference-type": "Mt Reference Type"}, "RolesCrud": {"descriptions": "Descriptions", "display-name": "Display Name", "permissions": "Permissions", "information": "Information", "role": "Role"}, "RolesPanels": {"delete": "Delete", "cancel": "Cancel", "save": "Save", "there-is-no-any-data": "There is no any data", "are-you-sure-you-want-to-remove": "Are you sure you want to remove this role?"}, "SelectCurrentTradingServer": {"server-item-displayname": "{0} Server"}, "SelectPermissions": {"permissions": "Permissions", "deselect-all": "Deselect All", "select-all": "Select All"}, "SettingsBtn": {"bak": {"dark-theme": "Dark Theme", "language": "Language", "server": "Server"}}, "TradingServersCrud": {"name": "Name", "display-name": "Display Name", "trading-platform": "Trading Platform", "manager-account": "Manager Account", "rest-api": "Rest API", "ip-address": "IP Address", "port": "Port", "login": "<PERSON><PERSON>", "url": "URL", "leave-empty-for-no-change": "Leave empty for no changes", "password": "Password", "is-active": "Is Active"}, "TransactionsStatusLabel": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "unknown": "Unknown"}, "TwoFactorWarningBanner": {"your-account-is-insecure": "Your Account is Insecure", "you-have-not-enabled-2-factors-authentication-plea": "You have not enabled 2 Factors Authentication. Please enable it to secure your account.", "enable-2fa": "Enable 2FA"}, "UserBtn": {"settings": "Settings", "dark-theme": "Dark Theme", "language": "Language", "server": "Server", "account-security": "Account Security", "logout": "Logout", "about": "About", "change-password": "Change Password"}, "UsersCrud": {"role": "Role", "add-new-role": "Add New Role", "enable-2fa": "Enable 2FA", "user-name": "User Name", "email": "Email", "do-you-want-to-replace-item-name-permissions-with-": "Do you want to replace \"{0}\" permissions with the new role permissions?", "no": "No", "yes": "Yes", "user": "User", "information": "Information", "meta-servers": "Meta Servers", "permissions": "Permissions", "whitelist-ips": "Restriction", "restrict-login-to-ips": "Restrict Login to IPs", "user-can-login-from-below": "User can login from the below IP addresses only. Leave empty to allow all IPs.", "ip-address": "IP Address", "insert": "Insert", "whitelisted-ips": "Whitelisted IPs", "no-whitelisted-ips": "No IPs added. No restriction"}, "ViewTransactionCrud": {"view-transaction": "View Transaction", "cancel": "Cancel", "reject": "Reject", "approve": "Approve", "actiontitle-transaction": "{0} Transaction", "comment": "Comment", "please-enter-your-comment": "Please enter your comment", "cancel-0": "Cancel"}, "WorkflowEvent": {"awaiting-for": "Awaiting for", "user-item-username": "user \"{0}\"", "a-user-with-role-item-rolename": "a user with role \"{0}\"", "by-item-username": "By {0}"}, "WorkflowEventLabel": {"fallback": "Fallback", "approval": "Approval", "reviewal": "Reviewal", "request": "Request", "unknown": "Unknown"}, "2fa": {"two-factors-authentications": "Two Factors Authentications", "please-enter-the-6-digit-generated-by-your-authent": "Please enter the 6-digit generated by your authenticator app.", "verify": "Verify", "login-with-a-different-account": "Login with a different Account"}, "logging": {"logging": "Logging ...", "please-choose-a-trading-server-to-continue": "Please choose a trading server to continue", "select-server": "Select Server", "getting-profile-data": "Getting Profile Data...", "please-try-to-login-again": "Please try to login again.", "an-error-occurred-while-getting-user-credentials-p": "An error occurred while getting user credentials/profile", "getting-preferences-data": "Getting Preferences Data...", "an-error-occurred-while-getting-user-preferences": "An error occurred while getting user preferences"}, "login": {"login": "Account <PERSON>gin", "enter-your-email-address": "Enter your email address", "email": "Email", "password": "Password", "enter-your-password": "Enter your password", "are-you-sure-you-want-to-delete-this-history": "Are you sure you want to delete this history item?", "login-with-a-different-acccount": "Login with a different account", "delete-item": "Delete item", "login-with-saved-account": "Login with Saved Account", "forgot-your-password": "Forgot your password?"}, "currencies": {"currencies": "Currencies", "create-currency": "Create C<PERSON><PERSON>cy", "name": "Name", "symbol": "Symbol", "date": "Date"}, "security": {"code-has-been-copied": "Code has been copied.", "2-factors-authentication": "2 Factors Authentication", "you-will-need-to-install": "You will need to install", "scan-the-qr-code-below-then-enter-the-code-generat": "Scan the QR code below. Then enter the code generated by the app.", "on-your-phone-to-use-2fa": "on your phone to use 2FA.", "microsoft-authentication-app": "Microsoft Authentication App", "cancel": "Cancel", "continue-setup": "Continue Setup", "your-account-is-secured": "Your Account is Secured", "two-factor-authentication-2fa-is-currently-enabled": "Two-factor authentication (2FA) is currently enabled on your account for added security. You will need to enter a code from your authenticator app to disable 2FA.", "cancel-0": "Cancel", "disable-2fa": "Disable 2FA", "to-disable-2fa-enter-the-code-from-your-authentica": "To disable 2<PERSON>, enter the code from your authenticator app.", "cancel-1": "Cancel", "disable-2fa-0": "Disable 2FA", "2fa-has-been-enabled": "2FA has been enabled.", "2fa-has-been-disabled": "2<PERSON> has been disabled.", "or": "Or"}, "bid-ask-last-ticks": {"bid-ask-last-ticks": "Bid/Ask/Last Ticks", "export-data": "Export Data", "from": "From", "symbol": "Symbol", "to": "To", "exporting-data": "Exporting data...", "the-data-is-being-exported": "The data is being exported. You will be notified by email when it is ready.", "ask-price": "Ask Price", "bid-price": "<PERSON><PERSON>", "time": "Date (MT Timezone)", "use-mt-timezone": "Use MT Timezone"}, "deals": {"historical-deals": "Historical Deals", "export-data": "Export Data", "login-number": "Login Number", "symbol": "Symbol", "from-date": "From Date", "to-date": "To Date", "position-id": "Position ID", "login": "<PERSON><PERSON>", "action": "Action", "open-price": "Open Price", "close-price": "Close Price", "min-price": "<PERSON>", "max-price": "Max Price", "min-price-time": "Min Price Time", "max-price-time": "Max Price Time", "open-time": "Open Time", "close-time": "Close Time", "profit": "Profit", "exporting-data": "Exporting data...", "the-data-is-being-exported": "The data is being exported. You will be notified by email when it is ready.", "volume": "Volume", "control-the-number-description": "Control the number of records to be exported in the excel file. Exporting time will increase with the number of records.", "limited-records": "Limited Records", "number-of-exported-record": "Number of Exported Records", "all-records": "All Records", "exporting-all-records-warning": "Exporting all records may take a long time.", "cancel": "Cancel", "export": "Export"}, "online-traders": {"online-traders": "Online Traders", "online-traders-users-length": "Online Traders: {0}", "offline": "Offline", "reconnecting-connection-attempts": "Reconnecting... ({0})", "search": "Search", "multi-sessions-country-mismatch": "Multi Sessions + Country Mismatch", "multi-sessions": "Multi Sessions", "country-mismatch": "Country Mismatch", "single-session": "Single Session", "block-session": "Block Session", "you-are-about-to-block-this-session": "You are about to block this session:", "ip-address": "IP Address:", "comment": "Comment", "reason-for-blocking": "Reason for blocking ...", "cancel": "Cancel", "block": "Block", "user": "User:", "country": "Country:", "copied-to-clipboard": "Copied User {0} to clipboard", "login": "<PERSON><PERSON>", "iP-Country": "Country", "mt-acc-country": "MT Acc. Country", "group": "Group", "sessions": "Sessions", "time": "Time", "blocked": "Session Blocked", "the-session-has-been-blocked": "The session has been blocked successfully", "username": "Name"}, "prices-id": {"date": "Date", "symbol": "Symbol", "price-minus3-minutes-high-ask": "-3 <PERSON> High Ask", "price-minus3-minutes-low-bid": "-3 <PERSON>id", "price-minus1-minute-high-ask": "-1 Min High Ask", "price-minus1-minute-low-bid": "-1 <PERSON>id", "price-minus3-seconds-high-ask": "-3 Sec High Ask", "price-minus3-seconds-low-bid": "-3 Sec Low Bid", "price-plus3-seconds-high-ask": "+3 Sec High Ask", "price-plus3-seconds-low-bid": "+3 Sec Low Bid", "price-plus1-minutes-high-ask": "+1 <PERSON>", "price-plus1-minutes-low-bid": "+1 Min <PERSON>", "price-plus3-minutes-high-ask": "+3 Min <PERSON>", "price-plus3-minutes-low-bid": "+3 Min <PERSON>id", "lowest-price-minus6-minutes": "Lowest in -6 Min", "highest-price-minus6-minutes": "Highest in -6 Min", "lowest-price-plus6-minutes": "Lowest in +6 Min", "highest-price-plus6-minutes": "Highest in +6 Min", "report-details": "Report Details", "download-report": "Download Report", "upload-another-file": "Upload Another File"}, "prices": {"download-template": "Download Template", "how-to-get-the-historical-prices-of-a-stock": "How to get the historical prices of a stock", "download-the-template-file-from-upper-right-corner": "Download the template file from upper right corner.", "fill-the-template-with-the-stock-symbols-you-want-": "Fill the template with the stock symbols you want to get the historical prices of.", "upload-the-filled-template-file": "Upload the filled template file.", "you-will-be-redirected-to-the-report-page": "You will be redirected to the report page", "historical-prices": "Historical Prices"}, "trading-servers": {"trading-servers": "Trading Servers", "create-server": "Create Server", "rest-api": "Rest API", "manager-account": "Manager Account", "display-name": "Display Name", "integration-type": "Integration Type", "address": "IP Address / URL", "trading-platform": "Trading Platform", "is-active": "Status"}, "transactions": {"transactions": "Transactions", "create-transaction": "Create Transaction", "users": "Users", "actions": "Actions", "start-typing-to-search": "Start typing to search...", "to-account": "To Account", "status": "Status", "from-account": "From Account", "action": "Action", "operation-name": "Operation", "trading-server": "Trading Server", "sender-account": "Sender Account", "receiver-account": "Receiver Account", "requested-by": "Requested By", "requested-amount": "Requested Amount", "received-amount": "Received Amount", "usd-amount": "USD Amount", "executed-by": "Executed By", "executed-at": "Executed At", "timestamp": "Date"}, "manage-access": {"manage-access": "Manage Access", "allowed-roles": "Allowed Roles", "allowed-roles-are-roles-that-are-allowed-to-access": "Allowed roles are roles that are allowed to access/request the action.", "roles": "Roles", "select-role": "Select Role", "add-allow-role": "Add Allow <PERSON>", "review-roles": "Review Roles", "review-roles-description": "Review roles are roles that are allowed to review the action request before it is approved.", "add-review-role": "Add Review Role", "approval-roles": "Approval Roles", "approval-roles-description": "Approval roles are roles that are allowed to approve the action request.", "limit": "Limit:", "daily": "Daily:", "monthly": "Monthly:", "yearly": "Yearly:", "transaction-amount-limit": "Transaction Amount Limit", "transaction-daily-limit": "Transaction Daily Limit", "transaction-monthly-limit": "Transaction Monthly Limit", "transaction-yearly-limit": "Transaction Yearly Limit", "add-approval-role": "Add Approval Role", "limitation-by-user": "Limitation by User", "limitation-by-user-description": "You can limit the approval by user. If you do not limit the approval by user, the approval will be available to all users with the approval role.", "add-user": "Add User", "fallback-role": "Fallback Role", "fallback-role-description": "Fallback role is the role that will be used when the user does not have any of the review or approval roles , and there is an error in the system.", "cancel": "Cancel", "save": "Save", "select-user": "Select user", "already-in-use": "Already in use", "workflow-has-been-saved-successfully": "Workflow has been saved successfully", "an-error-occurred-while-saving-the-workflow": "An error occurred while saving the workflow"}, "operations": {"operations": "Operations", "create-operation": "Create operation", "display-name": "Display Name", "type": "Type", "trading-platform": "Trading Platform"}, "action-types": {"action-types": "Action Types", "operations": "Operations", "create-action-type": "Create Action Type", "in": "In", "out": "Out", "display-name": "Display Name", "operation": "Operation", "direction": "Direction"}, "actions": {"actions": "Actions", "action-types": "Action Types", "create-action": "Create Action", "comment": "Comment", "action-type": "Action Type", "name": "Name:", "display-name": "Display Name:", "direction": "Direction:", "edit": "Edit", "display-name-0": "Display Name", "description": "Description", "only-receiver": "Sender/Receiver", "date": "Date"}, "roles": {"roles": "Roles", "create-role": "Create Role", "display-name": "Display Name", "description": "Description", "date": "Date", "name": "Name"}, "users": {"users": "Users", "roles": "Roles", "create-user": "Create User", "email": "Email", "role": "Role", "name": "Name", "date": "Date"}, "dashboard": {"dashboard": "Dashboard", "work-in-progress": "Work in Progress", "add-widget": "Add Widget", "you-dont-have-any-widget-yet": "You don't have any widget yet", "enable-widgets-editing": "Enable Widgets Editing", "save": "Save", "cancel": "Cancel", "welcome-back": "Welcome Back:"}, "widget-online-traders": {"online-traders": "Online Traders", "data-total-sessions": "{0} Sessions"}, "journals": {"user": "User", "from": "From", "to": "To", "ip-address": "IP Address"}, "UserInfoTooltip": {"edit-user": "Edit User", "name": "Name:", "email": "Email:", "role": "Role:"}, "rules": {"field": "Field", "default": "Validation error on field {field}", "required": "{field} is required", "enum": "{field} must be one of {values}", "whitespace": "{field} cannot be empty", "date": {"format": "{field} date {value} is invalid for format {format}", "parse": "{field} date could not be parsed, {value} is invalid ", "invalid": "{field} date {value} is invalid"}, "types": {"string": "{field} is not a {type}", "method": "{field} is not a {type} (function)", "array": "{field} is not an {type}", "object": "{field} is not an {type}", "number": "{field} is not a {type}", "date": "{field} is not a {type}", "boolean": "{field} is not a {type}", "integer": "{field} is not an {type}", "float": "{field} is not a {type}", "regexp": "{field} is not a valid {type}", "email": "{field} is not a valid {type}", "url": "{field} is not a valid {type}", "hex": "{field} is not a valid {type}"}, "string": {"len": "{field} must be exactly {len} characters", "min": "{field} must be at least {min} characters", "max": "{field} cannot be longer than {max} characters", "range": "{field} must be between {min} and {max} characters"}, "number": {"len": "{field} must equal {len}", "min": "{field} cannot be less than {min}", "max": "{field} cannot be greater than {max}", "range": "{field} must be between {min} and {max}"}, "array": {"len": "{field} must be exactly {len} in length", "min": "{field} cannot be less than {min} in length", "max": "{field} cannot be greater than {max} in length", "range": "{field} must be between {min} and {max} in length"}, "pattern": {"mismatch": "{field} value {value} does not match pattern {pattern}"}}, "symbols": {"symbol": "Symbol", "path": "Path", "execution": "Execution", "volume-min": "Min. Volume", "volume-max": "Max. Volume", "symbols": "Symbols", "create-symbol": "Create Symbol", "filter": "Filter", "spread": "Spread", "stops": "Stops", "trade": "Trade", "find-type": "Find Type", "expand-all": "Expand All", "collapse-all": "Collapse All", "add-root-directory": "Add Root Directory", "no-path-found": "No path found matching \"{0}\"", "add-directory": "Add Directory", "directory-name": "Directory Name", "cancel": "Cancel", "save": "Save", "are-you-sure-you-want-to-delete": "Are you sure you want to delete this directory with ({0}) Symbol?", "failed-to-delete-directory": "Failed to delete directory"}, "auth": {"copyrights": "INGOT Brokers Pilot {symbol} UI: v{version} All rights reserved"}, "Symbols": {"MT5": {"Crud": {"create-item-symbol": "Create \"{0}\" {1}", "update-item-symbol-itemna": "Update \"{0}\" {1}", "Common": {"symbol": "Symbol", "exchange": "Exchange", "basis": "<PERSON><PERSON>", "source": "Source", "background": "Background", "digits": "Digits", "spread": "Spread", "spread-balance": "Spread Balance", "item-spreadbalance-spread": "{0} Bid / {1} Ask", "path": "Path", "description": "Description", "international": "International", "sector": "Sector", "industry": "Industry", "country": "Country", "category": "Category", "page": "Page", "market-depth": "Market Depth", "chart-mode": "Chart Mode", "off": "off", "loaded-symbol": "Changing Symbol name will create a new symbol"}, "Currency": {"base": "Base", "base-currency": "Base currency", "base-currency-digits": "Base currency digits", "profit": "Profit", "profit-currency": "Profit currency", "profit-currency-digits": "Profit currency digits", "margin": "<PERSON><PERSON>", "margin-currency": "Margin currency", "margin-currency-digits": "Margin currency digits"}, "Execution": {"settings": "Settings", "max-time-deviation": "Max. Time Deviation", "max-profit-deviation": "Max. Profit Deviation", "max-losing-deviation": "<PERSON><PERSON> Losing Deviation", "max-volume": "Max. Volume", "timeout": "Timeout", "confirm-orders": "Confirm Orders", "fast-confirmation": "Fast Confirmation of requites within client Deviation", "execution": "Execution"}, "Margin": {"margin-values": "Margin Values", "initial-margin": "Initial Margin", "hedged-margin": "Hedged <PERSON><PERSON>", "maintenance-margin": "Maintenance Margin", "settings": "Settings", "calculate-hedged-margin-u": "Calculate hedged margin using larger leg", "exclude-long-position-pnl": "Exclude long position PnL from free margin and margin level", "additional-margin-checks": "Additional margin checks", "recalculate-margin": "Recalculate margin exchange rate at the end of day"}, "MarginRates": {"liquidity-margin-rate": "Liquidity Margin Rate", "currency-margin-rate": "<PERSON><PERSON><PERSON><PERSON>", "margin-rates": "Margin Rates", "market-order": "Market Order", "limit-order": "Limit Order", "stop-order": "Stop Order", "stop-limit-order": "Stop Limit Order", "initial-margin": "Initial Margin", "buy": "Buy", "sell": "<PERSON>ll", "maintenance-margin": "Maintenance Margin"}, "Quotes": {"allow-real-time-quotes": "Allow real time quotes from data feeds", "receive-market-statistics": "Receive market statistics from data feeds", "allow-negative-quotes": "Allow negative quotes", "save-raw-prices": "Save raw prices", "filtration": "Filtration", "soft-filtration-level": "Soft filtration level", "must-not-be-less-than-4": "Must not be less than 4 or 5 times spread by default", "hard-filtration-level": "Hard filtration level", "must-not-be-less-than-soft": "Must not be less than soft filtration level", "discard-filtration-level": "Discard filtration level", "must-not-be-less-than-har": "Must not be less than hard filtration level", "filter": "Filter", "wrong-quotes-coming-one": "Wrong quotes coming one after another", "other": "Other", "gap-mode-level": "Gap mode level", "minimum-spread": "Minimum spread", "delay-for-subscription": "Delay for subscription", "maximum-spread": "Maximum spread"}, "Sessions": {"sessions-timing": "Sessions Timing", "day": "Day", "quote": "Quote", "trade": "Trade", "use-time-limit": "Use time Limit", "edit-editday-text": "Edit {0}", "cancel": "Cancel", "quotes": "Quotes", "trades": "Trades", "enable-separate-trading-s": "Enable Separate Trading Sessions", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "time-expiration-must-be-greater-than-time-start": "Time Expiration must be greater than Starting Time"}, "Swaps": {"enable-swaps": "Enable <PERSON>waps", "settings": "Settings", "type": "Type", "long-positions": "Long Positions", "short-positions": "Short Positions", "days-in-year": "Days in Year", "automatically-consider-ho": "Automatically consider holidays", "swap-multipliers": "Swap Multipliers", "set-standard-settings": "Set Standard Settings", "forex": "Forex", "all-week": "All Week", "from-symbol": "From Symbol", "choose-symbol": "Choose Symbol", "cancel": "Cancel", "save": "Save", "symbol": "Symbol", "day": "Day", "multiplier": "Multiplier", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "Trade": {"settings": "Settings", "contract-size": "Contract Size", "calculation": "Calculation", "trade": "Trade", "filling": "Filling", "expiration": "Expiration", "limit-and-stop-level": "Limit & Stop Level", "freeze-level": "Freeze Level", "max-quote-delay": "<PERSON><PERSON> Quote <PERSON>", "convert-profit": "Convert Profit", "seconds": "seconds", "orders": "Orders", "enable-trading-signal": "Enable Trading Signal", "volumes": "Volumes", "minimum": "Minimum", "step": "Step", "maximum": "Maximum", "limit": "Limit", "tick-value": "Tick Value", "tick-size": "<PERSON><PERSON>"}, "common": "Common", "currency": "<PERSON><PERSON><PERSON><PERSON>", "quotes": "Quotes", "execution": "Execution", "margin": "<PERSON><PERSON>", "margin-rates": "Margin Rates", "swaps": "Swaps", "sessions": "Sessions", "symbol": "Symbol", "trade": "Trade", "options": "Options", "futures": "Futures", "bonds": "<PERSON><PERSON>", "Futures": {"settlement-price": "Settlement Price", "minimum-price": "Minimum Price", "maximum-price": "Maximum Price", "splice-type": "Splice Type", "splice-date": "Splice Date"}, "Options": {"option-type": "Option Type", "option-style": "Option Style", "strike-price": "Strike Price"}, "Bond": {}, "Bonds": {"accured-interest": "Accured Interest", "face-value": "Face Value"}}, "ExecutionMode": {"EXECUTION_MARKET": "Market", "EXECUTION_REQUEST": "Request", "EXECUTION_INSTANT": "Instant", "EXECUTION_EXCHANGE": "Exchange"}, "Sectors": {"SECTOR_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "SECTOR_UNDEFINED": "Undefined", "SECTOR_BASIC_MATERIALS": "Basic Materials", "SECTOR_COMMUNICATION_SERVICES": "Communication Services", "SECTOR_CONSUMER_CYCLICAL": "Consumer Cyclical", "SECTOR_CONSUMER_DEFENSIVE": "Consumer Defensive", "SECTOR_ENERGY": "Energy", "SECTOR_FINANCIAL": "Financial", "SECTOR_HEALTHCARE": "Healthcare", "SECTOR_INDUSTRIALS": "Industrials", "SECTOR_REAL_ESTATE": "Real Estate", "SECTOR_TECHNOLOGY": "Technology", "SECTOR_UTILITIES": "Utilities", "SECTOR_CURRENCY_CRYPTO": "Crypto", "SECTOR_INDEXES": "Indexes", "SECTOR_COMMODITIES": "Commodities"}, "Industries": {"UNDEFINED": "Undefined", "AGRICULTURAL_INPUTS": "Agricultural Inputs", "ALUMINIUM": "Aluminum", "BUILDING_MATERIALS": "Building Materials", "CHEMICALS": "Chemicals", "COKING_COAL": "Coking Coal", "COPPER": "Copper", "GOLD": "Gold", "LUMBER_WOOD": "<PERSON><PERSON>", "INDUSTRIAL_METALS": "Industrial Metals", "PRECIOUS_METALS": "Precious Metals", "PAPER": "Paper", "SILVER": "Silver", "SPECIALTY_CHEMICALS": "Specialty Chemicals", "STEEL": "Steel", "ADVERTISING": "Advertising", "BROADCASTING": "Broadcasting", "GAMING_MULTIMEDIA": "Gaming Multimedia", "ENTERTAINMENT": "Entertainment", "INTERNET_CONTENT": "Internet Content", "PUBLISHING": "Publishing", "TELECOM": "Telecom", "APPAREL_MANUFACTURING": "Apparel Manufacturing", "APPAREL_RETAIL": "Apparel Retail", "AUTO_MANUFACTURERS": "Auto Manufacturers", "AUTO_PARTS": "Auto Parts", "AUTO_DEALERSHIP": "Auto Dealership", "DEPARTMENT_STORES": "Department Stores", "FOOTWEAR_ACCESSORIES": "Footwear Accessories", "FURNISHINGS": "Furnishings", "GAMBLING": "Gambling", "HOME_IMPROV_RETAIL": "Home Improvement Retail", "INTERNET_RETAIL": "Internet Retail", "LEISURE": "Leisure", "LODGING": "Lodging", "LUXURY_GOODS": "Luxury Goods", "PACKAGING_CONTAINERS": "Packaging Containers", "PERSONAL_SERVICES": "Personal Services", "RECREATIONAL_VEHICLES": "Recreational Vehicles", "RESIDENT_CONSTRUCTION": "Resident Construction", "RESORTS_CASINOS": "Resorts Casinos", "RESTAURANTS": "Restaurants", "SPECIALTY_RETAIL": "Specialty Retail", "TEXTILE_MANUFACTURING": "Textile Manufacturing", "TRAVEL_SERVICES": "Travel Services", "BEVERAGES_BREWERS": "Beverages Brewers", "BEVERAGES_NON_ALCO": "Beverages Non Alcoholic", "BEVERAGES_WINERIES": "Beverages Wineries", "CONFECTIONERS": "Confectioners", "DISCOUNT_STORES": "Discount Stores", "EDUCATION_TRAINIG": "Education Training", "FARM_PRODUCTS": "Farm Products", "FOOD_DISTRIBUTION": "Food Distribution", "GROCERY_STORES": "Grocery Stores", "HOUSEHOLD_PRODUCTS": "Household Products", "PACKAGED_FOODS": "Packaged Foods", "TOBACCO": "Tobacco", "OIL_GAS_DRILLING": "Oil & Gas Drilling", "OIL_GAS_EP": "Oil & Gas Exploration & Production", "OIL_GAS_EQUIPMENT": "Oil & Gas Equipment", "OIL_GAS_INTEGRATED": "Oil & Gas Integrated", "OIL_GAS_MIDSTREAM": "Oil & Gas Midstream", "OIL_GAS_REFINING": "Oil & Gas Refining", "THERMAL_COAL": "Thermal Coal", "URANIUM": "Uranium", "EXCHANGE_TRADED_FUND": "Exchange Traded Fund", "ASSETS_MANAGEMENT": "Assets Management", "BANKS_DIVERSIFIED": "Banks Diversified", "BANKS_REGIONAL": "Banks Regional", "CAPITAL_MARKETS": "Capital Markets", "CLOSE_END_FUND_DEBT": "Closed-End Fund Debt", "CLOSE_END_FUND_EQUITY": "Closed-End Fund Equity", "CLOSE_END_FUND_FOREIGN": "Closed-End Fund Foreign", "CREDIT_SERVICES": "Credit Services", "FINANCIAL_CONGLOMERATE": "Financial Conglomerate", "FINANCIAL_DATA_EXCHANGE": "Financial Data Exchange", "INSURANCE_BROKERS": "Insurance Brokers", "INSURANCE_DIVERSIFIED": "Insurance Diversified", "INSURANCE_LIFE": "Insurance Life", "INSURANCE_PROPERTY": "Insurance Property", "INSURANCE_REINSURANCE": "Insurance Reinsurance", "INSURANCE_SPECIALTY": "Insurance Specialty", "MORTGAGE_FINANCE": "Mortgage Finance", "SHELL_COMPANIES": "Shell Companies", "BIOTECHNOLOGY": "Biotechnology", "DIAGNOSTICS_RESEARCH": "Diagnostics Research", "DRUGS_MANUFACTURERS": "Drugs Manufacturers", "DRUGS_MANUFACTURERS_SPEC": "Drugs Manufacturers Specialty", "HEALTHCARE_PLANS": "Healthcare Plans", "HEALTH_INFORMATION": "Health Information", "MEDICAL_FACILITIES": "Medical Facilities", "MEDICAL_DEVICES": "Medical Devices", "MEDICAL_DISTRIBUTION": "Medical Distribution", "MEDICAL_INSTRUMENTS": "Medical Instruments", "PHARM_RETAILERS": "Pharmaceutical Retailers", "AEROSPACE_DEFENSE": "Aerospace & Defense", "AIRLINES": "Airlines", "AIRPORTS_SERVICES": "Airports Services", "BUILDING_PRODUCTS": "Building Products", "BUSINESS_EQUIPMENT": "Business Equipment", "CONGLOMERATES": "Conglomerates", "CONSULTING_SERVICES": "Consulting Services", "ELECTRICAL_EQUIPMENT": "Electrical Equipment", "ENGINEERING_CONSTRUCTION": "Engineering & Construction", "FARM_HEAVY_MACHINERY": "Farm & Heavy Machinery", "INDUSTRIAL_DISTRIBUTION": "Industrial Distribution", "INFRASTRUCTURE_OPERATIONS": "Infrastructure Operations", "FREIGHT_LOGISTICS": "Freight & Logistics", "MARINE_SHIPPING": "Marine Shipping", "METAL_FABRICATION": "Metal Fabrication", "POLLUTION_CONTROL": "Pollution Control", "RAILROADS": "Railroads", "RENTAL_LEASING": "Rental & Leasing", "SECURITY_PROTECTION": "Security & Protection", "SPEALITY_BUSINESS_SERVICES": "Specialty Business Services", "SPEALITY_MACHINERY": "Specialty Machinery", "STUFFING_EMPLOYMENT": "Staffing & Employment", "TOOLS_ACCESSORIES": "Tools & Accessories", "TRUCKING": "Trucking", "WASTE_MANAGEMENT": "Waste Management", "REAL_ESTATE_DEVELOPMENT": "Real Estate Development", "REAL_ESTATE_DIVERSIFIED": "Real Estate Diversified", "REAL_ESTATE_SERVICES": "Real Estate Services", "REIT_DIVERSIFIED": "REIT Diversified", "REIT_HEALTCARE": "REIT Healthcare", "REIT_HOTEL_MOTEL": "REIT Hotel & Motel", "REIT_INDUSTRIAL": "REIT Industrial", "REIT_MORTAGE": "REIT Mortgage", "REIT_OFFICE": "REIT Office", "REIT_RESIDENTAL": "REIT Residential", "REIT_RETAIL": "REIT Retail", "REIT_SPECIALITY": "REIT Specialty", "COMMUNICATION_EQUIPMENT": "Communication Equipment", "COMPUTER_HARDWARE": "Computer Hardware", "CONSUMER_ELECTRONICS": "Consumer Electronics", "ELECTRONIC_COMPONENTS": "Electronic Components", "ELECTRONIC_DISTRIBUTION": "Electronic Distribution", "IT_SERVICES": "IT Services", "SCIENTIFIC_INSTRUMENTS": "Scientific Instruments", "SEMICONDUCTOR_EQUIPMENT": "Semiconductor Equipment", "SEMICONDUCTORS": "Semiconductors", "SOFTWARE_APPLICATION": "Software Application", "SOFTWARE_INFRASTRUCTURE": "Software Infrastructure", "SOLAR": "Solar", "UTILITIES_DIVERSIFIED": "Utilities Diversified", "UTILITIES_POWERPRODUCERS": "Utilities Power Producers", "UTILITIES_RENEWABLE": "Utilities Renewable", "UTILITIES_REGULATED_ELECTRIC": "Utilities Regulated Electric", "UTILITIES_REGULATED_GAS": "Utilities Regulated Gas", "UTILITIES_REGULATED_WATER": "Utilities Regulated Water", "COMMODITIES_AGRICULTURAL": "Agricultural Commodities", "COMMODITIES_ENERGY": "Energy Commodities", "COMMODITIES_METALS": "Metals Commodities", "COMMODITIES_PRECIOUS": "Precious Commodities"}, "ChartMode": {"CHART_MODE_BID_PRICE": "By Bid Price", "CHART_MODE_LAST_PRICE": "By Last Price"}, "CalcMode": {"TRADE_MODE_FOREX": "Forex", "TRADE_MODE_FUTURES": "Futures", "TRADE_MODE_CFD": "CFD", "TRADE_MODE_CFDINDEX": "CFD Index", "TRADE_MODE_CFDLEVERAGE": "CFD Leverage", "TRADE_MODE_FOREX_NO_LEVERAGE": "Forex No Leverage", "TRADE_MODE_EXCH_STOCKS": "Exchange Stocks", "TRADE_MODE_EXCH_FUTURES": "Exchange Futures", "TRADE_MODE_EXCH_FUTURES_FORTS": "Exchange Futures FORTS", "TRADE_MODE_EXCH_OPTIONS": "Exchange Options", "TRADE_MODE_EXCH_OPTIONS_MARGIN": "Exchange Options Margin", "TRADE_MODE_EXCH_BONDS": "Exchange Bonds", "TRADE_MODE_EXCH_STOCKS_MOEX": "Exchange Stocks MOEX", "TRADE_MODE_EXCH_BONDS_MOEX": "Exchange Bonds MOEX", "TRADE_MODE_SERV_COLLATERAL": "Collateral"}, "TradeMode": {"TRADE_DISABLED": "Disabled", "TRADE_LONGONLY": "Long Only", "TRADE_SHORTONLY": "Short Only", "TRADE_CLOSEONLY": "Close Only", "TRADE_FULL": "Full Access"}, "GTCMode": {"ORDERS_GTC": "<PERSON> Cancelled", "ORDERS_DAILY": "Good Till Today Including SL/TP", "ORDERS_DAILY_NO_STOPS": "Good Till Today Excluding SL/TP"}, "FillingFlags": {"FILL_FLAGS_NONE": "None", "FILL_FLAGS_FOK": "Fill or Kill", "FILL_FLAGS_IOC": "Immediate or Cancel", "FILL_FLAGS_BOC": "Book or Cancel", "FILL_FLAGS_ALL": "All Flags"}, "ExpirationFlags": {"TIME_FLAGS_NONE": "None", "TIME_FLAGS_GTC": "Good Till Cancel", "TIME_FLAGS_DAY": "Day", "TIME_FLAGS_SPECIFIED": "Specified Time", "TIME_FLAGS_SPECIFIED_DAY": "Specified Day", "TIME_FLAGS_ALL": "All Flags"}, "TradeFlags": {"NONE": "By Deal", "PROFIT_BY_MARKET": "Profit by Market"}, "OrderFlags": {"NONE": "None", "MARKET": "Market", "LIMIT": "Limit", "STOP": "Stop", "STOP_LIMIT": "Stop Limit", "SL": "Stop Loss", "TP": "Take Profit", "CLOSEBY": "Close By"}, "MarginFlags": {"MARGIN_FLAGS_CHECK_PROCESS": "Check Before Executing Order", "MARGIN_FLAGS_CHECK_SLTP": "Check On SL/TP Trigger"}, "SwapMode": {"SWAP_DISABLED": "Disabled", "SWAP_BY_POINTS": "In Points", "SWAP_BY_SYMBOL_CURRENCY": "In Money, Using Base Currency", "SWAP_BY_MARGIN_CURRENCY": "In Money, Using Margin <PERSON>cy", "SWAP_BY_GROUP_CURRENCY": "In Money, Using Group Currency", "SWAP_BY_INTEREST_CURRENT": "In Percentage Terms, Using Current Price", "SWAP_BY_INTEREST_OPEN": "In Percentage Terms, Using Open Price", "SWAP_REOPEN_BY_CLOSE_PRICE": "In Points, Reopen Position by Close Price", "SWAP_REOPEN_BY_BID": "In Points, Reopen Position by <PERSON><PERSON>", "SWAP_BY_PROFIT_CURRENCY": "In Money, Using Profit Currency"}, "SwapFlags": {"SWAP_FLAGS_NONE": "None", "SWAP_FLAGS_CONSIDER_HOLIDAYS": "Automatically Consider Holidays"}, "SpliceType": {"NONE": "None", "UNADJUSTED": "Unadjusted", "ADJUSTED": "Adjusted"}, "SpliceTimeType": {"EXPIRATION": "Expiration"}}, "MT4": {"Crud": {"Symbol": {"general": "General", "symbol": "Symbol", "source": "Source", "digits": "Digits", "description": "Description", "type": "Type", "execution": "Execution", "currency": "<PERSON><PERSON><PERSON><PERSON>", "trade": "Trade", "margin-currency": "Margin currency", "max-lots-for-ie": "Max. Lots for IE", "orders": "Orders", "limit-and-stop-level": "Limit & Stop Level", "spread-by-default": "Spread by default", "long-only": "Long only", "freeze-level": "Freeze Level", "spread-balance": "Spread Balance", "item-spreadbalance-spread": "{0} Bid / {1} Ask", "background": "Background", "loaded-symbol": "Changing Symbol name will create a new symbol"}, "Swaps": {"enable-swaps": "Enable <PERSON>waps", "settings": "Settings", "type": "Type", "long-positions": "Long Positions", "short-positions": "Short Positions", "days-in-year": "Days in Year", "use-open-price-for-positi": "Use open price for position value calculation", "charge-variation-margin-o": "Charge variation margin on rollover"}, "Filtration": {"allow-realtime-quotes-fro": "Allow realtime quotes from datafeeds", "save-all-incoming-prices-": "Save all incoming prices in a file (this will slow down your server)", "settings": "Settings", "filtration-level": "Filtration Level", "automatic-limit": "Automatic Limit", "filter": "Filter", "ignore-quotes": "Ignore quotes", "smoothing": "Smoothing", "none": "None"}, "Calculation": {"settings": "Settings", "contract-size": "Contract size", "initial-margin": "Initial margin", "maintenance": "Maintenance", "hedged": "Hedged", "tick-size": "Tick size", "tick-price": "Tick price", "percentage": "Percentage", "calculation-method": "Calculation Method", "margin-calculation": "Margin calculation", "profit-calculation": "Profit calculation", "strong-hedged-margin-mode": "Strong Hedged margin mode"}, "Sessions": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "day": "Day", "quote": "Quote", "trade": "Trade", "use-time-limit": "Use time limit", "sessions-timing": "Sessions Timing", "edit-editday-text": "Edit {0}", "cancel": "Cancel", "quotes": "Quotes", "trades": "Trades", "enable-separate-trading-s": "Enable Seperate Trading Sessions"}, "create-item-symbol": "Create \"{0}\" {1}", "update-item-symbol-itemna": "Update \"{0}\" {1}", "symbol": "Symbol", "filtration": "Filtration", "calculation": "Calculation", "swaps": "Swaps", "sessions": "Sessions", "please-fix-the-form-errors": "Please fix the form errors and try again"}, "ExecutionMode": {"EXE_REQUEST": "Request", "EXE_INSTANT": "Instant", "EXE_MARKET": "Market"}, "TradingMode": {"TRADE_NO": "No", "TRADE_CLOSE": "Close", "TRADE_FULL": "Full Access"}, "SwapType": {"SWAP_BY_POINTS": "By Points [ Lots × Long or Short Points × Point Size ]", "SWAP_BY_DOLLARS": "By Money [ Lots × Long or Short Points ]", "SWAP_BY_INTEREST": "By Interest [ Lots × Long or Short Points / 100 / 360 ]", "SWAP_BY_MARGIN_CURRENCY": "By Money in Margin Currency [ Lots × Long or Short Points ]"}, "MarginCalculationMode": {"MARGIN_CALC_FOREX": "Forec [ Lots × Contract Size / Leverage × Percentage / 100 ]", "MARGIN_CALC_CFD": "CFD [ Lots × Contract Size × Market Price × Percentage / 100 ]", "MARGIN_CALC_FUTURES": "Futures [ Lots × Initial Margin × Percentage / 100 ]", "MARGIN_CALC_CFDINDEX": "CFD-Index [ Lots × Contract Size × Market Price / Tick Size × Price × Percentage / 100 ]", "MARGIN_CALC_CFDLEVERAGE": "CFD-Leverage [ Lots × Contract Size × Market Price / Leverage × Percentage / 100]"}, "ProfitCalculationMode": {"PROFIT_CALC_FOREX": "Forex [ (Close Price - Open Price) × Contract × Lots × Point Size ]", "PROFIT_CALC_CFD": "CFD [ (Close Price - Open Price) × Contract × Lots ]", "PROFIT_CALC_FUTURES": "Futures [ (Close Price - Open Price) × Tick Price / Tick Size × Lots ]"}, "GTCMode": {"ORDERS_DAILY": "Good Till Today Including SL/TP", "ORDERS_GTC": "<PERSON> Cancelled", "ORDERS_DAILY_NO_STOPS": "Good Till Today Excluding SL/TP"}, "Days": {"SUNDAY": "Sunday", "MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday"}}}, "forgot": {"forgot-password": "Forgot Password", "please-enter-your-email": "Please enter your email address. We will send you a link to reset your password.", "enter-your-email-address": "Enter your email address", "email": "Email", "remembered-your-password": "Remembered your password?", "reset-my-password": "Reset My Password", "reset-link-has-been-sent": "Reset link <PERSON>", "back-to-login": "Return to Login", "an-email-has-been-sent-to": "An email has been sent to \"{0}\" with a link to reset your password. Please follow the link to complete the process."}, "reset": {"reset-password": "Reset Password", "please-enter-your-new-password": "Please enter your new password below to reset the password for the account \"{0}\".", "password": "Password", "enter-your-password": "Enter your password", "confirm-your-password": "Confirm your password", "confirm-password": "Confirm Password", "remembered-your-password": "Remembered your password?", "reset": "Reset", "your-password-has-been-su": "Your password has been successfully reset. You can now login with your new password."}, "TypeList": {"no-type-found-matching-an": "No type found matching \"{0}\"", "no-type-found": "No type found"}, "groups": {"groups": "Groups", "create-group": "Create Group", "name": "Name", "company": "Company", "mc-so": "MC / SO", "securities": "Securities", "find-path": "Find Path", "server": "Server", "margin": "<PERSON><PERSON>"}, "Groups": {"MT4": {"Crud": {"group": "Group", "common": "Common", "permissions": "Permissions", "archiving": "Archiving", "margins": "<PERSON><PERSON>", "securities": "Securities", "symbols": "Symbols", "reports": "Reports", "create-group": "Create Group", "update-group": "Update \"{0}\" Group", "Symbols": {"add-symbol": "Add Symbol", "default": "<PERSON><PERSON><PERSON>", "symbol": "Symbol", "long-position-swap": "Long position Swap", "margin-percentage": "<PERSON><PERSON> Per<PERSON>age", "short-position-swap": "Short position Swap", "cancel": "Cancel", "long": "<PERSON>", "short": "Short", "percentage": "Percentage", "add-symbol-0": "Add Symbol", "edit-currentsymbol-value-": "Edit {0}", "delete-symbol": "Delete Symbol", "are-you-sure-you-want-to-": "Are you sure you want to delete this symbol?"}, "Archiving": {"inactivity-period": "Inactivity Period", "maximum-balance": "Maximum Balance", "archive-deleted-pending": "Archive deleted pending older"}, "Common": {"enabled": "Enabled", "name": "Name", "one-time-password": "One-time Password", "changing-group-name-new-group": "Changing group name will create a new group", "force-otp-usage": "Force OTP Usage", "owner": "Owner", "support-page": "Support Page", "deposit-by-default": "Deposit By Default", "deposit-currency": "De<PERSON><PERSON><PERSON>", "leverage-by-default": "Leverage by <PERSON><PERSON><PERSON>", "annual-interest-rate": "Annual Interest Rate", "used-only-for-trade-in-fo": "Used Only for Trade in Forex", "daily-free-margin-credit-": "Daily = (Free Margin - Credit) × Interest Rate / 100 / 360"}, "EditSecurity": {"general": "General", "enabled": "Enabled", "trade": "Trade", "use-confirmations-in-requ": "Use Confirmations in REQUEST Mode", "execution": "Execution", "spread-difference": "Spread Difference", "do-not-check-free-margin-": "Do not check free margin after dealer's answer", "fast-confirmation-on-ie-w": "Fast Confirmation on IE With Deviation Specified", "maximum-deviation": "Maximum Deviation", "close-by": "Close By", "enable": "Enable", "multiple-close-by-orders": "Multiple Close By Orders", "auto-close-out": "Auto Close-Out", "lots": "Lots", "commissions": "Commissions", "step": "Step", "max": "<PERSON>.", "min": "<PERSON>.", "standard": "Standard", "taxes": "Taxes", "agent": "Agent"}, "Margins": {"margin-call-level": "Margin Call Level", "stop-out-level": "Stop Out Level", "free-margin": "Free Margin", "virtual-credit": "Virtual Credit", "applies-only-to-opening-n": "Applies only to opening new positions", "skip-fully-hedged-account": "<PERSON><PERSON> Hedged Accounts When Checking for Stop-out", "calculate-hedged-margin-u": "<PERSON><PERSON> Hedged Margin Using Larger Leg"}, "Permissions": {"timeout": "Timeout", "news-languages": "News Languages", "max-symbols": "<PERSON><PERSON>", "max-orders": "Max. Orders", "trade-signals": "Trade Signals", "enable-internal-mail-syst": "Enable Internal Mail System", "enable-charge-of-swap": "Enable Charge of Swap", "enable-trailing-stop": "Enable Trailing Stop", "enable-trading-by-expert-": "Enable Trading By Expert Advisors", "enable-expiration-of-pend": "Enable Expiration of Pending Orders", "check-request-prices-in-i": "Check Request Prices in IE", "prohibit-hedge-positions": "Prohibit Hedge Positions", "position-closing-accordin": "Position Closing According to FIFO Rule", "use-partial-close-with-fu": "Use Partial Close With Full Close of Initial Position", "show-the-risk-warning-win": "Show The Risk Warning Window After Connection", "unlimited": "Unlimited", "disable": "Disable"}, "Reports": {"enable": "Enable", "smtp-server": "SMTP Server", "smtp-login": "SMTP Login", "smtp-password": "SMTP Password", "templates-path": "Templates Path", "support-email": "Support Email", "copy-report-to-support": "Copy report to support", "signature": "Signature"}, "Securities": {"default": "<PERSON><PERSON><PERSON>", "edit": "Edit", "cancel": "Cancel", "type": "Type", "trade": "Trade", "exec": "Exec.", "commission": "Commission"}}, "ExecutionMode": {"EXECUTION_MANUAL": "Manual", "EXECUTION_AUTO": "Automatic", "EXECUTION_ACTIVITY": "Manual / Automatic"}, "OtpMode": {"OTP_MODE_TOTP_SHA256": "TOTP SHA-256", "OTP_MODE_DISABLED": "Disabled"}, "NewsLanguage": {"Afrikaans": "Afrikaans", "Albanian": "Albanian", "Arabic": "Arabic", "Armenian": "Armenian", "Azerbaijani": "Azerbaijani", "Basque": "Basque", "Belarusian": "Belarusian", "Bulgarian": "Bulgarian", "ChineseTraditional": "ChineseTraditional", "Croatian": "Croatian", "Czech": "Czech", "Danish": "Danish", "Dutch": "Dutch", "English": "English", "Estonian": "Estonian", "Finnish": "Finnish", "French": "French", "Georgian": "Georgian", "German": "German", "Greek": "Greek", "Hebrew": "Hebrew", "Hindi": "Hindi", "Hungarian": "Hungarian", "Indonesian": "Indonesian", "Italian": "Italian", "Japanese": "Japanese", "Korean": "Korean", "Latvian": "Latvian", "Lithuanian": "Lithuanian", "Macedonian": "Macedonian", "Norwegian": "Norwegian", "Oriya": "Oriya", "Persian": "Persian", "Polish": "Polish", "Portuguese": "Portuguese", "Romanian": "Romanian", "Russian": "Russian", "Slovak": "Slovak", "Slovenian": "Slovenian", "Spanish": "Spanish", "Swedish": "Swedish", "Tatar": "Tatar", "Thai": "Thai", "Turkish": "Turkish", "Ukrainian": "Ukrainian", "Urdu": "Urdu", "Vietnamese": "Vietnamese"}, "MarginMode": {"MARGIN_MODE_DONT_USE": "Don't Use Unrealized Profit/Loss", "MARGIN_MODE_USE_ALL": "Use Unrealized Profit/Loss", "MARGIN_MODE_USE_PROFIT": "Use Unrealized Profit Only", "MARGIN_MODE_USE_LOSS": "Use Unrealized Loss Only"}, "MarginType": {"MARGIN_TYPE_PERCENT": "Percent", "MARGIN_TYPE_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>"}, "AutoCloseoutMode": {"CLOSE_OUT_NONE": "None", "CLOSE_OUT_HIHI": "HIHI, Highest Sells Against Highest Buys", "CLOSE_OUT_LOLO": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Sells Against Lowest Buys", "CLOSE_OUT_HILO": "HILOm Highest Sells Against Lowest Buys", "CLOSE_OUT_LOHI": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> Sells Against Highest Buys", "CLOSE_OUT_FIFO": "FIFO, First Brought/Sold Against First Sold/Bought", "CLOSE_OUT_LIFO": "LIFO, Last Brought/Sold Against Fisrt Sold/Bought", "CLOSE_OUT_INTRDAY_FIFO": "Intraday Followed by FIFO"}, "CommissionUnit": {"COMM_TYPE_MONEY": "$", "COMM_TYPE_PIPS": "pt", "COMM_TYPE_PERCENT": "%"}, "CommissionCalculationType": {"COMMISSION_PER_LOT": "Per Lot", "COMMISSION_PER_DEAL": "Per Deal"}, "GroupPermissionFlags": {"ALLOW_FLAG_SIGNALS_OWN": "Enable Signals From My Servers Only", "ALLOW_FLAG_SIGNALS_ALL": "Enable All Signals From All Brokers"}}, "MT5": {"Crud": {"create-item": "Create \"{0}\" {1}", "update-item": "Update \"{0}\" {1}", "group": "Group", "common": "Common", "company": "Company", "news-and-mail": "News and Mail", "permissions": "Permissions", "margin": "<PERSON><PERSON>", "symbols": "Symbols", "commissions": "Commissions", "reports": "Reports", "trade": "Trade", "execution": "Execution", "margin-rates": "Margin Rates", "swaps": "Swaps", "Commission": {"CommissionForm": {"name": "Name", "symbol": "Symbol", "description": "Description", "range": "Range", "charge": "Charge", "turnover-currency": "Turnover C<PERSON>rency", "entry-mode": "Entry Mode", "action-mode": "Action Mode", "profit-mode": "Profit Mode", "reasons": "Reasons", "reason-mode-length": "(+{0} others)", "add-new-tier": "Add New Tier", "from": "From", "to": "To", "commission": "Commission", "currency": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "mode": "Mode", "min": "Min", "max": "Max", "cancel": "Cancel", "add-tier": "Add Tier", "edit-tier": "Edit Tier", "are-you-sure-you-want-to-remove-tier": "Are you sure you want to remove this tier?", "comm": "<PERSON><PERSON>"}}, "Symbols": {"add-symbol": "Add Symbol", "symbol": "Symbol", "spread": "Spread", "trade": "Trade", "cancel": "Cancel", "add-symbol-title": "Add Symbol", "edit-symbol-currentsymbol-title": "Edit Symbol \"{0}\"", "are-you-sure-you-want-to-remove-symbol": "Are you sure you want to remove this symbol?", "SymbolForm": {"Common": {"path": "Path", "enable-market-depth": "Enable Market Depth", "use-default-spread": "Use Default Spread", "market-depth-limit": "Market Depth Limit", "spread-balance": "Spread Balance", "bidprice-bid-askprice-ask": "{0}Bid / {1} Ask", "min": "Min", "step": "Step", "max": "Max", "use-default-limit": "Use Default Limit", "limit": "Limit", "spread-difference": "Spread Difference", "unlimited": "Unlimited", "use-default-volume": "Use Default Volume"}, "Execution": {"timeout": "Timeout", "trade": "Trade", "confirm-orders": "Confirm Orders", "max-time-deviation": "Max. Time Deviation", "max-profit-deviation": "Max. Profit Deviation", "max-losing-deviation": "<PERSON><PERSON> Losing Deviation", "max-volume": "Max. Volume", "fast-confirmation-of-request": "Fast Confirmation of requites within client Deviation", "use-default-exec-setting": "Use Default Execution Settings"}, "Margin": {"initial-margin": "Initial Margin", "hedged-margin": "Hedged <PERSON><PERSON>", "maintenance-margin": "Maintenance Margin", "calculate-hedged-margin": "<PERSON><PERSON> Hedged Margin Using Larger Leg", "exclude-long-position-pnl": "Exclude Long Position PnL From Free Margin & Margin Level", "recalculate-margin-exchange": "Recalculate Margin Exchange Rate at The End of Day", "additional-margin-checks": "Additional Margin Checks", "check-before-executing": "Check Before Executing Order", "check-on-sl-tp-trigger": "Check on SL-TP Trigger", "use-default-margin-setting": "Use Default Margin Setting", "use-default-margin-values": "Use Default Margin Values"}, "MarginRates": {"liquidity-margin-rate": "Liquidity Margin Rate", "currency-margin-rate": "<PERSON><PERSON><PERSON><PERSON>", "margin-rates": "Margin Rates", "market-order": "Market Order", "limit-order": "Limit Order", "stop-order": "Stop Order", "stop-limit-order": "Stop Limit Order", "initial-margin": "Initial Margin", "buy": "Buy", "sell": "<PERSON>ll", "maintenance-margin": "Maintenance Margin"}, "Swaps": {"use-default-swap-settings": "Use Default Swap Settings", "enable-swaps": "Enable <PERSON>waps", "settings": "Settings", "type": "Type", "long-positions": "Long Positions", "short-positions": "Short Positions", "days-in-year": "Days in Year", "automatically-consider-ho": "Automatically consider holidays", "swap-multipliers": "Swap Multipliers", "set-standard-settings": "Set Standard Settings", "forex": "Forex", "all-week": "All Week", "from-symbol": "From Symbol", "choose-symbol": "Choose Symbol", "cancel": "Cancel", "save": "Save", "symbol": "Symbol", "day": "Day", "multiplier": "Multiplier", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "default": "<PERSON><PERSON><PERSON>"}, "Trade": {"use-default-trade-setting": "Use Default Trade Settings", "trade": "Trade", "filling": "Filling", "expiration": "Expiration", "orders": "Orders", "use-default-trade-level-s": "Use Default Trade Level Settings", "limit-and-stop-level": "Limit & Stop Level", "freeze-level": "Freeze Level"}}, "default": "<PERSON><PERSON><PERSON>"}, "Commissions": {"add-commission": "Add Commission", "name": "Name", "symbol": "Symbol", "type": "Type", "range": "Range", "charge": "Charge", "cancel": "Cancel", "are-you-sure-you-want-to-remove": "Are you sure you want to remove this commission?", "add-commission-0": "Add Commission", "edit-commission-currentco": "Edit Commission \"{0}\""}, "Common": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "name": "Name", "server": "Server", "authentication": "Authentication", "password-min-length": "Password Min. Length", "digits": "Digits", "force-otp-usage": "Force OTP Usage", "push-notification": "Push Notification", "enable-connections": "Enable Connections", "enable-certificate-confirmation": "Enable Certificate Confirmation", "change-password-at-login": "Change Password at Login", "show-the-risk-warning-win": "Show the Risk Warning Window After Connection", "enforce-country-specific": "Enforce country-specific Regulatory Restrictions for Retail Clients", "loaded-group": "Changing Group name will create a new Group"}, "Company": {"company": "Company", "company-site": "Company Site", "company-email": "Company Email", "deposit-url": "Deposit URL", "withdrawal-url": "<PERSON><PERSON>wal URL", "support-site": "Support Site", "support-email": "Support Email", "templates-path": "Templates Path"}, "Margin": {"risk-management": "Risk Management", "margin-call-level": "Margin Call Level", "stop-out-level": "Stop Out Level", "stop-out-fully-hedged-acc": "Stop Out Fully Hedged Accounts", "compensate-negative-balan": "Compensate Negative Balance After Stop Out", "withdraw-credit-after-neg": "Withdraw Credit After Negative Balance Compensation", "floating-leverage-profile": "Floating Leverage Profile", "profit-loss-in-free-margin": "Profit/Loss in Free Margin", "unrealized-profit": "Unrealized Profit", "daily-fixed-profit": "Daily Fixed Profit", "release-fixed-profit-at-t": "Release Fixed Profit at The End of Day", "virtual-credit": "Virtual Credit", "in": "in"}, "NewsAndMail": {"news": "News", "news-categories": "News Categories", "news-languages": "News Languages", "enable-internal-mail-syst": "Enable Internal Mail System"}, "Permissions": {"maximum-symbols": "Maximum Symbols", "maximum-symbols-0": "Maximum Symbols", "maximum-positions": "Maximum Positions", "maximum-orders": "Maximum Orders", "deposit-by-default": "Deposit By Default", "leverage-by-default": "Leverage By Default", "trading-signals": "Trading Signals", "transfer-mode": "Transfer Mode", "enable-trading-by-expert-": "Enable Trading By Expert Advisors", "enable-position-closing-a": "Enable Position Closing According to FIFO", "enable-trailing-stops": "Enable Trailing Stops", "prohibit-hedge-positions": "Prohibit Hedge Positions", "enable-charge-of-swaps": "Enable Charge of Swaps", "enable-deal-cost-calculation": "Enable Deal Cost Calculation", "inactivity-period": "Inactivity Period", "unlimited": "Unlimited", "trade-interestrate": "Trade Interest Rate"}, "Reports": {"generate-statement-for-clients": "Generate Statement for Clients", "send-statement-by-email": "Send Statement by Email", "mail-server": "Mail Server", "send-copies-to-support-email": "Send Copies to Support Email", "default": "<PERSON><PERSON><PERSON>"}, "SymbolPathTree": {"select-path": "Select Path", "find-path": "Find Path"}}, "EnCommMode": {"COMM_STANDARD": "Standard", "COMM_AGENT": "Agent", "COMM_FEE": "Fee"}, "EnAuthMode": {"AUTH_STANDARD": "Normal", "AUTH_RSA1024": "1024-bit RSA SSL Certificate", "AUTH_RSA2048": "2048-bit RSA SSL Certificate", "AUTH_RSA_CUSTOM": "Custom SSL Certificate"}, "EnAuthOTPMode": {"AUTH_OTP_DISABLED": "Disabled", "AUTH_OTP_TOTP_SHA256": "TOTP SHA-256", "AUTH_OTP_TOTP_SHA256_WEB": "TOTP SHA-256 Web"}, "EnPermissionsFlags": {"PERMISSION_NOTIFY_DEALS": "Deals", "PERMISSION_NOTIFY_ORDERS": "Orders", "PERMISSION_NOTIFY_BALANCES": "Balance"}, "EnNewsMode": {"NEWS_MODE_FULL": "Full Packege", "NEWS_MODE_HEADERS": "Topics Only", "NEWS_MODE_DISABLED": "Disabled"}, "EnHistoryLimit": {"TRADE_HISTORY_ALL": "The entire history", "TRADE_HISTORY_MONTHS_1": "One month", "TRADE_HISTORY_MONTHS_3": "Three months", "TRADE_HISTORY_MONTHS_6": "Six months", "TRADE_HISTORY_YEAR_1": "One year", "TRADE_HISTORY_YEAR_2": "Two years", "TRADE_HISTORY_YEAR_3": "Three years"}, "EnTradeFlags": {"TRADEFLAGS_NONE": "Disabled", "TRADEFLAGS_SWAPS": "Allow charging of swaps", "TRADEFLAGS_TRAILING": "Enable trailing stop", "TRADEFLAGS_EXPERTS": "Enable trading using Expert Advisors", "TRADEFLAGS_EXPIRATION": "Enable order expiration", "TRADEFLAGS_SIGNALS_ALL": "Enable All Signals From All Brokers", "TRADEFLAGS_SIGNALS_OWN": "Enable Signals From My Servers Only", "TRADEFLAGS_SO_COMPENSATION": "Automatically execute DEAL_SO_COMPENSATION operation on negative balance after Stop Out", "TRADEFLAGS_SO_FULLY_HEDGED": "Perform Stop out on accounts with zero margin and negative equity", "TRADEFLAGS_FIFO_CLOSE": "Close positions by FIFO rule", "TRADEFLAGS_HEDGE_PROHIBIT": "Prohibit opening of opposite positions and orders", "TRADEFLAGS_DEAL_COST": "Calculate deal execution costs and display them in client terminals", "TRADEFLAGS_SO_COMPENSATION_CREDIT": "Withdraw credit funds on negative balance compensation", "TRADEFLAGS_ALL": "All"}, "EnTransferMode": {"TRANSFER_MODE_DISABLED": "Disable Transfer of Funds Between Accounts", "TRANSFER_MODE_NAME": "Enable Transfer of Funds Between Accounts with Matching Names", "TRANSFER_MODE_GROUP": "Enable Transfer of Funds Between Accounts with Matching Group/Subgroup", "TRANSFER_MODE_NAME_GROUP": "Enable Transfer of Funds Between Accounts with Matching Names and Group/Subgroup"}, "EnMarginMode": {"MARGIN_MODE_RETAIL": "Used For Retail Forex, CFD, and Futures", "MARGIN_MODE_EXCHANGE_DISCOUNT": "For Stock Exchange, Based on Margin Discount Rates", "MARGIN_MODE_RETAIL_HEDGED": "For Retail Forex, CFD, and Futures With Hedged Positions"}, "EnCommRangeMode": {"COMM_RANGE_VOLUME": "Volume", "COMM_RANGE_TURNOVER_MONEY": "Turnover Money", "COMM_RANGE_TURNOVER_VOLUME": "Turnover Volume"}, "EnCommChargeMode": {"COMM_CHARGE_INSTANT": "Instant", "COMM_CHARGE_MONTHLY": "Monthly", "COMM_CHARGE_DAILY": "Daily"}, "EnCommEntryMode": {"COMM_ENTRY_ALL": "All", "COMM_ENTRY_IN": "In", "COMM_ENTRY_OUT": "Out"}, "EnCommActionMode": {"COMM_ACTION_ALL": "All", "COMM_ACTION_BUY": "Buy", "COMM_ACTION_SELL": "<PERSON>ll"}, "EnCommProfitMode": {"COMM_PROFIT_ALL": "All", "COMM_PROFIT_PROFIT": "Profit", "COMM_PROFIT_LOSS": "Loss"}, "EnCommReasonFlags": {"COMM_REASON_FLAG_NONE": "No commission", "COMM_REASON_FLAG_CLIENT": "Client", "COMM_REASON_FLAG_EXPERT": "Expert", "COMM_REASON_FLAG_DEALER": "Dealer", "COMM_REASON_FLAG_EXTERNAL_CLIENT": "External", "COMM_REASON_FLAG_MOBILE": "Mobile", "COMM_REASON_FLAG_WEB": "Web", "COMM_REASON_FLAG_SIGNAL": "Signal", "COMM_REASON_FLAG_ALL": "All"}, "EnCommissionMode": {"COMM_MONEY_DEPOSIT": "Deposit CCY", "COMM_MONEY_SYMBOL_BASE": "Base, CCY", "COMM_MONEY_SYMBOL_PROFIT": "Profit, CCY", "COMM_MONEY_SYMBOL_MARGIN": "Margin, CCY", "COMM_PIPS": "Points", "COMM_PERCENT": "Percent", "COMM_MONEY_SPECIFIED": "Specified CCY", "COMM_PERCENT_PROFIT": "Profit <PERSON><PERSON>"}, "EnCommissionVolumeType": {"COMM_TYPE_DEAL": "Deal", "COMM_TYPE_VOLUME": "Volume"}, "EnReportsMode": {"REPORTS_DISABLED": "Reports are disabled", "REPORTS_FULL": "Enable both end-of-day and end-of-month data", "REPORTS_DAY_ONLY": "Enable for end of the day", "REPORTS_MONTH_ONLY": "Enable for end of the month"}, "TreeSelect": {"Placeholder": "Select Group"}, "EnStopOutMode": {"STOPOUT_PERCENT": "Percent", "STOPOUT_MONEY": "Money"}, "EnFreeMarginMode": {"FREE_MARGIN_USE_PL": "Use Unrealized Profit/Loss", "FREE_MARGIN_NOT_USE_PL": "Don't Use Unrealized Profit/Loss", "FREE_MARGIN_PROFIT": "Use Unrealized Profit", "FREE_MARGIN_LOSS": "Use Unrealized Loss"}, "EnMarginFreeProfitFlags": {"FREE_MARGIN_PROFIT_PL": "Use Daily Fixed Profit/Loss", "FREE_MARGIN_PROFIT_LOSS": "Use Daily Fixed Loss"}}}, "Accounts": {"MT4": {"Crud": {"account": "account", "please-fix-the-form-errors": "Please fix form errors and try again", "personal": "Personal", "trades": "Trades", "security": "Security", "create-account": "Create Account", "update-account": "Update \"{0}\" Account", "address": "Address", "info": "Info", "Address": {"city": "City", "country": "Country", "state": "State", "zip-code": "Zip Code", "address": "Address"}, "Info": {"registration-date": "Registration Date:", "last-seen-online": "Last Seen Online:", "last-ip": "Last IP:", "not-available": "Not Available", "previous-balance": "Previous Balance:", "previous-equity": "Previous Equity:", "previous-month-equity": "Previous Month Equity:", "meta-quote-id": "Meta Quote ID:", "balance": "Balance:"}, "Personal": {"name": "Name", "password": "Password", "investor-password": "Investor Password", "id-number": "ID Number", "login": "<PERSON><PERSON>", "phone-password": "Phone Password", "status": "Status", "color": "Color", "group": "Group", "leverage": "Leverage", "tax-rate": "Tax Rate", "agent-account": "Agent Account", "lead-source": "Lead Source", "enable": "Enable", "comment": "Comment", "read-only": "Read Only", "allow-password-change": "Allow Password Change", "send-reports": "Send Reports", "one-time-password": "One-time Password", "sponsored-mt-virtual-host": "Sponsored MT Virtual Hosting"}, "Security": {"check-password": "Check Password", "checking-password-allows-": "Checking Password allows to verify Master Account Password.", "password": "Password", "check-password-0": "Check Password", "change-password": "Change Password", "you-can-change-master-or-": "You can change Master or Investor passwords for the account. Password must be complex enough, at least 5 symbols long and at least two or three character types present - lowercase, uppercase, digit.", "you-can-also-disable-one-": "You can also disable one-time password usage for the account. If forced OTP usage is enabled for the group, the user will be requested to bind his account to the one-time password generator on the next connection to the server.", "unbind-account-from-one-t": "Unbind Account From One-time Password Generator", "change-read-only-investor": "Change Read Only (investor) Password", "new-password": "New Password", "change-password-0": "Change Password", "password-must-be-at-least-5-characters-long": "Password strength is low", "password-changed": "Password has been changed successfully", "password-verified": "Password is correct"}, "securities": "Securities", "permissions": "Permissions"}}, "MT5": {"Language": {"Afrikaans": "Afrikaans", "Albanian": "Albanian", "Arabic": "Arabic", "Armenian": "Armenian", "Azerbaijani": "Azerbaijani", "Basque": "Basque", "Belarusian": "Belarusian", "Bulgarian": "Bulgarian", "ChineseTraditional": "ChineseTraditional", "Croatian": "Croatian", "Czech": "Czech", "Danish": "Danish", "Dutch": "Dutch", "English": "English", "Estonian": "Estonian", "Finnish": "Finnish", "French": "French", "Georgian": "Georgian", "German": "German", "Greek": "Greek", "Hebrew": "Hebrew", "Hindi": "Hindi", "Hungarian": "Hungarian", "Indonesian": "Indonesian", "Italian": "Italian", "Japanese": "Japanese", "Korean": "Korean", "Latvian": "Latvian", "Lithuanian": "Lithuanian", "Macedonian": "Macedonian", "Norwegian": "Norwegian", "Oriya": "Oriya", "Persian": "Persian", "Polish": "Polish", "Portuguese": "Portuguese", "Romanian": "Romanian", "Russian": "Russian", "Slovak": "Slovak", "Slovenian": "Slovenian", "Spanish": "Spanish", "Swedish": "Swedish", "Tatar": "Tatar", "Thai": "Thai", "Turkish": "Turkish", "Ukrainian": "Ukrainian", "Urdu": "Urdu", "Vietnamese": "Vietnamese"}, "Crud": {"account": "account", "login": "<PERSON><PERSON>", "group": "Group", "existing-client": "Existing Client", "first-name": "First Name", "last-name": "Last Name", "middle-name": "Middle Name", "master-password": "Master Password", "the-password-strength-is-low": "The password strength is low", "investor-password": "Investor Password", "phone-password": "Phone Password", "company": "Company", "phone": "Phone", "email": "Email", "city": "City", "country": "Country", "zipcode": "Zipcode", "state": "State", "address": "Address", "groups": "Groups", "color": "Color", "leverage": "Leverage", "enable-this-account": "Enable This Account", "enable-password-change": "Enable Password Change", "enable-one-time-password": "Enable One-time Password", "change-password-at-next-login": "Change Password at Next Login", "show-to-regular-managers": "Show To Regular Managers", "include-in-server-reports": "Include in Server Reports", "enable-daily-reports": "Enable Daily Reports", "enable-trading": "Enable Trading", "enable-algo-trading-by-ex": "Enable Algo Trading by Expert Advisors", "enable-trailing-stops": "Enable Trailing stops", "enable-api-connections": "Enable API Connections", "enable-sponsored-vps-host": "Enable Sponsored VPS Hosting", "allow-access-to-subscript": "Allow Access to Subscriptions Data via Data Feeds", "limit-number-of-active-or": "Limit Number of Active Orders", "limit-total-value-of-position": "Limit Total Value of Positions", "default": "<PERSON><PERSON><PERSON>", "general": "General", "language": "Language", "id-number": "ID Number", "metaquote-id": "MetaQuote ID", "status": "Status", "lead-campaign": "Lead Campaign", "lead-source": "Lead Source", "registered": "Registered", "comment": "Comment", "master-password-is-used-for": "Master Password is used for full access to the trading account.", "check": "Check", "change": "Change", "investor-password-is-used-for": "Investor Password is used for limited access to the trading account in read-only mode.", "api-password": "API Password", "api-password-is-used-for": "API Password is used for access to the server using Web API.", "phone-password-and-otp": "Phone Password and OTP Secret Key", "otp-secret-key": "OTP Secret Key", "update-account": "Update \"{0}\" Account", "personal": "Personal", "limits": "Limits", "Manager": {}}, "Manager": {"common": "Common", "permissions": "Permissions", "reports": "Reports", "ipAccessList": "IP Access List", "create-item": "Create \"{0}\" {1}", "update-item": "Update \"{0}\" {1}"}, "Security": {"password-incorrect": "Password is Incorrect"}}, "InfoTooltip": {"group": "Group", "company": "Company", "city": "City", "email": "Email", "balance": "Balance", "there-is-no-account-with-": "There is no account with login: {0}", "couldnt-load-account-info": "Couldn't Load Account Info", "it-could-be-the-account-h": "It could be the Account has been deleted.", "edit": "Edit Account", "create": "Create Account"}}, "accounts": {"login": "<PERSON><PERSON>", "name": "Name", "group": "Group", "city": "City", "email": "Email", "agent": "Agent", "accounts": "Accounts", "create-account": "Create Account", "manager": "Manager", "create-manger": "Create Manager", "groups": "Groups", "mailbox": "Mailbox", "rights": "Rights", "hide-rights": "Hide Rights", "show-rights": "Show Rights", "logins": "<PERSON><PERSON>", "rows": "Rows", "copied": "Data has been copied succesfully", "copy-as": "<PERSON><PERSON> As"}, "Manager": {"MT4": {"Crud": {"Manager": {"common": "Common"}, "common": "Common", "Common": {"login": "<PERSON><PERSON>", "data-available-for": "Data Available For", "groups": "Groups", "from": "From", "to": "To", "mailbox-name": "Mailbox Name", "ip-filter": "IP Filter", "unlimited": "Unlimited"}, "Permissions": {"manager": "Manager", "supervise-trades": "Supervise Trades", "administrator": "Administrator", "accountant": "Accountant", "reports": "Reports", "risk-manager": "Risk Manager", "internal-mail-system": "Internal Mail System", "journals": "Journals", "send-news": "Send News", "market-watch": "Market Watch", "connections-show-online-c": "Connections (Show Online Clients)", "personal-details": "Personal Details", "configure-server-plugins": "Configure Server Plugins", "automatic-server-reports": "Automatic Server Reports", "access-to-app-market": "Access to App Market", "push-notification": "Push Notification", "access-to-technical-support": "Access to Technical Support", "roles": "Roles", "save": "Save", "delete": "Delete"}, "Securities": {"dealer": "Dealer", "edit-delete-trades": "Edit/Delete Trades", "type": "Type", "use": "Use", "min-lots": "Min. Lots", "max-lots": "Max. Lots"}}}, "MT5": {"EnManagerLimit": {"MANAGER_LIMIT_ALL": "All", "MANAGER_LIMIT_MONTHS_1": "1 month", "MANAGER_LIMIT_MONTHS_3": "3 months", "MANAGER_LIMIT_MONTHS_6": "6 months", "MANAGER_LIMIT_YEAR_1": "1 year", "MANAGER_LIMIT_YEAR_2": "2 years", "MANAGER_LIMIT_YEAR_3": "3 years"}, "Crud": {"Common": {"mailbox-name": "Mailbox Name", "login": "<PERSON><PERSON>", "groups": "Groups", "all-groups": "All"}, "IPAccessList": {"add-record": "Add Record", "from": "From", "to": "To", "access-list": "Access List", "cancel": "Cancel", "are-you-sure-you-want-to-delete": "Are you sure you want to delete this record?"}, "Permissions": {"available-logs": "Available Logs", "available-reports": "Available Reports", "permissions": "Permissions", "close-all": "Close All", "open-all": "Open All", "connection-types": "Connection Types", "connect-using-metatrader-5-administrator": "Connect Using MetaTrader 5 Administrator", "connect-using-metatrader-5-manager": "Connect Using MetaTrader 5 Manager", "configurations-setup": "Configurations Setup", "configure-network": "Configure Network", "configure-integrations": "Configure Integrations", "configure-sponsored-vps": "Configure Sponsored VPS", "configure-mail-server": "Configure Mail Server", "configure-sms-gateways-messenger-channels": "Configure SMS Gateways & Messenger Channels", "configure-kyc": "Configure KYC", "configure-payments": "Configure Payments", "configure-web-services": "Configure Web Services", "configure-ip-access-list": "Configure IP Access List", "configure-automation": "Configure Automation", "configure-operation-time": "Configure Operation Time", "configure-holidays": "Configure Holidays", "configure-leverages": "Configure Leverages", "configure-groups": "Configure Groups", "configure-allocations": "Configure Allocations", "configure-corporate-links": "Configure Corporate Links", "configure-managers-permissions": "Configure Managers Permissions", "configure-request-routing": "Configure Request Routing", "configure-gateways": "Configure Gateways", "configure-plugins": "Configure <PERSON>", "configure-data-feeds": "Configure Data Feeds", "configure-reports": "Configure Reports", "configure-symbols": "Configure Symbols", "configure-history-charts-synchronization": "Configure History Charts Synchronization", "configure-ecn": "Configure ECN", "configure-funds-etf": "Configure Funds & ETF", "administration": "Administration", "access-server-logs": "Access Server Logs", "receive-automatic-server-reports": "Receive Automatic Server Reports", "edit-charts": "Edit Charts", "send-emails": "Send Emails", "send-news": "Send News", "export-data": "Export Data", "manage-servers-machines": "Manage Servers Machines", "accounts": "Accounts", "accountant-balance-operations": "Accountant (Balance Operations)", "access-accounts": "Access Accounts", "view-technical-accounts": "View Technical Accounts", "manage-technical-accounts": "Manage Technical Accounts", "access-the-account-personal-details": "Access The Account Personal Details", "edit-accounts": "Edit Accounts", "delete-accounts": "Delete Accounts", "view-currently-connected-clients": "View Currently Connected Clients", "confirm-dangerous-actions": "Confirm Dangerous Actions", "push-notifications": "Push Notifications", "dealing": "Dealing", "access-orders-positions": "Access Orders & Positions", "edit-orders-positions-deals": "Edit Orders, Positions & Deals", "delete-orders-positions-deals": "Delete Orders, Positions & Deals", "dealer": "Dealer", "supervisor": "Supervisor", "show-raw-quotes-without-spread-difference": "Show Raw Quotes Without Spread Difference", "throw-in-quotes": "Throw In Quotes", "modify-spread-execution-mode": "Modify Spread & Execution Mode", "risk-manager": "Risk Manager", "edit-groups-margin-settings": "Edit Groups (<PERSON><PERSON>)", "edit-groups-commission-settings": "Edit Groups (Commission Settings)", "receive-reports": "Receive Reports", "payments": "Payments", "access-payments": "Access Payments", "process-payments": "Process Payments", "edit-payments": "Edit Payments", "delete-payments": "Delete Payments", "back-office": "Back Office", "clients": "Clients", "access-clients": "Access Clients", "create-clients": "Create Clients", "edit-clients": "Edit Clients", "delete-clients": "Delete Clients", "kyc-check": "KYC Check", "documents": "Documents", "access-documents": "Access Documents", "create-documents": "Create Documents", "edit-documents": "Edit Documents", "delete-documents": "Delete Documents", "add-files-to-documents": "Add Files to Documents", "delete-files-from-documents": "Delete Files From Documents", "comments": "Comments", "access-comments": "Access Comments", "add-comments": "Add Comments", "delete-comments": "Delete Comments", "finteza": "Fin<PERSON>za", "access-finteza": "Access Finteza", "view-website": "View Website", "view-campaigns": "View Campaigns", "view-reports": "View Reports", "subscriptions": "Subscriptions", "view-subscriptions": "View Subscriptions", "edit-subscriptions": "Edit Subscriptions", "save": "Save As", "delete": "Delete", "roles": "Roles"}, "manager": "Manager", "common": "Common", "permissions": "Permissions", "reports": "Reports", "ipAccessList": "IP Access List", "please-fix-the-form-errors": "Please fix form errors and try again", "Reports": {"add-report": "Add Report", "name": "Name", "view": "View", "export": "Export", "history": "History", "are-you-sure-you-want-to-remove": "Are you sure you want to remove this report?"}}}, "InfoTooltip": {"mailbox": "Mailbox", "groups": "Groups", "create": "Create Manager", "edit": "Edit Manager", "couldnt-load-manager-info": "Couldn't Load Manager Info", "it-could-be-the-manager-deleted": "It could be the Manager has been deleted.", "there-is-no-manager-with-": "There is no manager with login: {0}"}}, "GroupTooltip": {"company": "Company: ", "currency": "Currency: ", "margin": "Margin: ", "edit-group": "Edit Group"}, "ManagerInfoTooltip": {}, "InfoTooltip": {}, "change-password": {"title": "Change Password", "save": "Save", "cancel": "Cancel", "password-does-not-match": "Password does not match", "password-changed": "Password has been changed successfully", "secure-your-account-description": "Secure your account by updating your password frequently.{br} Mix letters, numbers, and symbols for a higher security."}, "Widgets": {"there-are-no-widgets-available": "There are no widgets available. or you don't have permission to view any widgets."}, "PathTreeItem": {"add-directory": "Add Directory", "delete-directory": "Delete Directory"}, "prices-history": {"1-min-history": "Prices History", "export-data": "Export Data", "symbol": "Symbol", "from": "From", "to": "To", "the-data-is-being-exported": "The data is being exported. You will be notified by email when it is ready.", "exporting-data": "Exporting data...", "open-price": "Open Price", "close-price": "Close Price", "high-price": "High Price", "low-price": "Low Price", "tick-volume": "Tick Volume", "date": "Date (MT Timezone)", "use-mt-timezone": "Use MT timezone"}}