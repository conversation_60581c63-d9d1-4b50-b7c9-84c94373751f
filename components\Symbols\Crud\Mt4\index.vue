<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Symbols.MT4.Crud.symbol')"
		item-identity="symbol"
		:navigation-drawer-props="{ width: 900 }"
		url="/symbols/mt4/:symbol"
		:validation-callback="validate"
		update-method="POST"
		update-url="/symbols/mt4"
		v-bind="$attrs"
		@closed="closeHandler"
		@loaded="loadedSymbol = $event.symbol"
	>
		<template #title="{ isUpdateAction, item, itemName }">
			<div v-if="isUpdateAction && item.symbol !== loadedSymbol">
				{{ $t('Symbols.MT4.Crud.create-item-symbol', [item.symbol, itemName]) }}
			</div>
			<div v-else-if="isUpdateAction">
				{{ $t('Symbols.MT4.Crud.update-item-symbol-itemna', [item.symbol, itemName]) }}
			</div>
		</template>
		<template #default="{ item, errors }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-3 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class="flex-grow-1"
						>
							<v-tabs-window-item
								v-for="tab in tabs"
								:key="tab.value"
								:value="tab.value"
								:eager="isValidated"
							>
								<v-form
									ref="tabForm"
									v-model="tab.isValid.value"
								>
									<component
										:is="tab.component"
										:loaded-symbol="loadedSymbol"
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-form>
							</v-tabs-window-item>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Symbol from './Symbol.vue'
import Filtration from './Filtration.vue'
import Calculation from './Calculation.vue'
import Swaps from './Swaps.vue'
import Sessions from './Sessions.vue'
import type { Symbols } from '~/types'
import Crud from '~/components/Crud.vue'
import type { DefaultItem } from '~/components/Crud.vue'

const loadedSymbol = ref<string | null>(null)

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem< Symbols.MT4._Id.POSTRequest | Symbols.MT4._Id.PUTRequest> = {
	symbol: '',
	description: '',
	source: '',
	currency: 'USD',
	type: 0,
	digits: 4,
	trade: 2,
	backgroundColor: **********,
	count: -1,
	countOriginal: 0,
	externalUnused: [
		0,
		0,
		0,
		0,
		0,
		0,
		0,
	],
	realtime: 1,
	starting: 0,
	expiration: 0,
	sessions: [
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 24,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
		{
			quote: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			trade: [
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
				{
					openHour: 0,
					openMin: 0,
					closeHour: 0,
					closeMin: 0,
				},
			],
			reserved: [0, 0],
		},
	],
	profitMode: 0,
	profitReserved: 0,
	filter: 20,
	filterCounter: 3,
	filterLimit: 0.1,
	filterSmoothing: 0,
	filterReserved: 0,
	logging: 0,
	spread: 5,
	spreadBalance: 0,
	exemode: 0,
	swapEnable: 1,
	swapType: 0,
	swapLong: 0,
	swapShort: 0,
	swapRollover3Days: 3,
	contractSize: 100000,
	tickValue: 0,
	tickSize: 0,
	stopsLevel: 10,
	gtcPendings: 1,
	marginMode: 0,
	marginInitial: 0,
	marginMaintenance: 0,
	marginHedged: 0,
	marginDivider: 1,
	point: 0.0001,
	multiply: 10000,
	bidTickValue: 0,
	askTickValue: 0,
	longOnly: 0,
	instantMaxVolume: 0,
	marginCurrency: '',
	freezeLevel: 0,
	marginHedgedStrong: 0,
	valueDate: 0,
	quotesDelay: 0,
	swapOpenPrice: 0,
	swapVariationMargin: 0,
	unused: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
} satisfies DefaultItem<Symbols.MT4._Id.POSTRequest | Symbols.MT4._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const tabModel = ref('common')

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs: Tab[] = ([
	{
		value: 'symbol',
		text: t('Symbols.MT4.Crud.symbol'),
		component: Symbol,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[0]?.errors.length || 0),
	},
	{
		value: 'filtration',
		text: t('Symbols.MT4.Crud.filtration'),
		component: Filtration,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[1]?.errors.length || 0),
	},
	{
		value: 'calculation',
		text: t('Symbols.MT4.Crud.calculation'),
		component: Calculation,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[2]?.errors.length || 0),
	},
	{
		value: 'swaps',
		text: t('Symbols.MT4.Crud.swaps'),
		component: Swaps,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[7]?.errors.length || 0),
	},
	{
		value: 'sessions',
		text: t('Symbols.MT4.Crud.sessions'),
		component: Sessions,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[8]?.errors.length || 0),
	},
])

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error(t('Symbols.MT4.Crud.please-fix-the-form-errors')))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs[0].value

	loadedSymbol.value = null
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: () => crudRef.value!.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
