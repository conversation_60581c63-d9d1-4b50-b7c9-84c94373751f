<template>
	<v-container>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.Margin.margin-values') }}</legend>
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginInitial"
						:error-messages="errors.marginInitial"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT5.Crud.Margin.initial-margin')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginHedged"
						:disabled="marginFlagsModel.includes(Symbols.MT5.MarginFlags.MARGIN_FLAGS_HEDGE_LARGE_LEG)"
						:error-messages="errors.marginHedged"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT5.Crud.Margin.hedged-margin')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginMaintenance"
						:error-messages="errors.marginMaintenance"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT5.Crud.Margin.maintenance-margin')"
					/>
				</v-col>
			</v-row>
		</fieldset>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.Margin.settings') }}</legend>
			<v-checkbox
				v-model="marginFlagsModel"
				hide-details
				color="primary"
				class="ms-n2 compact"
				:label="$t('Symbols.MT5.Crud.Margin.calculate-hedged-margin-u')"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_HEDGE_LARGE_LEG"
				:false-value="undefined"
			/>
			<v-checkbox
				v-model="marginFlagsModel"
				hide-details
				color="primary"
				class="ms-n2 compact"
				:label="$t('Symbols.MT5.Crud.Margin.exclude-long-position-pnl')"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_EXCLUDE_PL"
				:false-value="undefined"
			/>
			<v-checkbox
				v-model="marginFlagsModel"
				hide-details
				color="primary"
				class="ms-n2 compact mb-4"
				:label="$t('Symbols.MT5.Crud.Margin.recalculate-margin')"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_RECALC_RATES"
				:false-value="undefined"
			/>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						:model-value="marginFlagsModel.filter(value => marginFlagsItems.some((item) => item.value === value))"
						multiple
						chips
						closable-chips
						:items="marginFlagsItems"
						:label="$t('Symbols.MT5.Crud.Margin.additional-margin-checks')"
						@update:model-value="updateMarginFlagsSelectHandler"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import type { Props, Emit } from './Shared'
import { defaults } from './Shared'
import { Symbols } from '~/types'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const marginFlagsSelectItemsValues = ref([Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_SLTP, Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_PROCESS])
const marginFlagsItems = enumToItems(Symbols.MT5.MarginFlags, 'Symbols.MT5.MarginFlags',
	item => marginFlagsSelectItemsValues.value.includes(item.value)
		? item
		: undefined,
)

const computedMarginFlags = computed({
	get: () => p.item.marginFlags,
	set: (value: number) => {
		emit('update:item', { marginFlags: value })
	},
})

const { model: marginFlagsModel } = useMultiSelectEnum(computedMarginFlags, Symbols.MT5.MarginFlags)

const checkboxesValues = computed(() => {
	return marginFlagsModel.value.filter(value => !marginFlagsSelectItemsValues.value.includes(value))
})

const updateMarginFlagsSelectHandler = (value: number[]) => {
	marginFlagsModel.value = [...value, ...checkboxesValues.value]
}
</script>
