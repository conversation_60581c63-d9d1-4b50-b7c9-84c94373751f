export namespace Journals {
	export enum Level {
		/// <summary>
		/// Logs that contain the most detailed messages. These messages may contain sensitive application data.
		/// These messages are disabled by default and should never be enabled in a production environment.
		/// </summary>
		Trace = 0,

		/// <summary>
		/// Logs that are used for interactive investigation during development.  These logs should primarily contain
		/// information useful for debugging and have no long-term value.
		/// </summary>
		Debug = 1,

		/// <summary>
		/// Logs that track the general flow of the application. These logs should have long-term value.
		/// </summary>
		Information = 2,

		/// <summary>
		/// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
		/// application execution to stop.
		/// </summary>
		Warning = 3,

		/// <summary>
		/// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
		/// failure in the current activity, not an application-wide failure.
		/// </summary>
		Error = 4,

		/// <summary>
		/// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
		/// immediate attention.
		/// </summary>
		Critical = 5,

		/// <summary>
		/// Not used for writing log messages. Specifies that a logging category should not write any messages.
		/// </summary>
		None = 6,
	}

	export enum HttpAction {
		GET = 0,
		POST = 1,
		PUT = 2,
		DELETE = 3,
	}

	export interface Record {
		id: string
		userId: number
		email: string
		ipAddress: string
		message: string
		source: string
		timestamp: number
		level: Level
		filters: Filters
		changes: Change[]
		action: HttpAction
		createdAt: Date
		macAddress: string
	}

	export interface Change {
		name: string
		oldValue: string
		newValue: string
	}

	export interface Filters {
	}

	export type GETRequest = {
		items: Record[]
	}

	// export namespace _Id {
	// 	export const URL = (_Id: number | string) => `/transactions/${_Id}`
	// 	export interface GETResponse {
	// 		id: number
	// 		actionId: number
	// 		actionName: string
	// 		tradingServerId: number
	// 		tradingServerName: null
	// 		receiverAccount: string
	// 		senderAccount: null
	// 		requestedBy: number
	// 		requestedByUserName: string
	// 		requestedUserRoleName: string
	// 		requestedAmount: number
	// 		receivedAmount: number
	// 		requestedCurrencyId: number
	// 		requestedCurrencySymbol: null
	// 		receivedCurrencyId: number
	// 		receivedCurrencySymbol: null
	// 		exchangeRate: number
	// 		status: Status
	// 		statusName: string
	// 		executedBy: number
	// 		canExecute: boolean
	// 		executedAt: string
	// 		createdAt: string
	// 		updatedAt: null
	// 	}

	// 	export interface PUTRequest {
	// 		status: number
	// 		comment: string
	// 	}
	// 	export interface Account {
	// 		login: string
	// 		group: string
	// 		currency: string
	// 	}
	// 	export interface POSTRequest {
	// 		actionId: number
	// 		toAccount: Account
	// 		fromAccount?: Account
	// 		amount: number
	// 	}

	// 	export namespace Workflow {
	// 		export enum Event {
	// 			Reviewer = 'Reviewer',
	// 			Approval = 'Approval',
	// 			Fallback = 'Fallback',
	// 			Requester = 'Requester',
	// 		}

	// 		export const URL = (_Id: number | string) => `${Transactions._Id.URL(_Id)}/workflow`
	// 		export interface SingleRecord {
	// 			id: number
	// 			comment: string
	// 			flowableType: Event
	// 			roleName: string
	// 			userName: string
	// 			status: Status
	// 			processedAt: string
	// 		}

	// 		export type GETResponse = SingleRecord[]
	// 	}
	// }

}
