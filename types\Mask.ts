import type { MaskInputOptions } from 'maska'

type Mask = {
	IP: MaskInputOptions
	TwoDecimals: MaskInputOptions
	Integer: MaskInputOptions
	HourMinute: MaskInputOptions
	HourMinuteToHourMinute: MaskInputOptions
}
export const Mask = Object.freeze<Mask>({
	IP:	{
		mask: '#XX.#XX.#XX.#XX',
		tokens: {
			X: { pattern: /\d/, optional: true },
		},
		eager: true,
	},
	TwoDecimals: {
		mask: '9#.##',
		tokens: {
			'#': { pattern: /\d/ },
			'9': { pattern: /\d/, repeated: true, optional: true },
		},
		eager: true,
		// postProcess(value) {
		// 	console.log('🚀 ~ postProcess ~ value:', value)
		// 	const formattedValue = Number(value).toFixed(2)
		// 	console.log('postProcess', formattedValue)
		// 	return formattedValue
		// },
		preProcess(value) {
			if ((value === '.')) {
				value = '0.'
			}

			return value
		},
		number: {
			fraction: 2,
			locale: 'en-US',
			unsigned: true,
		},
		reversed: true,
	},
	Integer: {
		mask: '9#',
		tokens: {
			'#': { pattern: /\d/ },
			'9': { pattern: /\d/, repeated: true, optional: true },
		},
		eager: true,
		reversed: true,
	},
	HourMinute: {
		mask: 'Hh:M#',
		tokens: {
			'#': { pattern: /\d/ },
			'H': { pattern: /[0-2]/ },
			'h': { pattern: /[0-4]/ },
			'M': { pattern: /[0-5]/ },
		},
		eager: true,
	},
	HourMinuteToHourMinute: {
		mask: 'Hh:M# - Hh:M#',
		tokens: {
			'#': { pattern: /\d/ },
			'H': { pattern: /[0-2]/ },
			'h': { pattern: /[0-4]/ },
			'M': { pattern: /[0-5]/ },
		},
		eager: true,
	},

})
