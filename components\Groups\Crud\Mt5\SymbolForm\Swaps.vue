<template>
	<v-defaults-provider
		:defaults="{
			global: {
				disabled: !isSwapEnabled,
				returnObject: false,
			},
		}"
	>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<fieldset>
					<legend>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.settings') }}</legend>
					<DefaultField
						is="VSelect"
						v-model="item.swapMode"
						v-model:default="item.swapModeDefault"
						:default-value="-1"
						:disabled="false"
						:error-messages="errors.swapMode"
						:items="swapModeItems"
						:rules="rules({ required: true })"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.type')"
					/>
					<DefaultField
						is="VCombobox"
						v-model:default="item.swapLongDefault"
						v-model.number="item.swapLong"
						:default-value="1.7976931348623157e+308"
						:error-messages="errors.swapLong"
						:return-object="false"
						:rules="rules({ required: true })"
						type="number"
						:items="[]"
						hide-spin-buttons
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.long-positions')"
					/>
					<DefaultField
						is="VCombobox"
						v-model.number="item.swapShort"
						v-model:default="item.swapShortDefault"
						:default-value="1.7976931348623157e+308"
						:return-object="false"
						:error-messages="errors.swapShort"
						:rules="rules({ required: true })"
						:items="[]"
						type="number"
						hide-spin-buttons
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.short-positions')"
					/>

					<DefaultField
						is="VCombobox"
						v-model.number="item.swapYearDays"
						v-model:default="item.swapYearDaysDefault"
						:default-value="4294967295"
						:return-object="false"
						:items="[]"
						type="number"
						hide-spin-buttons
						:error-messages="errors.swapYearDays"
						:rules="rules({
							required: item.swapYearDays !== -1,
							type: item.swapYearDays === -1?'string':'number',
							min: 182,
							max: 366,
						})"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.days-in-year')"
					/>
					<DefaultField
						is="VCombobox"
						v-model="item.swapFlags"
						v-model:default="item.swapFlagsDefault"
						:default-value="4294967295"
						color="primary"
						:return-object="false"
						:items="swapFlagsItems"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.automatically-consider-ho')"
					/>
				</fieldset>

				<v-btn
					density="default"
					variant="text"
					class="mt-4"
					block
					@click="useDefaultSwapSettings"
				>
					{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.use-default-swap-settings') }}
				</v-btn>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<fieldset>
					<legend>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.swap-multipliers') }}</legend>
					<v-btn
						variant="text"
						density="default"
						block
						append-icon="i-mdi:menu-down"
					>
						{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.set-standard-settings') }}	<v-menu
							v-slot="{ isActive: isMenuActive }"
							activator="parent"
						>
							<v-list>
								<v-list-item
									:title="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.forex')"
									@click="setAsForex"
								/>
								<v-list-item
									:title="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.all-week')"
									@click="setAllWeek"
								/>
								<v-list-item
									:title="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.from-symbol')"
									link
								>
									<v-dialog
										v-slot="{ isActive }"
										activator="parent"
										max-width="400"
									>
										<v-card :title="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.choose-symbol')">
											<v-card-text>
												<symbols-crud-symbols-tree
													v-model="selectedSymbol"
													:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.symbol')"
													@update:model-value="fetchSymbol"
												/>
											</v-card-text>

											<v-card-actions>
												<v-btn
													variant="text"
													@click="isActive.value = false;isMenuActive.value = false"
												>
													{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.cancel') }}
												</v-btn>
												<v-btn
													variant="text"
													:loading="isFetching"
													:disabled="isFetching"
													@click="saveFetchedSymbol();isActive.value = false;isMenuActive.value = false "
												>
													{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.save') }}
												</v-btn>
											</v-card-actions>
										</v-card>
									</v-dialog>
								</v-list-item>
							</v-list>
						</v-menu>
					</v-btn>

					<v-table>
						<thead :class="{ 'text-disabled': !isSwapEnabled }">
							<tr>
								<th>
									{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.day') }}
								</th>
								<th>
									{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.multiplier') }}
								</th>
							</tr>
						</thead>
						<tbody :class="{ 'text-disabled': !isSwapEnabled }">
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.sunday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateSundayDefault"
										v-model.number="item.swapRateSunday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.monday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateMondayDefault"
										v-model.number="item.swapRateMonday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.tuesday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateTuesdayDefault"
										v-model.number="item.swapRateTuesday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.wednesday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateWednesdayDefault"
										v-model.number="item.swapRateWednesday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.thursday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateThursdayDefault"
										v-model.number="item.swapRateThursday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.friday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateFridayDefault"
										v-model.number="item.swapRateFriday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
							<tr>
								<td>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Swaps.saturday') }}</td>
								<td>
									<DefaultField
										is="VCombobox"
										v-model:default="item.swapRateSaturdayDefault"
										v-model.number="item.swapRateSaturday"
										:default-value="1.7976931348623157e+308"
										:return-object="false"
										type="number"
										hide-spin-buttons
										single-line
										density="compact"
										hide-details
										flat
										variant="solo-filled"
										class="my-1"
									/>
								</td>
							</tr>
						</tbody>
					</v-table>
				</fieldset>
			</v-col>
		</v-row>
	</v-defaults-provider>
</template>

<script lang="ts" setup>
import pick from 'lodash/pick'
import type { Emit, Props } from './Shared'
import { defaults, defaultSymbol } from './Shared'
import DefaultField from './Components/DefaultField.vue'
import { Symbols } from '~/types'

type DaysSwapRate = {
	swapRateSunday: number
	swapRateMonday: number
	swapRateTuesday: number
	swapRateWednesday: number
	swapRateThursday: number
	swapRateFriday: number
	swapRateSaturday: number
}

const { $api } = useNuxtApp()

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const swapModeItems = enumToItems(Symbols.MT5.SwapMode, 'Symbols.MT5.SwapMode')

const swapFlagsItems = enumToItems(Symbols.MT5.SwapFlags, 'Symbols.MT5.SwapFlags')

const selectedSymbol = ref<string | undefined>()

const isFetching = ref(false)

const fetchedSymbol = ref<Symbols.MT5._Id.GETResponse | null>(null)

const isSwapEnabled = computed({
	get() {
		return p.item.swapMode !== Symbols.MT5.SwapMode.SWAP_DISABLED
	},
	set(value: boolean) {
		emit('update:item', {
			swapMode: value ? Symbols.MT5.SwapMode.SWAP_BY_POINTS : Symbols.MT5.SwapMode.SWAP_DISABLED,
		})
	},
})

const setSwapRate = (days: DaysSwapRate) => {
	emit('update:item', days)
}

const setAsForex = () => {
	setSwapRate({
		swapRateSunday: 0,
		swapRateMonday: 1,
		swapRateTuesday: 1,
		swapRateWednesday: 3,
		swapRateThursday: 1,
		swapRateFriday: 1,
		swapRateSaturday: 0,
	})
}

const setAllWeek = () => {
	setSwapRate({
		swapRateSunday: 1,
		swapRateMonday: 1,
		swapRateTuesday: 1,
		swapRateWednesday: 1,
		swapRateThursday: 1,
		swapRateFriday: 1,
		swapRateSaturday: 1,
	})
}

const fetchSymbol = (value: string) => {
	selectedSymbol.value = value
	isFetching.value = true
	$api<Symbols.MT5._Id.GETResponse>(Symbols.MT5._Id.URL(value))
		.then((symbol) => {
			fetchedSymbol.value = symbol
		})
		.finally(() => {
			isFetching.value = false
		})
}

const saveFetchedSymbol = () => {
	if (fetchedSymbol.value) {
		setSwapRate(
			{
				swapRateSunday: fetchedSymbol.value.swapRateSunday,
				swapRateMonday: fetchedSymbol.value.swapRateMonday,
				swapRateTuesday: fetchedSymbol.value.swapRateTuesday,
				swapRateWednesday: fetchedSymbol.value.swapRateWednesday,
				swapRateThursday: fetchedSymbol.value.swapRateThursday,
				swapRateFriday: fetchedSymbol.value.swapRateFriday,
				swapRateSaturday: fetchedSymbol.value.swapRateSaturday,
			},
		)
	}
}

const useDefaultSwapSettings = () => {
	const keys: (keyof typeof defaultSymbol)[] = [
		'swapMode',
		'swapLong',
		'swapShort',
		'swap3Day',
		'swapFlags',
		'swapYearDays',
		'swapRateSunday',
		'swapRateMonday',
		'swapRateTuesday',
		'swapRateWednesday',
		'swapRateThursday',
		'swapRateFriday',
		'swapRateSaturday',
	]
	const defaults = pick(defaultSymbol, keys)

	emit('update:item',
		{ ...defaults },
	)
}
</script>
