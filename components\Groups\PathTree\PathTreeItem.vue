<template>
	<v-lazy min-height="40">
		<v-list-group
			:value="item.value"
			expand-icon="i-mdi:plus"
			collapse-icon="i-mdi:minus"
			subgroup
			style="--prepend-width: 12px"
			class="flex-grow-1"
		>
			<template #activator="{ props, isOpen }">
				<v-list-item
					:title="item.title"
					class="px-1"
					@click="selectPath(item.value)"
				>
					<template #prepend>
						<v-btn
							:disabled="!item.children.length"
							:ripple="false"
							variant="text"
							size="x-small"
							:icon="isOpen?'i-mdi:minus':'i-mdi:plus'"
							v-bind="props"
							:class="{ 'opacity-0': !item.children.filter(i => i.type==='Path').length }"
						/>
						<v-icon icon="i-mdi:folder text-yellow-darken-1" />
					</template>
					<template #title="{ title }">
						{{ title }} <span class="text-caption">({{ item.totalItems }})</span>
					</template>
				</v-list-item>
			</template>

			<template
				v-for="(child, i) in item.children"
				:key="i"
			>
				<PathTreeItem
					v-if="child.type === 'Path'"
					:item="child"
					@click.stop="selectPath(child.value)"
					@path-select="selectPath"
				/>
			</template>
		</v-list-group>
	</v-lazy>
</template>

<script lang="ts" setup>
// import PathTreeItem from './PathTreeItem.vue'
import type { Groups } from '~/types'

type Props = {
	item: Groups.MT5.Paths.Lookup.SingleRecord
}

type Emit = {
	(e: 'path-select', value: string): void
}

defineProps<Props>()

const emit = defineEmits<Emit>()

const selectPath = (value: string) => {
	emit('path-select', value)
}
</script>
