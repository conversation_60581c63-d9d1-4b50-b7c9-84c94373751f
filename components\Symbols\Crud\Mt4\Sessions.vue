<template>
	<v-container>
		<v-row>
			<v-col cols="12">
				<fieldset>
					<legend>{{ $t('Symbols.MT4.Crud.Sessions.sessions-timing') }}</legend>

					<v-table hover>
						<thead>
							<tr>
								<th>
									{{ $t('Symbols.MT4.Crud.Sessions.day') }}
								</th>
								<th class="text-center">
									{{ $t('Symbols.MT4.Crud.Sessions.quote') }}
								</th>
								<th class="text-center">
									{{ $t('Symbols.MT4.Crud.Sessions.trade') }}
								</th>
								<th />
							</tr>
						</thead>
						<tbody>
							<tr
								v-for="day in daysOfWeek"
								:key="day.value"
								@dblclick="edit(day)"
							>
								<td>{{ day.text }}</td>
								<template v-if="isAllZeros(item.sessions[day.value].quote)">
									<td
										v-if="day.default"
										class="text-center"
									>
										{{ format(day.default.open) }} - {{ format(day.default.close) }}
									</td>
									<td
										v-else
										class="text-center"
									>
										-
									</td>
								</template>
								<td
									v-else
									class="text-center"
									v-html="formatRanges(item.sessions[day.value].quote) || '-'"
								/>

								<template v-if="isAllZeros(item.sessions[day.value].trade)">
									<td
										v-if="day.default"
										class="text-center"
									>
										{{ format(day.default.open) }} - {{ format(day.default.close) }}
									</td>
									<td
										v-else
										class="text-center"
									>
										-
									</td>
								</template>
								<td
									v-else
									class="text-center"
									v-html="formatRanges(item.sessions[day.value].trade) || '-'"
								/>								<td>
									<v-btn
										variant="text"
										icon="i-mdi:pencil"
										size="small"
										@click="edit(day)"
									/>
								</td>
							</tr>
						</tbody>
					</v-table>
				</fieldset>
			</v-col>
		</v-row>
		<v-row>
			<v-col>
				<v-checkbox
					v-model="isTimeLimitEnabled"
					:label="$t('Symbols.MT4.Crud.Sessions.use-time-limit')"
					color="primary"
					@update:model-value="timeLimitHandler"
				/>
			</v-col>
			<v-col>
				<date-time-input
					v-model="timeStart"
					:disabled="!isTimeLimitEnabled"
					prepend-inner-icon="i-mdi:calendar"
				/>
			</v-col>
			<v-col>
				<date-time-input
					ref="timeExpirationRef"
					:key="`isTimeLimitEnabled-${isTimeLimitEnabled}`"
					v-model="timeExpiration"
					:disabled="!isTimeLimitEnabled"
					prepend-inner-icon="i-mdi:calendar"
					:min="timeStart"
					:rules="[!isTimeLimitEnabled?true:timeExpirationRule]"
				/>
			</v-col>
		</v-row>
	</v-container>
	<v-dialog
		v-model="dialogModel"
		max-width="800"
	>
		<v-confirm-edit
			v-if="item && editDay"
			v-slot="{ model: proxyModel, actions }"
			v-model="editDay"
			cancel-text="Reset"
			@save="confirmEditSaveHandler"
		>
			<v-card :title="$t('Symbols.MT4.Crud.Sessions.edit-editday-text', [editDay?.text])">
				<v-card-text>
					<symbols-crud-session-time-slider
						v-model="proxyModel.value.quote"
						type="MT4"
						:label="$t('Symbols.MT4.Crud.Sessions.quotes')"
						class="mt-4"
					/>
					<symbols-crud-session-time-slider
						v-model="proxyModel.value.trade"
						type="MT4"
						:label="$t('Symbols.MT4.Crud.Sessions.trades')"
						:disabled="!proxyModel.value.isSeparateTrading"
					/>
					<v-switch
						v-model="proxyModel.value.isSeparateTrading"
						hide-details
						color="success"
						class="ms-3 compact"
						:label="$t('Symbols.MT4.Crud.Sessions.enable-separate-trading-s')"
					/>
				</v-card-text>
				<template #actions>
					<v-btn
						variant="text"
						@click="dialogModel = false"
					>
						{{ $t('Symbols.MT4.Crud.Sessions.cancel') }}
					</v-btn>
					<v-spacer />
					<component :is="actions" />
				</template>
			</v-card>
		</v-confirm-edit>
	</v-dialog>
</template>

<script lang="ts" setup>
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import type { Symbols } from '~/types'

// import { Mask } from '~/types/Mask'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

type Day = {
	text: string
	value: number
	default?: {
		open: number
		close: number
	} // number of minutes if the ranges are all 0
}

type DayData = {
	text: string
	value: number
	isSeparateTrading: boolean
	trade: Symbols.MT4.ConSession[]
	quote: Symbols.MT4.ConSession[]
}

const isTimeLimitEnabled = ref((!!p.item.starting && !!p.item.expiration) || false)
console.log('🚀 ~ (!!p.item.starting && !!p.item.expiration):', p.item.starting, p.item.expiration, (!!p.item.starting && !!p.item.expiration))

const { t } = useI18n()

const daysOfWeek = ref<Day[]>([
	{
		text: t('Symbols.MT4.Crud.Sessions.sunday'),
		value: 0,
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.monday'),
		value: 1,
		default: {
			open: 0,
			close: 24 * 60,
		},
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.tuesday'),
		value: 2,
		default: {
			open: 0,
			close: 24 * 60,
		},
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.wednesday'),
		value: 3,
		default: {
			open: 0,
			close: 24 * 60,
		},
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.thursday'),
		value: 4,
		default: {
			open: 0,
			close: 24 * 60,
		},
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.friday'),
		value: 5,
		default: {
			open: 0,
			close: 24 * 60,
		},
	},
	{
		text: t('Symbols.MT4.Crud.Sessions.saturday'),
		value: 6,
	},
])

const dialogModel = ref(false)

const editDay = ref<DayData | null>(null)

const edit = (day: Day) => {
	const trade = toRaw(useCloned(p.item.sessions[day.value].trade).cloned.value)
	const quote = toRaw(useCloned(p.item.sessions[day.value].quote).cloned.value)

	editDay.value = {
		text: day.text,
		value: day.value,
		isSeparateTrading: !isEqual(trade, quote),
		trade,
		quote,
	}

	nextTick(() => {
		dialogModel.value = true
	})
}

const format = (time: number) => {
	const hours = Math.floor(time / 60)
	const minutes = time % 60
	return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
}

const formatRanges = (ranges: Symbols.MT4.ConSession[]) => {
	return ranges.filter(range => !isZeros(range)).map((range) => {
		return `${format((range.openHour * 60) + range.openMin)} - ${format((range.closeHour * 60) + range.closeMin)}`
	}).join('<br>')
}

const confirmEditSaveHandler = (dayData: DayData) => {
	dialogModel.value = false

	const sessions = useCloned(p.item.sessions).cloned.value
	sessions[dayData.value].quote = dayData.quote
	// sessionsQuotes[dayData.value] = dayData.quote

	if (dayData.isSeparateTrading) {
		sessions[dayData.value].trade = dayData.trade
	} else {
		sessions[dayData.value].trade = dayData.quote
	}

	// fill with zeros for all days, trade and quote should be array of 3
	for (let i = 0; i < 7; i++) {
		while (sessions[i].quote.length < 3) {
			sessions[i].quote.push({
				openHour: 0,
				openMin: 0,
				closeHour: 0,
				closeMin: 0,
			})
		}
		while (sessions[i].trade.length < 3) {
			sessions[i].trade.push({
				openHour: 0,
				openMin: 0,
				closeHour: 0,
				closeMin: 0,
			})
		}
	}

	emit('update:item', {
		sessions,
	})
}

const dayjs = useDayjs()

const timeStart = computed({
	get() {
		// convert from unix timestamp to date
		return dayjs.unix(p.item.starting).utc().format('YYYY-MM-DD HH:mm')
	},
	set(value: string) {
		emit('update:item', {
			starting: dayjs(value).utc().unix(),
		})
	},
})

const timeExpiration = computed({
	get() {
		// convert from unix timestamp to date
		return dayjs.unix(p.item.expiration).utc().format('YYYY-MM-DD HH:mm')
	},
	set(value: string) {
		emit('update:item', {
			expiration: dayjs(value).utc().unix(),
		})
	},
})

const timeLimitHandler = (value: boolean | null) => {
	if (value === false) {
		emit('update:item', {
			starting: 0,
			expiration: 0,
		})
	}
}

const isAllZeros = (ranges: Symbols.MT4.ConSession[]) => {
	return ranges.every(isZeros)
}

const isZeros = (range: Symbols.MT4.ConSession) => {
	return range.openHour === 0 && range.openMin === 0 && range.closeHour === 0 && range.closeMin === 0
}

const timeExpirationRule = (v: string) => {
	return (dayjs(v).unix() > dayjs(timeStart.value).unix()) || ('Time Expiration must be greater than Starting Time')
}
</script>
