import { useAuthStore } from '@/stores/auth'

const log = useLog('hasTokenButNo2FA Middleware', {
	namespaceStyle: {
		backgroundColor: '#FF5722',
	},
})

export default defineNuxtRouteMiddleware((_to, _from) => {
	const authStore = useAuthStore()
	if (authStore.isLoggedIn) {
		log('Already verified')
		return navigateTo({ name: 'index' })
	}

	if (!authStore.hasToken) {
		log('Token not found')
		return navigateTo({ name: 'auth-login' })
	}
})
