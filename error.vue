<template>
	<v-container
		fluid
		class="d-flex align-center justify-center h-screen bg-background"
	>
		<v-card
			class="pa-4"
			width="600"
		>
			<v-row>
				<v-col cols="12">
					<v-sheet
						width="200"
						class="ps-3"
					>
						<logo />
					</v-sheet>
				</v-col>
			</v-row>
			<v-row
				align="center"
				class="flex-nowrap"
			>
				<v-col>
					<v-card-title>{{ title }}</v-card-title>
					<!-- <v-card-subtitle>	{{ subtitle }}</v-card-subtitle> -->
					<v-card-text class="text-medium-emphasis">
						{{ message }}
					</v-card-text>
				</v-col>
				<v-col cols="auto">
					<v-icon
						size="132"
						:icon="icon"
					/>
				</v-col>
			</v-row>
			<v-row>
				<v-col cols="12">
					<v-card-actions>
						<v-spacer />
						<v-btn @click="goHome">
							Home
						</v-btn>
						<v-btn @click="p.clear">
							Return Back
						</v-btn>
					</v-card-actions>
				</v-col>
			</v-row>
		</v-card>
	</v-container>
</template>

<script setup lang="ts">
type Props = {
	error: any
	clear?: typeof clearError
}

const p = withDefaults(defineProps<Props>(), {
	clear: undefined,
})

const goHome = () => {
	const { replace } = useRouter()
	replace({ name: 'index' }).then(() => {
		if (p.clear) {
			p.clear()
		}
	})
}

definePageMeta({
	title: 'Error',
	layout: 'default',
})

const title = computed(() => {
	switch (p.error?.statusCode) {
		case 404:
			return 'Page Not Found'
		case 403:
			return 'Permission Denied'
		default:
			return `${p.error?.statusCode ? 'HTTP Error ' + p.error.statusCode : 'Script Error'}`
	}
})

const message = computed(() => {
	switch (p.error?.statusCode) {
		case 404:
			return 'Sorry, but the page you are trying to view does not exist. It\'s possible that the page has been removed, had its name changed, or is temporarily unavailable. But don\'t worry, we can help guide you back to familiar territory.'
		case 403:
			return 'You\'ve encountered a 403 Forbidden Error. Check your login details and permissions, or contact the system administrator for access issues.'
		default:
			return `${p.error?.message || p.error?.value?.message || 'No error message provided'}`
	}
})
console.error(p.error)
if (p.error?.value?.stack) {
	console.log(p.error.value.stack)
}

const icon = computed(() => {
	switch (p.error?.statusCode) {
		case 404:
			return 'i-mdi:file-document-outline'
		case 403:
			return 'i-mdi:shield-lock-outline'
		default:
			return 'i-mdi:alert-circle-outline'
	}
})
</script>
