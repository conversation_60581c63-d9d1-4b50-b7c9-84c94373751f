import type { HTMLAttributes } from 'vue'
import merge from 'lodash/merge'

type LogFunction = (namespace: string, options?: Options) => typeof console.log

type Options = {
	namespaceStyle?: Partial<HTMLAttributes['style']>
}

const defaultOptions: Options = {
	namespaceStyle: {
		color: '#ffffff',
		backgroundColor: '#3F51B5',
		borderRadius: '0.25rem',
		padding: '4px 8px',
	},
}

export const useLog: LogFunction = (namespace: string, options?: Options): LogFunction => {
	const _options = merge(defaultOptions, options)

	const style = Object.entries(_options.namespaceStyle || {})
		.map(([key, value]) => {
			// Convert camelCase to kebab-case for the keys and keep values intact
			const kebabKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
			return `${kebabKey}: ${value}`
		}).join('; ')

	const styledLog = (console.log as any).bind(console, `%c${namespace}`, style)

	const fn = (...args: any) => {
		const config = useRuntimeConfig()

		if (config.public.log.showLog) {
			return styledLog(...args)
		} else {
			return (() => {}) as any
		}
	}

	return fn
}
