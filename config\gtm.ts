import type { ModuleOptions } from '@zadigetvoltaire/nuxt-gtm'
import { isDev } from './general'

const isEnabled = !!process.env.NUXT_PUBLIC_GTM_ID

const config: ModuleOptions = {
	id: 'GTM-XXXXXX', // its preferred to use NUXT_PUBLIC_GTM_ID
	defer: true,
	compatibility: true,
	enabled: isEnabled,
	debug: isDev && isEnabled,
	loadScript: isEnabled,
	enableRouterSync: isEnabled,
	// ignoredViews: ['homepage'],
	trackOnNextTick: true,
	devtools: isDev && isEnabled,
}

export default config
