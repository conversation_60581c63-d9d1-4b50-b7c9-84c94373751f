<template>
	<v-defaults-provider :defaults="{ global: { density: 'compact' } }">
		<v-tabs
			v-model="tabModel"
			center-active
			color="accent"
			:items="tabs"
			grow
			class="mb-4"
		>
			<v-badge
				v-for="tab in tabs"
				:key="tab.value"
				color="error"
				:offset-y="8"
				:offset-x="8"
				bordered
				:content="tab.errorsCount?.value"
				:model-value="tab.isValid.value === false"
			>
				<v-tab
					class="flex-grow-1"
					:value="tab.value"
				>
					{{ tab.text }}
				</v-tab>
			</v-badge>
		</v-tabs>
		<!-- <v-sheet max-height="calc(100dvh - 132px)" class="flex-grow-1 overflow-y-auto"> -->

		<v-tabs-window v-model="tabModel">
			<v-form
				v-for="tab in tabs"
				ref="tabForm"
				:key="tab.value"
				v-model="tab.isValid.value"
			>
				<v-tabs-window-item
					:value="tab.value"
					:eager="isValidated"
				>
					<v-sheet min-height="480">
						<component
							:is="tab.component"
							:item="model"
							:errors="errors"
							@update:item="updateHandler"
						/>
					</v-sheet>
				</v-tabs-window-item>
			</v-form>
		</v-tabs-window>

		<!-- </v-sheet> -->
	</v-defaults-provider>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Common from './Common.vue'
import Execution from './Execution.vue'
import Margin from './Margin.vue'
import MarginRates from './MarginRates.vue'
import Swaps from './Swaps.vue'
import Trade from './Trade.vue'
import type { Groups } from '~/types'
import type { ItemErrors } from '~/components/Crud.vue'

type Props = {
	errors: ItemErrors<Groups.MT5.Symbol>
}

defineProps<Props>()

const isValidated = ref(false)

const model = defineModel<Groups.MT5.Symbol>({
	required: true,
})

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs: Tab[] = ([
	{
		value: 'common',
		text: t('Groups.MT5.Crud.common'),
		component: Common,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[0]?.errors.length || 0),
	},
	{
		value: 'trade',
		text: t('Groups.MT5.Crud.trade'),
		component: Trade,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[1]?.errors.length || 0),
	},
	{
		value: 'execution',
		text: t('Groups.MT5.Crud.execution'),
		component: Execution,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[2]?.errors.length || 0),
	},
	{
		value: 'margin',
		text: t('Groups.MT5.Crud.margin'),
		component: Margin,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[3]?.errors.length || 0),
	},
	{
		value: 'margin-rates',
		text: t('Groups.MT5.Crud.margin-rates'),
		component: MarginRates,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[4]?.errors.length || 0),
	},
	{
		value: 'swaps',
		text: t('Groups.MT5.Crud.swaps'),
		component: Swaps,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[5]?.errors.length || 0),
	},

])

const tabModel = ref<string>(tabs[0].value)

const updateHandler = (newItem: Groups.MT5.Symbol) => {
	const item = useCloned(model).cloned.value
	for (const key in newItem) {
		(item as any)[key] = (newItem as any)[key]
	}
	model.value = item
}
</script>
