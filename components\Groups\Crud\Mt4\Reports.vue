<template>
	<v-container>
		<v-switch
			v-model="item.reports"
			:true-value="1"
			:false-value="0"
			:label="$t('Groups.MT4.Crud.Reports.enable')"
			color="success"
		/>
		<v-defaults-provider
			:defaults="{
				global: {
					disabled: !item.reports,
				},
			}"
		>
			<v-text-field
				v-model="item.smtpServer"
				:error-messages="errors.smtpServer"
				:label="$t('Groups.MT4.Crud.Reports.smtp-server')"
				:rules="rules({ required: !!item.reports })"
			/>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.smtpLogin"
						:error-messages="errors.smtpLogin"
						:label="$t('Groups.MT4.Crud.Reports.smtp-login')"
						:rules="rules({ required: !!item.reports })"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.smtpPassword"
						:error-messages="errors.smtpPassword"
						type="password"
						:label="$t('Groups.MT4.Crud.Reports.smtp-password')"
						:rules="rules({ required: !!item.reports })"
					/>
				</v-col>
			</v-row>
			<v-text-field
				v-model="item.templatesPath"
				:error-messages="errors.templatesPath"
				:label="$t('Groups.MT4.Crud.Reports.templates-path')"
			/>
			<v-text-field
				v-model="item.supportEmail"
				:error-messages="errors.supportEmail"
				:label="$t('Groups.MT4.Crud.Reports.support-email')"
			>
				<template #append>
					<v-checkbox
						v-model.number="item.copies"
						color="primary"
						hide-details
						:label="$t('Groups.MT4.Crud.Reports.copy-report-to-support')"
						:true-value="1"
						:false-value="0"
					/>
				</template>
			</v-text-field>
			<v-textarea
				v-model="item.signature"
				rows="4"
				:label="$t('Groups.MT4.Crud.Reports.signature')"
			/>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
// import { Groups } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()
</script>

<style>

</style>
