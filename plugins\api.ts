interface CustomHeaders {
	Authorization?: string
	ServerName?: string
	Language?: string
}
export default defineNuxtPlugin(() => {
	const config = useRuntimeConfig()

	const authStore = useAuthStore()

	const pref = usePreferencesStore()

	const { locale } = useNuxtApp().$i18n

	const APIVersion = useState('APIVersion', () => ref(''))
	// isShallow(state) === true

	const $api = $fetch.create({
		baseURL: config.public.apiBaseUrl,
		timeout: 20000, // 20 seconds
		mode: 'cors',
		onRequest({ options }) {
			const customHeaders: CustomHeaders = {}

			if (authStore.hasToken) {
				customHeaders.Authorization = `Bearer ${authStore.accessToken}`

				if (pref.getKey('tradingServerName')) {
					customHeaders.ServerName = pref.getKey('tradingServerName')?.toString()
				}
			}

			customHeaders.Language = locale.value

			// if (options.headers?.['API-Version']) {
			APIVersion.value = APIVersion.value + 1
			// }

			options.headers = {
				...options.headers,
				...customHeaders,
			}
		},
		onResponse({ response }) {
			if (response.headers.has('Api-Version')) {
				APIVersion.value = response.headers.get('Api-Version') || ''
			}
		},
		onResponseError({ response }) {
			const { status } = response
			switch (status) {
				case 401:
					authStore.logout()
					navigateTo({ name: 'auth-login' })
					break
				case 406:
					navigateTo({ name: 'auth-2fa' })
					break
				case 403:
					throw createError({
						statusCode: 403,
						fatal: true,
						unhandled: false,
					})
					break
				default:
					break
			}
		},
	})
	// Expose to useNuxtApp().$api
	return {
		provide: {
			api: $api,
		},
	}
})
