::-webkit-scrollbar {
	width: 7px;
	height: 7px;
}
::-webkit-scrollbar-button {
	width: 0px;
	height: 0px;
}
::-webkit-scrollbar-thumb {
	background: rgba(var(--v-theme-primary), 0.8);
	border: 0px none #ffffff;
	border-radius: 50px;
}
::-webkit-scrollbar-thumb:hover {
	background: rgba(var(--v-theme-primary), 0.65);
}
::-webkit-scrollbar-thumb:active {
	background: rgba(var(--v-theme-primary), 1);
}
::-webkit-scrollbar-track {
	background: rgba(var(--v-theme-background), 0.8);
	border: 0px none #ffffff;
	border-radius: 50px;
}
::-webkit-scrollbar-track:hover {
	background: rgba(var(--v-theme-background), 0.65);
}
::-webkit-scrollbar-track:active {
	background: rgba(var(--v-theme-background), 1);
}
::-webkit-scrollbar-corner {
	background: transparent;
}
html,
[class*="text-"] {
	font-family: "Inter";
}
.locale-ar {
	font-family: "Tajawal" !important;
	.text-h1,
	.text-h2,
	.text-h3,
	.text-h4,
	.text-h5,
	.text-h6,
	.text-caption,
	.text-body-1,
	.text-body-2,
	.text-overline,
	.text-subtitle-1,
	.text-subtitle-2,
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		font-family: "Tajawal" !important;
	}
}

.brand-name {
	color: transparent;
	background-size: 400% 100%;
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background: linear-gradient(179deg, rgba(0, 0, 0, 1) 0%, rgba(21, 21, 42, 1) 35%, rgb(141 137 137) 100%);
	background-clip: text;
	font-weight: 500;
	font-size: 14px;
	user-select: none;
	font-family: "Open Sans";
	padding-top: 14px;
	text-decoration: none !important;
}

.v-theme--dark .brand-name {
	background: rgba(255 255 255 / 90%);
	background-clip: text;
}

input[type="password"] {
	font-family: monospace;
}

input[type="password"]::placeholder {
	font-family: "Roboto", sans-serif; /* or specify a different font-family */
}

.bg-transparent {
	background-color: transparent !important;
}
.text-transparent {
	color: transparent !important;
}

.v-messages {
	//to prevent the error message from touching the input field down
	min-height: 20px !important;
}

.text-inherit {
	color: inherit !important;
}

// already in vuetify
// .cursor-pointer {
// 	cursor: pointer;
// }

.cursor-alias {
	cursor: alias;
}

.v-locale--is-rtl {
	// remove letter-spacing on arabic
	letter-spacing: normal;
	.v-btn {
		letter-spacing: normal;
	}

	//rotatable icons
	i.rtl-flip {
		transform: scaleX(-1);
	}
	// i.i-mdi\:chevron-left,
	// i.i-mdi-chevron-left,
	// i.i-mdi\:chevron-right,
	// i.i-mdi-chevron-right,
	// i.i-mdi\:logout,
	// i.i-mdi-logout {
	// 	transform: scaleX(-1);
	// }
}

.pulse {
	position: relative;
	overflow: visible !important;
	:before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 100%;
		background-color: currentColor;
		border-radius: 100%;
		z-index: -1;
		animation: pulse 2s infinite;
		will-change: transform;
		transform: translate(-50%, -50%);
		mask: initial;
		-webkit-mask: initial;
		pointer-events: none;
	}
}

.pulse:hover:before {
	animation: none;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 0.4;
	}
	100% {
		transform: scale(1.25);
		opacity: 0;
	}
}

a[href]:not([class^="v-"]) {
	color: rgb(var(--v-theme-link));
	// text-decoration: none;
}

@for $i from 1 through 4 {
	.line-truncate-#{$i} {
		display: -webkit-box !important;
		line-clamp: #{$i};
		-webkit-line-clamp: #{$i};
		-webkit-box-orient: vertical;
		overflow: hidden;
		padding-bottom: 0 !important;

		/* These are technically the same, but use both */
		overflow-wrap: break-word;
		word-wrap: break-word;
		-ms-word-break: break-all;

		/* This is the dangerous one in WebKit, as it breaks things wherever */
		word-break: break-all;

		/* Instead use this non-standard one: */
		word-break: break-word;

		/* Adds a hyphen where the word breaks, if supported (No Blink) */
		-ms-hyphens: auto;
		-moz-hyphens: auto;
		-webkit-hyphens: auto;
		hyphens: auto;
	}
}

// .no-bottom-line {
// 	.v-field__outline {
// 		&:before {
// 			display: none;
// 		}
// 	}
// }

fieldset {
	border-width: thin;
	padding: 8px;
	padding-inline: 16px;
	border-radius: 8px;
	legend {
		padding-inline: 4px;
		margin-inline-start: -4px;
		color: rgba(var(--v-theme-on-background), var(--v-medium-emphasis-opacity)) !important;
	}
	~ fieldset {
		margin-top: 16px;
	}
}

.compact {
	.v-selection-control {
		--v-input-control-height: 24px !important
	;
	}
	.v-selection-control--density-compact {
		--v-selection-control-size: 24px;
	}
}

.show-on-hover-trigger {
	&:hover {
		.show-on-hover-item {
			visibility: visible;
			opacity: 1;
			transition: opacity 0.6s ease;
		}
	}
	.show-on-hover-item {
		visibility: hidden;
		opacity: 0;
		transition: opacity 0.6s ease;
	}
}

//icons
.v-table.compact > .v-table__wrapper > table > tbody > tr > td,
.v-table.compact > .v-table__wrapper > table > thead > tr > th {
	padding: 0 8px;
}

.draggable-rows {
	table {
		border-collapse: collapse;
		tr.ghost {
			background: rgb(var(--v-theme-background)) !important;
			border: 1px dashed rgb(var(--v-theme-on-background)) !important;
			td {
				// if visibility: hidden on TD, background will not be visible
				border: none !important;
				// * {
				// 	visibility: hidden;
				// }
			}
		}
	}
}

.v-inline-fields--card-container {
	.v-input__control {
		min-width: 12px;
	}
}

.v-data-table--loading .v-data-table__td {
	opacity: 1 !important;
}

//for loop 11 times for z-index from 0 to 10
@for $i from 0 through 10 {
	.z-#{$i} {
		z-index: $i;
	}
}
.v-list--nav {
	.v-list-item__overlay {
		--v-activated-opacity: 0.9;
		--v-hover-opacity: 0.1;
		right: unset;
		width: 4px;
		top: 2px;
		bottom: 2px;
	}

	.v-list-item__content,
	.v-list-item__prepend {
		color: rgb(var(--v-theme-on-surface));
	}

	.v-list-item-title {
		font-weight: 400 !important;
	}
}

.bg-aurora {
	--v-theme-overlay-multiplier: var(--v-theme-primary-overlay-multiplier);
	color: rgb(var(--v-theme-on-primary)) !important;
	background: linear-gradient(135deg, #5b2282, #b20680, #328bcc, #512382);
	&.animate {
		background-size: 300% 300%;
		animation: gradient 40s ease infinite;
	}
}

.vue-grid-item > .vue-resizable-handle {
	z-index: 1;
	// @at-root .v-theme--dark & {
	// 	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgRmlyZXdvcmtzIENTNiwgRXhwb3J0IFNWRyBFeHRlbnNpb24gYnkgQWFyb24gQmVhbGwgKGh0dHA6Ly9maXJld29ya3MuYWJlYWxsLmNvbSkgLiBWZXJzaW9uOiAwLjYuMSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgaWQ9IlVudGl0bGVkLVBhZ2UlMjAxIiB2aWV3Qm94PSIwIDAgNiA2IiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjojZmZmZmZmMDAiIHZlcnNpb249IjEuMSIKCXhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHhtbDpzcGFjZT0icHJlc2VydmUiCgl4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjZweCIgaGVpZ2h0PSI2cHgiCj4KCTxnIG9wYWNpdHk9IjAuMzAyIj4KCQk8cGF0aCBkPSJNIDYgNiBMIDAgNiBMIDAgNC4yIEwgNCA0LjIgTCA0LjIgNC4yIEwgNC4yIDAgTCA2IDAgTCA2IDYgTCA2IDYgWiIgZmlsbD0iI2ZmZmZmZiIvPgoJPC9nPgo8L3N2Zz4=)
	// 		no-repeat content-box bottom right;
	// }
	background: unset !important;
	border-bottom: 1px solid rgb(var(--v-theme-accent));
	border-inline-end: 1px solid rgb(var(--v-theme-accent));
	width: 10px !important;
	height: 10px !important;
}

@keyframes gradient {
	0% {
		background-position: 0% 0%;
	}
	25% {
		background-position: 100% 0%;
	}
	50% {
		background-position: 100% 100%;
	}
	75% {
		background-position: 0% 100%;
	}
	100% {
		background-position: 0% 0%;
	}
}

// ::view-transition-old(root),
// ::view-transition-new(root) {
// 	animation-duration: 0.35s;
// }

// .page-enter-active,
// .page-leave-active {
// 	transition: opacity 0.25s;
// }
// .page-enter-from,
// .page-leave-to {
// 	opacity: 0;
// 	// filter: blur(1rem);
// }
// .page-leave-active {
// 	position: absolute;
// 	/* Optional: Set top, left, right, bottom to control positioning */
// 	/* top: 0; */
// 	/* left: 0; */
// 	/* width: 100%; for full-width */
// 	/* height: 100%; for full-height */
// }
.vue3-snackbar-message-title,
.vue3-snackbar-message-additional {
	color: #fff;
}
