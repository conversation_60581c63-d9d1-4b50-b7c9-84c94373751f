<template>
	<v-text-field
		v-model="model"
		readonly
	>
		<v-menu
			v-model="menuModel"
			activator="parent"
			:close-on-content-click="false"
			@update:model-value="menuModelUpdateHandler"
		>
			<v-card>
				<v-tabs
					v-model="tabModel"
					color="primary"
					:rounded="false"
					grow
				>
					<v-tab
						value="date"
						:rounded="false"
					>
						<v-icon color="primary">
							i-mdi:calendar
						</v-icon>
					</v-tab>
					<v-tab
						value="time"
						:rounded="false"
						:disabled="!dateModel"
					>
						<v-icon color="primary">
							i-mdi:clock-outline
						</v-icon>
					</v-tab>
				</v-tabs>

				<v-tabs-window v-model="tabModel">
					<v-tabs-window-item value="date">
						<v-date-picker
							v-model="dateModel"
							hide-header
							color="primary"
							v-bind="p.datePickerProps"
							:min="minDate"
							:max="maxDate"
							@update:model-value="validateDate"
						/>
					</v-tabs-window-item>
					<v-tabs-window-item value="time">
						<!-- eslint-disable-next-line -->
								<v-time-picker 
							v-model="timeModel"
							color="primary"
							v-bind="p.timePickerProps"
							:min="minTime"
							:max="maxTime"
							@update:model-value="validateTime"
							@update:minute="p.timePickerProps.useSeconds?undefined: closeMenu()"
							@update:second="closeMenu()"
						/>
					</v-tabs-window-item>
				</v-tabs-window>

				<v-card-actions>
					<v-btn
						color="primary"
						variant="text"
						:disabled="isNowDisabled"
						@click="setNow"
					>
						{{ $t('DateTimeInput.now') }}
					</v-btn>
					<v-spacer />
					<v-btn
						variant="text"
						@click="closeMenu"
					>
						{{ $t('DateTimeInput.close') }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-menu>
	</v-text-field>
</template>

<script lang="ts" setup>
import { VTextField, VDatePicker } from 'vuetify/components'
import { VTimePicker } from 'vuetify/labs/components'
import type { ConfigType } from 'dayjs'

type Emit = {
	(e: 'update:modelValue', value: string | null): void
}

type VTextFieldPropTypes = Partial<Omit<VTextField['$props'], 'type' | 'modelValue'>>

export interface Props extends /* @vue-ignore */ VTextFieldPropTypes {
	datePickerProps?: Omit<Partial<VDatePicker['$props']>, 'modelValue'>
	timePickerProps?: Omit<Partial<VTimePicker['$props']>, 'modelValue'>
	modelValue?: string | null
	format?: string
	min?: ConfigType
	max?: ConfigType
}

const p = withDefaults(defineProps<Props>(), {
	datePickerProps: () => ({}),
	timePickerProps: () => ({}),
	modelValue: null,
	format: 'YYYY-MM-DD HH:mm Z',
	min: undefined,
	max: undefined,
})

const emit = defineEmits<Emit>()

const dayjs = useDayjs()

const menuModel = ref(false)
const tabModel = ref('date')
const timeModel = ref<string | null>(null)
const dateModel = ref<Date | null>(null)

const minDate = computed(() => (p.min ? dayjs(p.min).format('YYYY-MM-DD') : undefined))
const maxDate = computed(() => (p.max ? dayjs(p.max).format('YYYY-MM-DD') : undefined))

const minTime = computed(() => {
	if (dateModel.value && p.min && dayjs(dateModel.value).isSame(dayjs(p.min), 'day')) {
		return dayjs(p.min).format('HH:mm:ss')
	}
	return undefined
})

const maxTime = computed(() => {
	if (dateModel.value && p.max && dayjs(dateModel.value).isSame(dayjs(p.max), 'day')) {
		return dayjs(p.max).format('HH:mm:ss')
	}
	return undefined
})

const model = computed<string | null>({
	get: () => {
		return p.modelValue
	},
	set: (value) => {
		emit('update:modelValue', value)
	},
})

if (dateModel.value === null && model.value) {
	dateModel.value = dayjs(model.value).toDate()
}

if (timeModel.value === null && model.value) {
	timeModel.value = dayjs(model.value).format('HH:mm:ss')
}

watch([dateModel, timeModel], ([date, time]) => {
	if (date && time) {
		const timeObj = time.split(':')
		const hour = Number.parseInt(timeObj[0], 10)
		const minute = Number.parseInt(timeObj[1], 10)
		const second = Number.parseInt(timeObj[2], 10) || 0
		model.value = dayjs(dateModel.value).hour(hour).minute(minute).second(second).format(p.format)
	}

	if (date === null && model.value) {
		dateModel.value = dayjs(model.value).toDate()
	}

	if (time === null && model.value) {
		timeModel.value = dayjs(model.value).format('HH:mm:ss')
	}
})

const closeMenu = () => {
	menuModel.value = false
	setTimeout(() => {
		switchTo('date')
	}, 250)
}

const switchTo = (value: 'date' | 'time') => {
	tabModel.value = value
}

const validateDate = (newDate: Date | null) => {
	if (!newDate) {
		// timeModel.value = null
		return
	}

	if (p.min && dayjs(newDate).isBefore(dayjs(p.min), 'day')) {
		dateModel.value = null
	}
	if (p.max && dayjs(newDate).isAfter(dayjs(p.max), 'day')) {
		dateModel.value = null
	}

	if (dateModel.value) {
		switchTo('time')
	}
}

const validateTime = (newTime: string) => {
	if (dateModel.value) {
		const combinedDateTime = dayjs(dateModel.value).hour(Number.parseInt(newTime.split(':')[0])).minute(Number.parseInt(newTime.split(':')[1]))

		if (p.min && combinedDateTime.isBefore(dayjs(p.min))) {
			timeModel.value = null
		}
		if (p.max && combinedDateTime.isAfter(dayjs(p.max))) {
			timeModel.value = null
		}
	}
}

const menuModelUpdateHandler = (value: boolean) => {
	if (!value) {
		closeMenu()
	}
}

const now = useNow()

const isNowDisabled = computed(() => {
	if (p.min && dayjs(now.value).isBefore(dayjs(p.min), 'minute')) {
		return true
	}
	if (p.max && dayjs(now.value).isAfter(dayjs(p.max), 'minute')) {
		return true
	}
	return false
})

const setNow = () => {
	dateModel.value = new Date()
	timeModel.value = dayjs().format('HH:mm:ss')
	closeMenu()
}
</script>
