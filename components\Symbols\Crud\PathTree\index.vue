<template>
	<div>
		<v-text-field
			v-bind="$attrs"
			:model-value="model"
			placeholder="Select Path"
			readonly
			prepend-inner-icon="i-mdi:folder text-yellow-darken-1"
			class="curser-pointer"
			:append-inner-icon="menuModel ? 'i-mdi:menu-up' : 'i-mdi:menu-down'"
			:loading="status === 'pending'"
		>
			<v-menu
				v-model="menuModel"
				activator="parent"
				max-height="500dvh"
				:close-on-content-click="false"
				@update:model-value="menuHandler"
			>
				<v-list
					v-model:opened="openedModel"
					class="position-relative"
					width="100%"
					slim
					density="compact"
				>
					<!-- <template #prepend> -->
					<v-sheet
						class="position-sticky px-1"
						color="surface"
						style="z-index: 1;top: -8px;"
					>
						<v-text-field
							v-model="searchPathModel"
							autofocus
							clearable
							placeholder="Find Path"
							hide-details
							flat
							variant="solo"
							density="comfortable"
						/>
						<v-divider class="mx-2" />
					</v-sheet>
					<PathTreeItem
						v-for="item in computedPaths"
						:key="item.value"
						:item="item"
						@path-select="pathSelectHandler"
						@add-dir="addDirHandler"
					/>

					<v-empty-state
						v-if="!computedPaths.length"
						:text="`No path found matching &quot;${searchPathModel}&quot;`"
					>
						<template #media>
							<v-icon
								class="mb-3"
								size="x-large"
								icon="i-mdi:folder-off-outline"
							/>
						</template>
					</v-empty-state>

					<v-dialog
						v-model="addDirModel"
						max-width="400"
						attach
						absolute
						@update:model-value="cancelAddDir"
					>
						<v-form
							v-slot="{ isValid }"
							@submit.prevent="saveDir"
						>
							<v-card title="Add Directory">
								<template #text>
									<div class="mb-2" />
									<v-text-field
										v-model.trim="dirNameModel"
										autofocus
										placeholder="Directory Name"
										density="comfortable"
										persistent-placeholder
										single-line
										:rules="rules({ required: true, whitespace: true })"
									/>
								</template>
								<template #actions="">
									<v-spacer />
									<v-btn @click="cancelAddDir(false)">
										Cancel
									</v-btn>
									<v-btn
										:disabled="!isValid.value"
										@click="saveDir()"
									>
										Save
									</v-btn>
								</template>
							</v-card>
						</v-form>
					</v-dialog>
				</v-list>
			</v-menu>
		</v-text-field>
	</div>
</template>

<script lang="ts" setup>
import { VTextField } from 'vuetify/components'
import PathTreeItem from '~/components/Symbols/PathTree/PathTreeItem.vue'
import { Symbols } from '~/types'

defineOptions({
	inheritAttrs: false,
})
type Path = Symbols.MT5.Paths.Lookup.SingleRecord

type Props = {
	symbol: string
}

const p = defineProps<Props>()

watch(() => p.symbol, (newValue, oldValue) => {
	if (oldValue) {
		const parts = model.value.split('\\')
		parts[parts.length - 1] = parts[parts.length - 1].replace(oldValue, newValue)
		model.value = parts.join('\\')
	} else if (model.value.endsWith('\\')) {
		model.value = `${model.value}${newValue}`
	} else {
		model.value = `${model.value}\\${newValue}`
	}
})

const model = defineModel('modelValue', {
	type: String,
	default: '',
})

const extractPathArray = (path: string) => {
	const parts = path.split('\\')
	return parts.reduce((acc, part) => {
		// only add \\ if not first one
		const path = (acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part
		acc.push(path)
		return acc
	}, [''])
		.filter(part => !!part)
}

const searchPathModel = ref('')

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const paths = ref<Path[]>([])

const openedModel = ref<Path['value'][]>(extractPathArray(model.value))

const menuModel = ref(false)

const currentAddDirItem = ref<Path | null>(null)

const addDirModel = ref(false)

const dirNameModel = ref('')

const { data, status } = useApi<Symbols.MT5.Paths.Lookup.GETResponse>(Symbols.MT5.Paths.Lookup.URL, {
	key: 'crudPathTree',
})

watch(() => data.value, (value) => {
	if (value) {
		paths.value = value
	}
})

const computedPaths = computed(() => {
	if (!paths.value) {
		return []
	}

	if (!debouncedSearchPathModel.value) {
		return paths.value
	}
	// filter the data based on the debouncedSearchPathModel model, find the item.title recursively in all children and keep the parent folder
	const filterData = (paths: Symbols.MT5.Paths.Lookup.GETResponse, search: string) => {
		return paths.filter((item) => {
			if (item.children.length) {
				const children = filterData(item.children, search)
				if (children.length) {
					item.children = children
					return true
				}
			}
			return item.title.toLowerCase().includes(search.toLowerCase())
		})
	}
	return filterData(paths.value, debouncedSearchPathModel.value)
})

// const addIndexLocation = (node: Symbols.Paths.Lookup.GETRequest, currentPath: any): Path => {
// 	const currentValue = currentPath.join('\\') // Use backslash as separator

// 	// If the node has children, iterate through them
// 	if (node.children && node.children.length > 0) {
// 		node.children = node.children.map((child, _childIndex) =>
// 			addIndexLocation(child, [...currentPath, child.text]), // Assuming each node has a 'name' field
// 		)
// 	}

// 	return {
// 		...node,
// 		value: currentValue,
// 	} as Path
// }

const pathSelectHandler = (path: string) => {
	model.value = path + `\\${p.symbol}`
	menuModel.value = false
}

const addDirHandler = (item: Path) => {
	currentAddDirItem.value = item
	addDirModel.value = true
}

const saveDir = () => {
	if (!currentAddDirItem.value) {
		return
	}
	const parentPath = currentAddDirItem.value.value
	const dirName = dirNameModel.value
	const path = `${parentPath}\\${dirName}`

	// find the correct location and push the item to the paths array
	const findAndPush = (node: Path) => {
		if (node.value === parentPath) {
			node.children.push({
				title: dirName,
				value: path,
				children: [],
				totalItems: 0,
			})
			return
		}

		if (node.children && node.children.length > 0) {
			node.children.forEach(child => findAndPush(child))
		}
	}

	paths.value.forEach(item => findAndPush(item))
	// if the path is level1\level2\level3, then open all nodes by pushing level1 & level1\level2 & level1\level2\level3
	const pathParts = path.split('\\')
	pathParts.reduce((acc, part) => {
		// only add \\ if not first one

		acc.push((acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part)
		return acc
	}, ['']).forEach((part) => {
		if (part && !openedModel.value.includes(part)) {
			openedModel.value.unshift(part)
		}
	})
	cancelAddDir(false)
}

const cancelAddDir = (value: boolean) => {
	if (value) {
		return
	}
	addDirModel.value = false
	dirNameModel.value = ''
}

const menuHandler = (value: boolean) => {
	if (value === false) {
		searchPathModel.value = ''
		openedModel.value = extractPathArray(model.value)
	}
	// else {
	// 	// eslint-disable-next-line no-lonely-if
	// 	if (model.value) {
	// 		openedModel.value = extractPathArray(model.value)
	// 	}
	// 	// placeholder
	// }
}
</script>
