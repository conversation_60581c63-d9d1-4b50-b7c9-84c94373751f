<template>
	<v-menu
		v-model="menuModel"
		location="top"
		attach
		v-bind="VMenuFilteredProps"
	>
		<template #activator="{ props: menuProps }">
			<div
				class="d-inline-flex align-center"
				:class="{ 'menu-open': menuModel, 'show-on-hover': p.showOnHover, 'cursor-help': !p.disabled }"
				v-bind="menuProps"
			>
				<slot />

				<v-icon
					v-if="!p.disabled"
					size="x-small"
					end
					icon="i-mdi:information-variant-circle-outline"

					class="info-icon"
				/>
			</div>
		</template>
		<v-card
			theme="dark"
			min-width="100"
			flat
		>
			<v-card-text class="pb-0">
				<slot name="info" />
			</v-card-text>
			<v-card-actions v-if="typeof $slots?.actions === 'function'">
				<slot name="actions" />
			</v-card-actions>
		</v-card>
	</v-menu>
</template>

<script lang="ts" setup>
import { VMenu } from 'vuetify/components'

export type Slots = Readonly<{
	actions(): any
	info(): any
	default(): any
}>
defineSlots<Slots>()

defineOptions({
	extends: VMenu,
})

type Props = {
	// props here
	showOnHover?: boolean
	disabled?: boolean
}

const p = defineProps<Props>()

const menuModel = defineModel({ type: Boolean })

const VMenuFilteredProps = computed(() => {
	const props = VMenu.filterProps(p as any)
	delete props.modelValue
	return props
})
</script>

<style lang="scss" scoped>
.info-icon {
		opacity:0.2;
		transition:opacity 0.25s;
		&:hover{
			opacity:1;

		}
		@at-root .menu-open & {
			opacity:1;
			// transition:opacity 0.25s;
		}
	}

	.show-on-hover{
		.info-icon{
			opacity:0;
			visibility:hidden;
			transition:opacity 0.25s;
		}
		&:hover .info-icon{
			opacity:1;
			visibility:visible;
		}
	}
</style>
