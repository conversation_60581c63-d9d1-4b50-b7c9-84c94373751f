/**
 * A composable function that prevents a menu from closing when hovered.
 *
 * @param {Ref} model - A reference to the model that controls the menu's open/close state.
 * @returns {Object} An object containing:
 * - `props`: An object with `onMouseenter` and `onMouseleave` event handlers to manage hover state.
 * - `model`: The reactive model reference.
 */
export const useKeepMenuVisible = (model: Ref) => {
	const isHovered = ref(false)

	watch(model, (value) => {
		if (!value && isHovered.value) {
			model.value = true
		}
	})
	return {
		props: {
			onMouseenter: () => {
				isHovered.value = true
			},
			onMouseleave: () => {
				isHovered.value = false
			},
		},
		model,
	}
}
