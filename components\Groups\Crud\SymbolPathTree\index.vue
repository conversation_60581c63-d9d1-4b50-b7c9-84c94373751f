<template>
	<div>
		<v-text-field
			v-bind="$attrs"
			:model-value="model"
			:placeholder="$t('Groups.MT5.Crud.SymbolPathTree.select-path')"
			readonly
			prepend-inner-icon="i-mdi:folder text-yellow-darken-1"
			class="curser-pointer"
			:append-inner-icon="menuModel ? 'i-mdi:menu-up' : 'i-mdi:menu-down'"
			:loading="status === 'pending'"
		>
			<v-menu
				v-model="menuModel"
				activator="parent"
				max-height="500dvh"
				:close-on-content-click="false"
				@update:model-value="menuHandler"
			>
				<v-list
					v-model:opened="openedModel"
					class="position-relative"
					width="100%"
					slim
					density="compact"
				>
					<!-- <template #prepend> -->
					<v-sheet
						class="position-sticky px-1"
						color="surface"
						style="z-index: 1;top: -8px;"
					>
						<v-text-field
							v-model="searchPathModel"
							autofocus
							clearable
							:placeholder="$t('Groups.MT5.Crud.SymbolPathTree.find-path')"
							hide-details
							flat
							variant="solo"
							density="comfortable"
						/>
						<v-divider class="mx-2" />
					</v-sheet>
					<PathTreeItem
						v-for="item in computedPaths"
						:key="item.value"
						:item="item"
						@path-select="pathSelectHandler"
					/>

					<v-empty-state
						v-if="!computedPaths.length"
						:text="`No path found matching &quot;${searchPathModel}&quot;`"
					>
						<template #media>
							<v-icon
								class="mb-3"
								size="x-large"
								icon="i-mdi:folder-off-outline"
							/>
						</template>
					</v-empty-state>
				</v-list>
			</v-menu>
		</v-text-field>
	</div>
</template>

<script lang="ts" setup>
import { VTextField } from 'vuetify/components'
import PathTreeItem from './PathTreeItem.vue'
import { Symbols } from '~/types'

defineOptions({
	inheritAttrs: false,
})
type Path = Symbols.MT5.Lookup.SingleRecord

type Props = {
	// symbol: string
}

defineProps<Props>()

const model = defineModel('modelValue', {
	type: String,
	default: '',
})

const extractPathArray = (path: string) => {
	const parts = path.split('\\')
	return parts.reduce((acc, part) => {
		// only add \\ if not first one
		const path = (acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part
		acc.push(path)
		return acc
	}, [''])
		.filter(part => !!part)
}

const searchPathModel = ref('')

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const paths = ref<Path[]>([])

const openedModel = ref<Path['value'][]>(extractPathArray(model.value))

const menuModel = ref(false)

const { data, status } = useApi<Symbols.MT5.Lookup.GETResponse>(Symbols.MT5.Lookup.URL, {
	key: 'crudPathTree',
})

watch(() => data.value, (value) => {
	if (value) {
		paths.value = value
	}
})

const computedPaths = computed(() => {
	if (!paths.value) {
		return []
	}

	if (!debouncedSearchPathModel.value) {
		return paths.value
	}
	// filter the data based on the debouncedSearchPathModel model, find the item.title recursively in all children and keep the parent folder
	const filterData = (paths: Symbols.MT5.Lookup.GETResponse, search: string) => {
		return paths.filter((item) => {
			if (item.children.length) {
				const children = filterData(item.children, search)
				if (children.length) {
					item.children = children
					return true
				}
			}
			return item.title.toLowerCase().includes(search.toLowerCase())
		})
	}
	return filterData(paths.value, debouncedSearchPathModel.value)
})

// const addIndexLocation = (node: Symbols.Paths.Lookup.GETRequest, currentPath: any): Path => {
// 	const currentValue = currentPath.join('\\') // Use backslash as separator

// 	// If the node has children, iterate through them
// 	if (node.children && node.children.length > 0) {
// 		node.children = node.children.map((child, _childIndex) =>
// 			addIndexLocation(child, [...currentPath, child.text]), // Assuming each node has a 'name' field
// 		)
// 	}

// 	return {
// 		...node,
// 		value: currentValue,
// 	} as Path
// }

const pathSelectHandler = (item: Symbols.MT5.Lookup.SingleRecord) => {
	if (item.type === 'symbol') {
		model.value = item.value
	} else {
		model.value = item.value + '\\*'
	}
	menuModel.value = false
}

const menuHandler = (value: boolean) => {
	if (value === false) {
		searchPathModel.value = ''
		// openedModel.value = extractPathArray(model.value)
	}
	// else {
	// 	// eslint-disable-next-line no-lonely-if
	// 	if (model.value) {
	// 		openedModel.value = extractPathArray(model.value)
	// 	}
	// 	// placeholder
	// }
}
</script>
