<template>
	<div><component :is="currentPlatformComponent" /></div>
</template>

<script lang="ts" setup>
import MT5 from './MT5.vue'
import MT4 from './MT4.vue'

definePageMeta({
	permission: 'manager.view',
})

const currentServer = useCurrentMtServer()

const currentPlatformComponent = computed(() => {
	const platform = currentServer.value?.tradingPlatform.name
	if (platform === 'MT5') {
		return MT5
	}
	return MT4
})
</script>
