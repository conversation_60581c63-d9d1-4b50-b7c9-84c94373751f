<template>
	<page title="Profile">
		<!-- <v-row>
			<v-col md="auto" cols="12">
				<v-tabs
					v-model="tab"
					color="accent"
					class="flex-shrink-0 pe-2"
					:direction="mdAndUp ? 'vertical':'horizontal'"
					mobile-breakpoint="md"
					mandatory
					:stacked="!mdAndUp"
					grow
				>
					<v-tab :width="!mdAndUp?120:undefined" prepend-icon="i-mdi:user-outline" text="Personal Details" value="profile" />
					<v-tab :width="!mdAndUp?120:undefined" prepend-icon="i-arcticons:metatrader-5" text="Trading Servers" value="tradingServers" />
					<v-tab :width="!mdAndUp?120:undefined" prepend-icon="i-mdi:lock" text="2 Factors Auth." value="2fa" />
				</v-tabs>
			</v-col>
			<v-divider vertical />
			<v-col md cols="12">
				<v-tabs-window v-model="tab">
					<v-tabs-window-item value="profile">
						<v-card flat title="Profile" style="max-width: 750px">
							<v-card-text>
								<v-text-field :model-value="authStore.user?.name" label="User Name" readonly />
								<v-text-field :model-value="authStore.user?.email" label="Email" readonly />
								<v-text-field :model-value="authStore.user?.role.displayName" readonly label="Role" />
							</v-card-text>
						</v-card>
					</v-tabs-window-item>

					<v-tabs-window-item value="tradingServers">
						<v-card flat title="Servers">
							<v-card-text>
								<v-row>
									<v-col v-for="(item, i) in authStore.user?.tradingServerList" :key="i + 'servers'">
										<v-card flat border class="mb-2">
											<v-card-title class="d-flex align-center">
												<div class="me-4">
													{{ item.displayName }}
												</div>
												<v-spacer />
												<is-active size="x-small" :model-value="item.isActive" />
											</v-card-title>
											<v-card-text>
												<div class="d-flex">
													<div class="me-4">
														<span class="me-1">IP Address</span>
														<span v-if="item.ipAddress">{{ item.ipAddress }}:{{ item.port }}</span>
													</div>
												</div>
											</v-card-text>
										</v-card>
									</v-col>
								</v-row>
							</v-card-text>
						</v-card>
					</v-tabs-window-item>
					<v-tabs-window-item value="2fa">
						2FA
					</v-tabs-window-item>
				</v-tabs-window>
			</v-col>
		</v-row> -->
	</page>
</template>

<script lang="ts" setup>
// const tab = ref('profile')
// const authStore = useAuthStore()

// const { mdAndUp } = useDisplay()
</script>
