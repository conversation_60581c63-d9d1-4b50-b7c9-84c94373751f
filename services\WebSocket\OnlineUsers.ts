import type { Socket } from 'socket.io-client'
import { WebSocketService } from './Base'
import type { ClientToServerEvents, ServerToClientEvents } from '~/types/Common/Socket.IO'

export interface SocketData {

}
export interface Response extends ServerToClientEvents {
	update: (data: SocketData) => void
}

export interface Request extends ClientToServerEvents {
	get: (room: string) => void
}
export class OnlineSessions extends WebSocketService<Socket<Response, Request>> {
	constructor() {
		super('/online-sessions')
	}
}
