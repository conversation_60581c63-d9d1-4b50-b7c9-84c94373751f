<template>
	<page :title="$t('prices-history.1-min-history')">
		<template #actions>
			<v-btn
				prepend-icon="i-mdi:export-variant"
				color="primary"
				:loading="isExporting"
				:disabled="!hasData"
				@click="exportData"
			>
				{{ $t('prices-history.export-data') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/report/historical-symbol-prices"
			:headers="headers"
			:api-options="{ immediate: false }"
			no-search
			:on-mounted="table?.showFilter()"
			:default-model="{ period: PeriodPrice.S1, fromDate: null, toDate: null, symbol: '' }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
			items-per-page="25"
			persistent-filter
		>
			<template #filter="{ model }">
				<symbols-lookup
					v-model="model.symbol"
					component="combobox"
					return="symbol"
					:label="$t('prices-history.symbol')"
					:rules="rules({ required: true })"
				/>

				<date-time-input
					v-model="model.fromDate"
					:rules="rules({ required: true })"
					:max="model.toDate || now"
					:label="$t('prices-history.from')"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('prices-history.use-mt-timezone')"
				/>
				<date-time-input
					v-model="model.toDate"
					:rules="rules({ required: true })"
					:label="$t('prices-history.to')"
					:min="model.fromDate"
					:max="now"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('prices-history.use-mt-timezone')"
				/>

				<!-- :date-picker-props="{min: dayjs(model.fromDate), max: new Date()}" -->
				<!-- <v-label>Period</v-label> -->
				<v-input class="mt-2">
					<v-btn-toggle
						v-model="model.period"
						mandatory
						color="primary"
						class="w-100"
						rounded="lg"
						border
					>
						<v-btn
							v-for="period, p in periods"
							:key="period.value "
							:value="period.value"
							:class="{ 'border-e': (p !== periods.length-1) }"
							size="small"
							class="flex-grow-1"
						>
							{{ period.title }}
						</v-btn>
					</v-btn-toggle>
				</v-input>
			</template>
		</datatable>
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'

enum PeriodPrice {
	T = 0,
	S1 = 1,
	M1 = 2,
	H1 = 3,
	D1 = 4,
	MN1 = 5,
}

const { t } = useI18n()

const now = useNow()

const { $api } = useNuxtApp()

const { successSnackbar, errorSnackbar } = useSnackbar()

const periods = enumToItems(PeriodPrice).filter(x => x.value !== PeriodPrice.T)

definePageMeta({
	permission: 'oneMinuteHistoryPrices.view',
})

const isExporting = ref(false)

const hasData = computed(() => table.value?.items.length > 0)

const exportData = () => {
	isExporting.value = true

	const query = table.value?.query

	const { cloned: clonedQuery } = useCloned(query, { deep: true })

	delete clonedQuery.value.page
	delete clonedQuery.value.pageSize

	$api('/report/historical-symbol-prices/export', {
		params: {
			...clonedQuery.value,
		},
	})
		.then(() => {
			successSnackbar({
				title: t('prices-history.exporting-data'),
				text: t('prices-history.the-data-is-being-exported'),
			})
		})
		.catch((e) => {
			errorSnackbar(e.value.message)
		})
		.finally(() => {
			isExporting.value = false
		})
}
const table = ref<typeof Datatable | null>(null)

const headers: Headers = [
	{
		title: t('prices-history.open-price'),
		value: 'openPrice',
	},
	{
		title: t('prices-history.close-price'),
		value: 'closePrice',
	},
	{
		title: t('prices-history.high-price'),
		value: 'highPrice',
	},
	{
		title: t('prices-history.low-price'),
		value: 'lowPrice',
		align: 'center',
	},
	{
		title: t('prices-history.tick-volume'),
		value: 'tickVolume',
	},
	{
		title: t('prices-history.date'),
		value: 'date',
		headerProps: {
			// $children: {
			// 	tag: 'v-icon',
			// 	$props: {
			// 		color: 'accent',

			// 	},
			// },
		},
	},

]

// const minToDateTime = (date)=>{

// }
</script>
