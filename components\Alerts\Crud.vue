<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="alert"
		item-identity="name"
		:navigation-drawer-props="{ width: 500 }"
		:url="Alerts._Id.URL(':id')"
		v-bind="$attrs"
		@loaded="selectedCategories = useCloned($event.categoryIds).cloned.value"
	>
		<template #default="{ item, errors }: { item: Item<Alerts._Id.GETResponse | Alerts._Id.POSTRequest | Alerts._Id.PUTRequest>, errors: ItemErrors<Alerts._Id.GETResponse>, isCreateAction: boolean}">
			<v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				:rules="rules({ required: true })"
				label="Name"
			/>

			<api-items
				v-slot="props"
				url="/categories/lookup"
				:error-messages="errors.categoryIds"
				@loaded="categories = $event"
			>
				<v-select
					v-model="item.categoryIds"
					:rules="rules({ required: true })"
					label="Category"
					v-bind="props"
					item-title="name"
					item-value="id"
					multiple
					chips
					closable-chips
					chip
					@update:model-value="selectedCategories = $event"
				/>
			</api-items>
			<v-autocomplete
				v-model="item.excludedSymbols"
				label="Exclude Symbols"
				:chips="true"
				:closable-chips="true"
				:disabled="!item.categoryIds.length"
				:items="excludeItems"
				multiple
			/>
			<v-radio-group
				v-model="item.type"
				label="Alert should trigger when:"
				:rules="rules({ required: true })"
			>
				<div>
					<div class="d-flex align-center ga-4 w-100 mb-2">
						<v-radio
							label="Price stops changing for: "
							class=""
							:value="Alerts.AlertType.STOP_DURATION"
						/>

						<v-text-field
							v-if="item.type === Alerts.AlertType.STOP_DURATION"
							v-model.number="item.configValue"
							type="number"
							max-width="150"
							suffix="minutes"
							persistent-placeholder
							hide-details="auto"
							single-line
							density="compact"
							variant="solo-filled"
							flat
							hide-spin-buttons
							:rules="rules({ required: true })"
						/>
					</div>
				</div>
				<div class="d-flex align-center ga-4 w-100 mb-2">
					<v-radio
						label="Daily Price change is more than: "
						:value="Alerts.AlertType.PERCENTAGE"
					/>

					<v-text-field
						v-if="item.type === Alerts.AlertType.PERCENTAGE"
						v-model.number="item.configValue"
						type="number"
						max-width="150"
						suffix="%"
						persistent-placeholder
						hide-details="auto"
						single-line
						density="compact"
						variant="solo-filled"
						flat
						hide-spin-buttons
						:disabled="item.type !== Alerts.AlertType.PERCENTAGE"
						:rules="rules({ required: true })"
					/>
				</div>
				<div class="d-flex align-center ga-4 w-100">
					<v-radio
						label="Spread changes to (or higher):"
						:value="Alerts.AlertType.SPREAD"
					/>

					<v-text-field
						v-if="item.type === Alerts.AlertType.SPREAD"
						v-model.number="item.configValue"
						type="number"
						max-width="150"
						suffix="%"
						persistent-placeholder
						hide-details="auto"
						single-line
						density="compact"
						variant="solo-filled"
						flat
						hide-spin-buttons
						:disabled="item.type !== Alerts.AlertType.SPREAD"
						:rules="rules({ required: true })"
					/>
				</div>
			</v-radio-group>

			<v-combobox
				v-model="item.emails"
				label="Emails"
				chips
				closable-chips
				multiple
				:delimiters="[',', ' ']"
				:rules="[rule({ required: true, type: 'array', min: 1 }), validateEmails]"
			>
				<template #chip="{ props }">
					<v-chip
						v-bind="props"
						color="indigo"
					/>
				</template>
			</v-combobox>
			<!-- <v-radio
				:label="$t('TradingServersCrud.rest-api')"
				:value="TradingServers.IntegrationType.REST_API"
			/> -->
			<!-- </v-radio-group> -->
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type { DefaultItem, Item, ItemErrors, URLVars } from '~/components/Crud.vue'
import type { CategoriesSymbols } from '~/types/'
import { Alerts } from '~/types/'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Alerts._Id.POSTRequest | Alerts._Id.PUTRequest> = {
	name: '',
	categoryIds: [],
	excludedSymbols: [],
	emails: [],
	type: Alerts.AlertType.STOP_DURATION,
	configValue: null,
}

const categories = ref<CategoriesSymbols.Record[]>([])

const selectedCategories = ref<CategoriesSymbols.Record['categoryId'][]>([])

const excludeItems = computed(() => {
	const nestedSymbols = categories.value.filter(category => selectedCategories.value.includes(category.id)).map(c => c.symbolsCategory.map(sc => sc.name))
	return [...new Set(nestedSymbols.flat())]
})

const validateEmails = async (v: string[]) => {
	if (!v || v.length === 0) return true

	const errors: string[] = []

	// Use Promise.all to wait for all validations to complete
	await Promise.all(v.map(async (email: string) => {
		const validation = await rule({ type: 'email', message: `"${email}" is not a valid email` })(email)
		if (typeof validation === 'string') {
			errors.push(validation)
		}
	}))

	return errors.length === 0 ? true : errors[0]
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: Alerts.Record) => crudRef.value?.update(item as unknown as URLVars),
	delete: (item: Alerts.Record) => crudRef.value?.delete(item as unknown as URLVars),
})
</script>
