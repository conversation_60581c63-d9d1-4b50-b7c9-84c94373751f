<template>
	<v-container>
		<div class="mb-4">
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.show-to-regular-managers')"
				:false-icon="$vuetify.icons.aliases?.checkboxOn + ' opacity-100'"
				:true-icon="$vuetify.icons.aliases?.checkboxOff"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_TECHNICAL"
			/>
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.include-in-server-reports')"
				:false-icon="$vuetify.icons.aliases?.checkboxOn + ' opacity-100'"
				:true-icon="$vuetify.icons.aliases?.checkboxOff"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_EXCLUDE_REPORTS"
			/>
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.enable-daily-reports')"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_REPORTS"
			/>
		</div>

		<div class="mb-4">
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.enable-trading')"
				:false-icon="$vuetify.icons.aliases?.checkboxOn + ' opacity-100'"
				:true-icon="$vuetify.icons.aliases?.checkboxOff"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_TRADE_DISABLED"
				@update:model-value="enableTradingHandler"
			/>
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.enable-algo-trading-by-ex')"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_EXPERT"
				:disabled="rightsModel.includes(Accounts.MT5.EnUsersRights.USER_RIGHT_TRADE_DISABLED)"
			/>
			<v-checkbox
				v-model="rightsModel"
				color="primary"
				hide-details
				class="compact"
				:label="$t('Accounts.MT5.Crud.enable-trailing-stops')"
				:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_TRAILING"
				:disabled="rightsModel.includes(Accounts.MT5.EnUsersRights.USER_RIGHT_TRADE_DISABLED)"
			/>
		</div>

		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.enable-api-connections')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_API_ENABLED"
		/>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.enable-sponsored-vps-host')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_SPONSORED_HOSTING"
		/>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			class="compact"
			:label="$t('Accounts.MT5.Crud.allow-access-to-subscript')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_ALLOW_DATAFEED"
		/>
		<v-combobox
			v-model.number="item.limitPositionsValue"
			type="number"
			hide-spin-buttons
			:label="$t('Accounts.MT5.Crud.limit-total-value-of-position')"
			:items="limitItems"
			:return-object="false"
			suffix="USD"
		/>
		<v-combobox
			v-model.number="item.limitOrders"
			type="number"
			hide-spin-buttons
			:label="$t('Accounts.MT5.Crud.limit-number-of-active-or')"
			:items="limitItems"
			:return-object="false"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts } from '~/types'
// import { AccountsTrades } from '~/types'

const { t } = useI18n()

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const computedRights = computed({
	get: () => p.item.rights,
	set: v => emit('update:item', { rights: v }),
})

const { model: rightsModel } = useMultiSelectEnum(computedRights, Accounts.MT5.EnUsersRights)

const enableTradingHandler = (v: number[] | null) => {
	if (!v) {
		return
	}

	const isSelected = v.includes(Accounts.MT5.EnUsersRights.USER_RIGHT_TRADE_DISABLED)
	if (isSelected) {
		nextTick(() => {
			rightsModel.value = rightsModel.value.slice().filter(x => ![Accounts.MT5.EnUsersRights.USER_RIGHT_EXPERT, Accounts.MT5.EnUsersRights.USER_RIGHT_TRAILING].includes(x))
		})
	}
}

const limitItems = ref([{
	title: t('Accounts.MT5.Crud.default'),
	value: 0,
}])
</script>
