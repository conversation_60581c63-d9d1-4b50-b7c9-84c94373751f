<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Transaction"
		:navigation-drawer-props="{ width: 800 }"
		url="/transactions/:id"
		v-bind="$attrs"
	>
		<template #title="{}">
			{{ $t('ViewTransactionCrud.view-transaction') }}
		</template>
		<template #default="{ item }: { item: Item<Transactions._Id.GETResponse>, errors: ItemErrors<Transactions._Id.GETResponse>}">
			<v-row>
				<v-col
					cols="12"
					md="auto"
				>
					<transactions-timeline
						v-if="passedItem"
						:id="passedItem.id"
					/>
				</v-col>
				<v-divider
					vertical
					class="mx-4"
				/>
				<v-col>
					<v-row dense>
						<v-col v-bind="labelColProps">
							<b>Status:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							<transactions-status-label :model-value="item.status" />
						</v-col>
						<v-col v-bind="labelColProps">
							<b>Requested By:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							{{ item.requestedByUserName }}
						</v-col>
						<v-col v-bind="labelColProps">
							<b>Requested At:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							{{ dateTime(item.createdAt) }}
						</v-col>
						<v-col v-bind="labelColProps">
							<b>Action:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							{{ item.actionName }}
						</v-col>
						<v-col v-bind="labelColProps">
							<b>Requested Amount:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							{{ money(item.requestedAmount, item.requestedCurrencySymbol!) }}
						</v-col>
						<v-col v-bind="labelColProps">
							<b>To Account:</b>
						</v-col>
						<v-col v-bind="valueColProps">
							{{ item.receiverAccount }}
						</v-col>
					</v-row>
				</v-col>
			</v-row>
		</template>
		<template #actions="actionProps">
			<v-btn @click="actionProps.close">
				{{ $t('ViewTransactionCrud.cancel') }}
			</v-btn>
			<v-spacer />
			<v-btn
				v-if="can('transactions.execute') && actionProps.item.canExecute"
				variant="flat"
				color="error"
				:disabled="actionProps.item.status !== Transactions.Status.Pending"
				@click="reject(actionProps)"
			>
				{{ $t('ViewTransactionCrud.reject') }}
			</v-btn>
			<v-btn
				v-if="can('transactions.execute') && actionProps.item.canExecute"
				variant="flat"
				color="success"
				:disabled="actionProps.item.status !== Transactions.Status.Pending"
				@click="approve(actionProps)"
			>
				{{ $t('ViewTransactionCrud.approve') }}
			</v-btn>
		</template>
	</crud>
	<v-dialog
		v-if="can('transactions.execute')"
		v-model="dialogModel"
		max-width="400"
	>
		<v-form ref="commentFormRef">
			<v-card>
				<v-card-title>{{ $t('ViewTransactionCrud.actiontitle-transaction', [actionTitle]) }}</v-card-title>
				<v-card-text>
					<v-textarea
						v-model="commentModel"
						:label="$t('ViewTransactionCrud.comment')"
						:placeholder="$t('ViewTransactionCrud.please-enter-your-comment')"
						:rules="[rule({ required: isRejectAction })]"
						:error-messages="errors.comment"
					/>
				</v-card-text>
				<v-card-actions>
					<v-btn @click="dialogModel = false">
						{{ $t('ViewTransactionCrud.cancel-0') }}
					</v-btn>
					<v-spacer />
					<v-btn
						:color="color"
						:loading="isSaving"
						@click="update"
					>
						{{ actionTitle }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-form>
	</v-dialog>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import type Crud from './Crud.vue'
import type { DefaultItem, Item, ItemErrors } from './Crud.vue'
import { Transactions } from '~/types/Transactions'

const { $api } = useNuxtApp()

const { t } = useI18n()

const passedItem = ref<{ id: number } | null>(null)

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const commentFormRef = ref<InstanceType<typeof VForm> | null >()

const commentModel = ref('')

const action = ref('')

const itemId = ref('')

const isSaving = ref(false)

const dialogModel = ref(false)

const labelColProps = ref({
	cols: 12,
	md: 5,
	class: 'text-subtitle-1 font-weight-regular',
})

const valueColProps = ref({
	cols: 12,
	md: 7,
	class: 'text-subtitle-1',
})

const defaultItem: DefaultItem<Transactions._Id.PUTRequest> = {
	comment: '',
	status: 0,
}

const color = computed(() => {
	switch (action.value) {
		case 'approve':
			return 'success'
		case 'reject':
			return 'error'
		default:
			return 'grey'
	}
})

const actionTitle = computed(() => {
	switch (action.value) {
		case 'approve':
			return t('ViewTransactionCrud.approve')
		case 'reject':
			return t('ViewTransactionCrud.reject')
		default:
			return t('TransactionsStatusLabel.unknown')
	}
})

const emit = defineEmits(['created', 'updated', 'deleted'])

// const isApproveAction = computed(() => action.value === 'approve')

const isRejectAction = computed(() => action.value === 'reject')

const approve = (actionProps: any) => {
	itemId.value = actionProps.item.id

	action.value = 'approve'
	dialogModel.value = true
}

const reject = (actionProps: any) => {
	itemId.value = actionProps.item.id
	action.value = 'reject'
	dialogModel.value = true
}

const { errors, handler } = useFormHandler(defaultItem)

const update = async () => {
	const validation = await commentFormRef.value?.validate()

	if (validation?.valid) {
		isSaving.value = true
		$api(`/transactions/${itemId.value}`, {
			method: 'PUT',
			body: {
				comment: commentModel.value,
				status: action.value === 'approve' ? Transactions.Status.Approved : Transactions.Status.Rejected,
			},
		})
			.then((res) => {
				dialogModel.value = false
				commentFormRef.value?.reset()
				commentFormRef.value?.resetValidation()
				commentModel.value = '' // null will create error in the backend
				emit('updated', res)
				crudRef.value?.close()
			})
			.catch(handler)
			.finally(() => {
				isSaving.value = false
			})
	}
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => {
		passedItem.value = item
		crudRef.value?.update(item)
	},
	delete: (item: any) => {
		passedItem.value = item
		crudRef.value?.delete(item)
	},
})
</script>
