<template>
	<v-switch
		v-model="themeCookie"
		v-tooltip:bottom="`Switch Theme to ${themeCookie === 'dark' ? 'Light' : 'Dark'}`"
		color="success"
		true-value="dark"
		false-value="light"
		true-icon="i-mdi:weather-night"
		false-icon="i-mdi:weather-sunny"
		hide-details
		inset
		@update:model-value="themeHandler($event as Theme)"
	/>
</template>

<script lang="ts" setup>
const prefStore = usePreferencesStore()

const themeCookie = useThemeCookie()

const theme = useTheme()

const themeHandler = (themeName: Theme) => {
	prefStore.updateKey('theme', themeName)
	theme.global.name.value = themeName // to update useTheme() hook
}
</script>

<style>

</style>
