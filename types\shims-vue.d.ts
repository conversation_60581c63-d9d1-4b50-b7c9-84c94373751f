import type {
	ComponentCustomOptions as _ComponentCustomOptions,
	ComponentCustomProperties as _ComponentCustomProperties,
} from 'vue'
import type { Permissions } from '~/types/Permissions'
// https://github.com/nuxt/nuxt/issues/28869#issuecomment-2406407082

declare module '~/node_modules/nuxt/dist/pages/runtime/composables' {
	interface PageMeta {
		permission?: Permissions.Names
	}
}

declare module '@vue/runtime-core' {
	interface ComponentCustomProperties extends _ComponentCustomProperties {}
	interface ComponentCustomOptions extends _ComponentCustomOptions {}
}
