[{"name": "accounts", "permissions": [{"id": 52, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 53, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 54, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 55, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "actions", "permissions": [{"id": 1, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 2, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 3, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 21, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "actionTypes", "permissions": [{"id": 10, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 11, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 12, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 24, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "alerts", "permissions": [{"id": 77, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 78, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 79, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 80, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "category", "permissions": [{"id": 69, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 70, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 71, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 72, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "currencies", "permissions": [{"id": 27, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 28, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 29, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 30, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "group", "permissions": [{"id": 49, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 50, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 51, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 64, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "hedging", "permissions": [{"id": 65, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 66, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 67, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 68, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "journals", "permissions": [{"id": 45, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "manager", "permissions": [{"id": 56, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 57, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 58, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 59, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "oneMinuteHistoryPrices", "permissions": [{"id": 41, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "onlineTraders", "permissions": [{"id": 39, "name": "block", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 40, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "operations", "permissions": [{"id": 13, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 14, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 15, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 25, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "reports", "permissions": [{"id": 19, "name": "prices", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 20, "name": "deals", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 38, "name": "traders", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "roles", "permissions": [{"id": 7, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 8, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 9, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 23, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "serverlogs", "permissions": [{"id": 81, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "symbol", "permissions": [{"id": 46, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 47, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 48, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 60, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 61, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 62, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 63, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "symbolcategory", "permissions": [{"id": 73, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 74, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 75, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 76, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "ticksHistoryPrices", "permissions": [{"id": 42, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "tradingPlatform", "permissions": [{"id": 35, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "tradingServers", "permissions": [{"id": 16, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 17, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 18, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 26, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "transactions", "permissions": [{"id": 31, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 32, "name": "execute", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 33, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 34, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "transactionWorkflow", "permissions": [{"id": 43, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 44, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "users", "permissions": [{"id": 4, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 5, "name": "delete", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 6, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 22, "name": "create", "createdAt": "0001-01-01 00:00:00 +00:00"}]}, {"name": "workflow", "permissions": [{"id": 36, "name": "view", "createdAt": "0001-01-01 00:00:00 +00:00"}, {"id": 37, "name": "edit", "createdAt": "0001-01-01 00:00:00 +00:00"}]}]