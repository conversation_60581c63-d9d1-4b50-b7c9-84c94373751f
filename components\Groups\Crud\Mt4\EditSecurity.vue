<template>
	<v-defaults-provider
		:defaults="{
			global: {
				density: 'compact',
				disabled: !model.show,
			},
		}"
	>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<fieldset class="h-100">
					<legend>{{ $t('Groups.MT4.Crud.EditSecurity.general') }}</legend>
					<v-checkbox
						v-model="model.show"
						:true-value="1"
						:false-value="0"
						hide-details
						:label="$t('Groups.MT4.Crud.EditSecurity.enabled')"
						color="primary"
						:disabled="false"
					/>
					<v-checkbox
						v-model="model.trade"
						:true-value="1"
						:false-value="0"
						hide-details
						:label="$t('Groups.MT4.Crud.EditSecurity.trade')"
						color="primary"
					/>

					<v-checkbox
						v-model="model.confirmation"
						:true-value="1"
						:false-value="0"
						color="primary"
						:label="$t('Groups.MT4.Crud.EditSecurity.use-confirmations-in-requ')"
					/>

					<v-select
						v-model="model.execution"
						:items="executionItems"
						:label="$t('Groups.MT4.Crud.EditSecurity.execution')"
					/>

					<v-select
						v-model="model.spreadDiff"
						:items="Array.from({ length: 7 }, (_, i) => i -3)"
						:label="$t('Groups.MT4.Crud.EditSecurity.spread-difference')"
					/>

					<v-checkbox
						v-model="model.freeMarginMode"
						:true-value="1"
						:false-value="0"
						color="primary"
						:label="$t('Groups.MT4.Crud.EditSecurity.do-not-check-free-margin-')"
					/>

					<v-select
						v-model="model.ieDeviation"
						:items="Array.from({ length: 6 }, (_, i) => i)"
						:label="$t('Groups.MT4.Crud.EditSecurity.maximum-deviation')"
						suffix="points"
						hint="IE Only"
					/>

					<v-checkbox
						v-model="model.ieQuickMode"
						:true-value="1"
						:false-value="0"
						color="primary"
						:label="$t('Groups.MT4.Crud.EditSecurity.fast-confirmation-on-ie-w')"
					/>
				</fieldset>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<fieldset>
					<legend>
						{{ $t('Groups.MT4.Crud.EditSecurity.close-by') }}
					</legend>
					<v-row>
						<v-col
							cols="12"
							md="5"
						>
							<v-checkbox
								:model-value="!tradeRightsModel.includes(Groups.MT4.TradeRights.TRADE_DENY_CLOSEBY)"
								in
								hide-details
								:label="$t('Groups.MT4.Crud.EditSecurity.enable')"
								color="primary"
								@update:model-value="tradeRightsHandler(Groups.MT4.TradeRights.TRADE_DENY_CLOSEBY)"
							/>
						</v-col>
						<v-col>
							<v-checkbox
								:model-value="!tradeRightsModel.includes(Groups.MT4.TradeRights.TRADE_DENY_MUCLOSEBY)"
								:disabled="tradeRightsModel.includes(Groups.MT4.TradeRights.TRADE_DENY_CLOSEBY)"
								hide-details
								:label="$t('Groups.MT4.Crud.EditSecurity.multiple-close-by-orders')"
								color="primary"
								@update:model-value="tradeRightsHandler(Groups.MT4.TradeRights.TRADE_DENY_MUCLOSEBY)"
							/>
						</v-col>
					</v-row>
					<v-select
						v-model="model.autoCloseoutMode"
						:items="autocloseoutModeItems"
						:label="$t('Groups.MT4.Crud.EditSecurity.auto-close-out')"
					/>
				</fieldset>
				<fieldset>
					<legend>
						{{ $t('Groups.MT4.Crud.EditSecurity.lots') }}
					</legend>
					<v-row>
						<v-col
							cols="12"
							md="4"
						>
							<v-text-field
								v-model.number="model.lotMin"
								:label="$t('Groups.MT4.Crud.EditSecurity.min')"
							/>
						</v-col>
						<v-col
							cols="12"
							md="4"
						>
							<v-text-field
								v-model.number="model.lotMax"
								:label="$t('Groups.MT4.Crud.EditSecurity.max')"
							/>
						</v-col>
						<v-col
							cols="12"
							md="4"
						>
							<v-combobox
								v-model.number="model.lotStep"
								:label="$t('Groups.MT4.Crud.EditSecurity.step')"
								:items="[0.1, 1.0, 10.0, 100.0]"
							/>
						</v-col>
					</v-row>
				</fieldset>
				<fieldset>
					<legend>
						{{ $t('Groups.MT4.Crud.EditSecurity.commissions') }}
					</legend>
					<v-row no-gutters>
						<v-col
							cols="12"
							md="12"
							class="d-flex"
						>
							<v-text-field
								v-model.number="model.commBase"
								type="number"
								:label="$t('Groups.MT4.Crud.EditSecurity.standard')"
								max-width="40%"
								rounded="ts te-0"
							/>
							<v-select
								v-model="model.commType"
								:items="commTypeItems"
								max-width="30%"
								:rounded="false"
							/>
							<v-select
								v-model="model.commLots"
								:items="commLotsItems"
								max-width="30%"
								rounded="te ts-0"
							/>
						</v-col>
						<v-col
							cols="12"
							md="5"
						>
							<v-text-field
								v-model.number="model.commTax"
								:label="$t('Groups.MT4.Crud.EditSecurity.taxes')"
								type="number"
								:rules="rules({ type: 'number', min: 0, max: 100 })"
								suffix="%"
							/>
						</v-col>
					</v-row>
					<v-row no-gutters>
						<v-col
							cols="12"
							class="d-flex"
						>
							<v-text-field
								v-model.number="model.commAgent"
								type="number"
								:label="$t('Groups.MT4.Crud.EditSecurity.agent')"
								max-width="40%"
								rounded="ts te-0"
							/>
							<v-select
								v-model="model.commAgentType"
								:items="commTypeItems"
								max-width="30%"
								:rounded="false"
							/>
							<v-select
								v-model="model.commAgentLots"
								:items="commLotsItems"
								max-width="30%"
								rounded="te ts-0"
							/>
						</v-col>
					</v-row>
				</fieldset>
			</v-col>
		</v-row>
	</v-defaults-provider>
</template>

<script lang="ts" setup>
import { Groups } from '~/types'

const model = defineModel<Groups.MT4.GroupSec>({
	default: () => ({}),
})

const executionItems = enumToItems(Groups.MT4.ExecutionMode, 'Groups.MT4.ExecutionMode')

const autocloseoutModeItems = enumToItems(Groups.MT4.AutoCloseoutMode, 'Groups.MT4.AutoCloseoutMode')

const commTypeItems = enumToItems(Groups.MT4.CommissionUnit, 'Groups.MT4.CommissionUnit')

const commLotsItems = enumToItems(Groups.MT4.CommissionCalculationType, 'Groups.MT4.CommissionCalculationType')

const computedTradeRights = computed({
	get: () => model.value.tradeRights,
	set: (value: number) => {
		model.value.tradeRights = value
	},
})

const { model: tradeRightsModel } = useMultiSelectEnum(computedTradeRights, Groups.MT4.TradeRights)

const tradeRightsHandler = (value: number) => {
	const clonedTradeRightsModel = [...tradeRightsModel.value]

	// toggle the value from tradeRightsModel
	const index = tradeRightsModel.value.indexOf(value)

	if (index === -1) {
		clonedTradeRightsModel.push(value)
		if (value === Groups.MT4.TradeRights.TRADE_DENY_CLOSEBY) {
			// remove Groups.MT4.TradeRights.TRADE_DENY_MUCLOSEBY from tradeRightsModel
			const index = tradeRightsModel.value.indexOf(Groups.MT4.TradeRights.TRADE_DENY_MUCLOSEBY)
			if (index === -1) {
				clonedTradeRightsModel.push(Groups.MT4.TradeRights.TRADE_DENY_MUCLOSEBY)
			}
		}
	} else {
		clonedTradeRightsModel.splice(index, 1)
	}

	tradeRightsModel.value = clonedTradeRightsModel
}
</script>
