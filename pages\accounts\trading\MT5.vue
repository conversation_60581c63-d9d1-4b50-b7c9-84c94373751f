<template>
	<page :title="$t('accounts.accounts')">
		<template #actions>
			<!-- <v-btn v-if="can('group.create')" @click="managerCrudRef?.create()">
				create manager
			</v-btn> -->

			<v-btn
				v-if="can('accounts.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="tradingAccountCrudRef?.create()"
			>
				{{ $t('accounts.create-account') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/accounts/mt5"
			:headers="headers"
			search-key="search"
			item-value="login"
			:default-model="{ name: '' }"
			v-bind="props"
		>
			<template #filter="{ model }">
				<api-items
					v-slot="props"
					:url="Groups.Lookup.URL"
				>
					<v-autocomplete
						v-model="model.group"
						:label="$t('accounts.group')"
						v-bind="props"
						clearable
					/>
				</api-items>
			</template>
			<template #item.name="{ value, item }">
				<manager-mt5-info-tooltip
					v-if="isManager(item)"
					:login="item.login"
					@update="managerCrudRef?.update"
					@create="managerCrudRef?.create({}, { override: $event })"
				>
					{{ value }}
				</manager-mt5-info-tooltip>
				<template v-else>
					{{ value }}
				</template>
			</template>
			<template #item.group="{ value, item }">
				<groups-mt5-info-tooltip

					:group="item.group"
					@update="updateGroup"
				>
					{{ value }}
				</groups-mt5-info-tooltip>
			</template>

			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:loading="isClearingCache"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>
			<template #toolbar.middle-end>
				<v-btn
					v-if="selectedModel.length"
					prepend-icon="i-mdi:content-copy"
				>
					{{ $t('accounts.copy-as') }}
					<v-menu
						activator="parent"
						offset-y
					>
						<v-list
							:items="copyMenuItems"
							density="compact"
						/>
					</v-menu>
				</v-btn>
			</template>
			<template #item.login="{ item, value }">
				<accounts-icon
					platform="MT5"
					:account="item"
					start
				/>
				{{ value }}
			</template>
			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('accounts.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="tradingAccountCrudRef?.update(item)"
					/>
					<v-btn
						v-if="can('accounts.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="tradingAccountCrudRef?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<accounts-crud-mt5
		ref="tradingAccountCrudRef"
		@created="accountCreationHandler"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
	<manager-crud-mt5
		ref="managerCrudRef"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>

	<groups-crud ref="groupCrud" />
</template>

<script lang="ts" setup>
import type TradingAccountCrud from '@/components/Accounts/Crud/Mt5/index.vue'
import type ManagerCrud from '@/components/Manager/Crud/Mt5/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import type { Accounts } from '~/types'
import { Groups } from '~/types'
import type { CrudEventData } from '~/components/Crud.vue'

import GroupsCrud from '@/components/Groups/Crud/Mt5/index.vue'

const groupCrud = ref<InstanceType<typeof GroupsCrud>>()

const table = ref <InstanceType<typeof Datatable> | null>(null)

const tradingAccountCrudRef = ref<InstanceType<typeof TradingAccountCrud>>()

const managerCrudRef = ref<InstanceType<typeof ManagerCrud>>()

const isClearingCache = ref(false)

const { t } = useI18n()

const headers: Headers = [
	{
		title: t('accounts.login'),
		value: 'login',
		nowrap: true,
	},
	{
		title: t('accounts.name'),
		value: 'name',
		nowrap: true,
	},
	{
		title: t('accounts.group'),
		value: 'group',
		nowrap: true,
	},
	{
		title: t('accounts.city'),
		value: 'city',
		nowrap: true,
	},
	{
		title: t('accounts.email'),
		value: 'eMail',
	},
	{
		title: t('accounts.agent'),
		value: 'agent',
	},
	{
		value: 'actions',
	},
]

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/accounts/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
	}).finally(() => {
		isClearingCache.value = false
	})
}

const accountCreationHandler = ({ response }: CrudEventData) => {
	table.value?.refresh()

	if (response?.group?.startsWith('manager')) {
		setTimeout(() => {
			managerCrudRef.value?.update(response)
		}, 500)
	}
}

const updateGroup = (item: any) => {
	groupCrud.value?.update(item)
}

const { props, model: selectedModel } = useDatatableSelection<Accounts.MT5._Id.GETResponse>()

const { copy } = useClipboard()

const { infoSnackbar } = useSnackbar()

const copyAsLogins = () => {
	const data = selectedModel.value.map((item: any) => item.login).join(',')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyAsRows = () => {
	const itemKeys = headers.map(header => header.value) as (keyof Accounts.MT5._Id.GETResponse)[]

	const data = selectedModel.value.map(item =>
		itemKeys.map(key => item[key]).join(','),
	).join('\n')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyMenuItems = [
	{
		title: t('accounts.logins'),
		props: {
			onClick: copyAsLogins,
		},
	},
	{
		title: t('accounts.rows'),
		props: {
			onClick: copyAsRows,
		},
	},
]

const isManager = (item: Accounts.MT5._Id.GETResponse) => item.group.startsWith('manager')
</script>
