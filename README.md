# Ingot Pilot (Frontend)

At INGOT, we are committed to continually improving our internal processes and the manner in which we control our complex trading platforms. Therefore, within the scope of this dedication to excellence, we have come up with INGOT Pilot – a sophisticated web application that serves as a powerful interface for MetaTrader 4 and 5 administrators and managers.

INGOT Pilot is more than just an instrument; it is an embodiment of our enduring quest for advancement, efficiency and operational supremacy. The current system contains all modern functional features required by project teams together with comprehensive reporting capabilities on which they rely when performing their daily activities. With such functionalities integrated into it, INGOT Pilot helps us make our workflow efficient while also making it more flexible thus enabling dynamic control over trading operations.

The development of INGOT Pilot is a strategic move in the organization that aims at equipping the internal units with effective tools for managing trading environments accurately and confidently. This application has been carefully developed to be in line with our broader goals thereby supporting our internal strategy to trade smoothly and efficiently.

Every single detail in INGOT Pilot reflects our commitment towards continuous improvement. We hold strongly onto through empowering or giving power back to our teams- that way we can solve anything.


## Setup

Make sure to install the dependencies:

```bash
cp .env.example .env
```
```bash
npm install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
npm run dev
```

## Production

Build the application for production:

```bash
npm run generate
```

Locally preview production build:

```bash
npm run preview
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

Look at the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) to learn more.
