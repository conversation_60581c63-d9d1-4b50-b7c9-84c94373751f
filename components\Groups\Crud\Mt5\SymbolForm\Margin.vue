<template>
	<div>
		<DefaultsGroup
			v-model="useMultipleDefaultModel(item, ['marginInitialDefault', 'marginHedgedDefault', 'marginMaintenanceDefault']).value"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.use-default-margin-values')"
			:keys="[
				'marginInitial',
				'marginHedged',
				'marginMaintenance',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-text-field
				v-model.number="item.marginInitial"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.initial-margin')"
				type="number"
			/>
			<v-text-field
				v-model.number="item.marginHedged"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.hedged-margin')"
				type="number"
			/>
			<v-text-field
				v-model.number="item.marginMaintenance"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.maintenance-margin')"
				type="number"
			/>
		</DefaultsGroup>

		<DefaultsGroup
			v-model="item.marginFlagsDefault"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.use-default-margin-setting')"
			:keys="[
				'marginFlags',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-checkbox
				v-model="marginFlagModel"
				color="primary"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_HEDGE_LARGE_LEG"
				hide-details
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.calculate-hedged-margin')"
			/>
			<v-checkbox
				v-model="marginFlagModel"
				color="primary"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_EXCLUDE_PL"
				hide-details
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.exclude-long-position-pnl')"
			/>
			<v-checkbox
				v-model="marginFlagModel"
				color="primary"
				:true-value="Symbols.MT5.MarginFlags.MARGIN_FLAGS_RECALC_RATES"
				hide-details
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.recalculate-margin-exchange')"
			/>

			<v-select
				v-model="computedAdditionalMarginChecksModel"
				multiple
				chips
				closable-chips
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.additional-margin-checks')"
				:items="marginFlagItems"
			/>
		</DefaultsGroup>
	</div>
</template>

<script lang="ts" setup>
import DefaultsGroup from './Components/DefaultsGroup.vue'
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import { Symbols } from '~/types'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const computedMarginFlag = computed({
	get: () => {
		return p.item.marginFlags
	},
	set: (value) => {
		emit('update:item', { marginFlags: value })
	},
})

const { model: marginFlagModel } = useMultiSelectEnum(computedMarginFlag, Symbols.MT5.MarginFlags)

const { t } = useI18n()

const marginFlagItems = [
	{
		title: t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.check-before-executing'),
		value: Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_PROCESS,
	},
	{
		title: t('Groups.MT5.Crud.Symbols.SymbolForm.Margin.check-on-sl-tp-trigger'),
		value: Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_SLTP,
	},
]

const computedAdditionalMarginChecksModel = computed<Symbols.MT5.MarginFlags[]>({
	get: () => (marginFlagModel.value).filter(flag => [
		Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_PROCESS,
		Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_SLTP,
	].includes(flag)),
	set: (value: Symbols.MT5.MarginFlags[]) => {
		const flags = marginFlagModel.value.filter(flag => ![
			Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_PROCESS,
			Symbols.MT5.MarginFlags.MARGIN_FLAGS_CHECK_SLTP,
		].includes(flag))
		marginFlagModel.value = flags.concat(value)
	},
})
</script>
