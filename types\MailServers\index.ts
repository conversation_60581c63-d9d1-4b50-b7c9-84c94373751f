export namespace MailServers {
	// export namespace _Id {
	// 	export const URL = (_Id: number | string) => `/actions/${_Id}`
	// 	export interface ActionType {
	// 		id: number;
	// 		name: string;
	// 		displayName: string;
	// 		isIncremental: boolean;
	// 		operationId: number;
	// 	}
	// 	export interface GETResponse {
	// 		id: number;
	// 		comment: string;
	// 		displayName: string;
	// 		onlyReceiver: boolean;
	// 		actionTypeId: number;
	// 		actionType: ActionType;
	// 	}

	// 	export interface PUTRequest {
	// 		comment: string;
	// 		displayName: string;
	// 		description: string;
	// 		actionTypeId: number;
	// 		onlyReceiver: boolean;
	// 	}

	// 	export interface POSTRequest {
	// 		comment: string;
	// 		displayName: string;
	// 		description: string;
	// 		actionTypeId: number;
	// 		onlyReceiver: boolean;
	// 	}

	// 	export namespace Workflow {

	// 		export const URL = (_Id: number | string) => `${Actions._Id.URL(_Id)}/workflow`

	// 		export interface GETResponse {
	// 			allowedRoles: number[];
	// 			reviewRoles: number[];
	// 			approvalRoles: {
	// 						actionId: number;
	// 						roleId: number;
	// 						order: number;
	// 						transactionAmountLimit: number;
	// 						transactionDailyLimit: number;
	// 						transactionMonthlyLimit: number;
	// 						transactionYearlyLimit: number;
	// 						createdAt: string;
	// 						updatedAt: null;
	// 					}[];
	// 			approvalUsers: unknown[];
	// 			fallbackRole: number;
	// 		}

	// 		export interface PUTRequest {
	// 			allowedRoles: number[];
	// 			reviewRoles: number[];
	// 			fallbackRole: number;
	// 			approvalRoles: {
	// 						roleId: number;
	// 						transactionAmountLimit: number;
	// 						transactionDailyLimit: null;
	// 						transactionMonthlyLimit: null;
	// 						transactionYearlyLimit: null;
	// 					}[];
	// 			approvalUsers: {
	// 						userId: number;
	// 						transactionAmountLimit: number;
	// 						transactionDailyLimit: null;
	// 						transactionMonthlyLimit: null;
	// 						transactionYearlyLimit: null;
	// 					}[];
	// 		}
	// 	}
	// }
	export namespace Lookup {
		export const URL = '/mail-server/email/lookup'

		export interface SingleRecord {
			name: string
			login: string
		}
		export type GETResponse = SingleRecord[]

	}
}
