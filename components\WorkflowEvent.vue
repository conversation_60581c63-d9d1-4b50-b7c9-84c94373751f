<template>
	<v-timeline-item
		:dot-color="iconColor"
		:icon="icon"
		size="small"
	>
		<template #opposite>
			<workflow-event-label
				:ended="!!item.processedAt"
				:model-value="item.flowableType"
			/>
		</template>
		<div v-if="!item.processedAt">
			{{ $t('WorkflowEvent.awaiting-for') }}
			<span v-if="item.userName">
				{{ $t('WorkflowEvent.user-item-username', [item.userName]) }}
			</span>
			<span v-if="item.roleName">
				{{ $t('WorkflowEvent.a-user-with-role-item-rolename', [item.roleName]) }}
			</span>
			<div class="text-caption text-medium-emphasis">
				For {{ nextEventDuration }}
			</div>
		</div>
		<div v-else>
			<div class="text-caption text-medium-emphasis">
				{{ dateTime(item.processedAt) }}<span
					v-if="prevEvent && eventDuration"
					style="font-size:10px"
				> (Took {{ eventDuration }})</span>
			</div>
			<div class="d-flex justify-space-between ">
				<div class="text-body-2 text-capitalize font-weight-regular me-1 ">
					{{ $t('WorkflowEvent.by-item-username', [item.userName]) }}
				</div>

				<v-chip
					v-if="item.roleName"
					rounded="sm"
					size="x-small"
				>
					{{ item.roleName }}
				</v-chip>
			</div>

			<v-card
				v-if="item.comment"
				variant="plain"
				rounded
				class="text-caption pa-1"
			>
				{{ item.comment }}
			</v-card>
		</div>
	</v-timeline-item>
</template>

<script lang="ts" setup>
import type { ConfigType } from 'dayjs'
import { Transactions } from '~/types/Transactions'

type Props = {
	item: Transactions._Id.Workflow.SingleRecord
	prevEvent?: Transactions._Id.Workflow.SingleRecord
}

const p = withDefaults(defineProps<Props>(), {
	prevEvent: undefined,
})

const dayjs = useDayjs()

const now = useNow('dayjs')

const iconColor = computed(() => {
	if (Transactions._Id.Workflow.Event.Requester === p.item.flowableType) {
		return 'primary'
	}
	switch (p.item.status) {
		case Transactions.Status.Pending:
			return 'warning'
		case Transactions.Status.Approved:
			return 'success'
		case Transactions.Status.Rejected:
			return 'error'
		default:
			return 'grey'
	}
})

const icon = computed(() => {
	if (Transactions._Id.Workflow.Event.Requester === p.item.flowableType) {
		return 'i-mdi:plus'
	} else {
		switch (p.item.status) {
			case Transactions.Status.Pending:
				return 'i-mdi:clock'
			case Transactions.Status.Approved:
				return 'i-mdi:check'
			case Transactions.Status.Rejected:
				return 'i-mdi:close'
			default:
				return 'i-mdi:help'
		}
	}
})

const eventDuration = computed(() => {
	if (!p.prevEvent) {
		return ''
	}

	return durationAsText(p.prevEvent.processedAt, p.item.processedAt)
})

const nextEventDuration = computed(() => {
	return durationAsText(p.prevEvent?.processedAt, now.value)
})

const durationAsText = (startDate: ConfigType, endDate: ConfigType) => {
	const start = dayjs(startDate)
	const end = dayjs(endDate)
	const duration = end.diff(start, 'ms')

	const x = dayjs.duration(duration, 'ms')

	const days = Math.floor(x.asDays())
	const hours = Math.floor(x.asHours() % 24)
	const minutes = Math.floor(x.asMinutes() % 60)

	let text = ''

	if (days > 0) {
		text += `${days}d `
	}

	if (hours > 0 || days > 0) {
		text += `${hours}h `
	}

	if (minutes > 0 || hours > 0 || days > 0) {
		text += `${minutes}m`
	}

	return text.trim()
}
</script>
