import type { Pagination } from '../Helpers'

export namespace Categories {
	export const URL = '/categories'

	export interface Record {
		id: number
		name: string
		displayName1: string
		displayName2: string
		createdAt: string
		symbolsCategory: SymbolsCategory[]
	}

	export interface SymbolsCategory {
		id: number
		name: string
		createdAt: string
	}

	export interface GETResponse extends Pagination<Record> {}

	export namespace _Id {
		export const URL = (_Id: number | string) => `/categories/${_Id}`

		export type GETResponse = Record

		export type PUTRequest = Omit<Record, 'id' | 'createdAt' | 'symbolsCategory'>

		export type POSTRequest = Omit<Record, 'id' | 'createdAt' | 'symbolsCategory'>
	}

	export namespace Lookup {
		export const URL = '/category/lookup'

		export interface SingleRecord {
			id: number
			name: string
			symbol: string
			createdAt: string
			updatedAt: string
		}

		export type GETResponse = SingleRecord[]

	}

}
