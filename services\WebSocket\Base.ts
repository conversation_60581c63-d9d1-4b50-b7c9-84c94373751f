import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'

type ExtractResponseType<T> = T extends Socket<infer R, any> ? R : never

export enum ConnectionStatus {
	Connected = 1,
	Disconnected = 0,
	Connecting = 2,
}

export class WebSocketService<T extends Socket<any, any> > {
	public socket: T | null = null
	public status = ref<ConnectionStatus>(ConnectionStatus.Disconnected)
	protected attempts = ref<number>(0)
	protected hasSocketEmitted = ref<boolean>(false)
	// protected serverName: string = ''
	public data = ref<Parameters<ExtractResponseType<T>['update']>[0]>()

	constructor(private namespace: string) {}

	connect() {
		if (this.socket) {
			return this
		}

		const config = useRuntimeConfig()

		const url = new URL(this.namespace, config.public.socketUrl).href

		const authStore = useAuthStore()

		this.socket = io(url, {
			extraHeaders: {
				token: `${authStore.accessToken}`,
				serverType: '2',
			},
		}) as T

		this.socket.on('connect', () => {
			this.status.value = ConnectionStatus.Connected
		})

		this.socket.on('disconnect', () => {
			this.status.value = ConnectionStatus.Disconnected
			this.resetData()
			// this.serverName = ''
			this.attempts.value = 0
		})

		this.socket.io.on('reconnect_attempt', (attempt) => {
			this.attempts.value = attempt
			this.status.value = ConnectionStatus.Connecting
			this.hasSocketEmitted.value = false
		})

		this.socket.on('connect_error', (error) => {
			console.error('connect_error', error)
		})

		this.socket.on('update', (data: any) => {
			this.data.value = data
			this.hasSocketEmitted.value = true
		})

		return this
	}

	disconnect() {
		if (this.socket) {
			this.socket.disconnect()
			this.socket = null
		}
		return this
	}

	refresh() {
		// const { serverName } = this
		this.disconnect().connect()// .to(serverName)
	}

	resetData() {
		this.data.value = undefined
		this.hasSocketEmitted.value = false
	}

	getData() {
		return this.data
	}
}
