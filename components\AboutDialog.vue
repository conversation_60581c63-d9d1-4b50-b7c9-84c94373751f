<template>
	<v-dialog
		v-model="model"
		max-width="700"
	>
		<v-card>
			<template #title>
				INGOT Brokers Pilot <sup>©</sup>
			</template>
			<template #text>
				<div class="text-body-2">
					<p class="mb-2">
						At INGOT, we are committed to continually improving our internal processes and the manner in which we control our complex trading platforms. Therefore, within the scope of this dedication to excellence, we have come up with INGOT Pilot – a sophisticated web application that serves as a powerful interface for MetaTrader 4 and 5 administrators and managers.
					</p>
					<p class="mb-2">
						INGOT Pilot is more than just an instrument; it is an embodiment of our enduring quest for advancement, efficiency and operational supremacy. The current system contains all modern functional features required by project teams together with comprehensive reporting capabilities on which they rely when performing their daily activities. With such functionalities integrated into it, INGOT Pilot helps us make our workflow efficient while also making it more flexible thus enabling dynamic control over trading operations.
					</p>
					<p class="mb-2">
						The development of INGOT Pilot is a strategic move in the organization that aims at equipping the internal units with effective tools for managing trading environments accurately and confidently. This application has been carefully developed to be in line with our broader goals thereby supporting our internal strategy to trade smoothly and efficiently.
					</p>
					<p class="mb-2">
						Every single detail in INGOT Pilot reflects our commitment towards continuous improvement. We hold strongly onto through empowering or giving power back to our teams- that way we can solve anything.
					</p>
				</div>
				<div class="mt-4 text-subtitle-2">
					<div>UI	Version: v{{ app.version }}</div>
					<div>API Version: v{{ APIVersion }}</div>
				</div>
			</template>
			<template #actions>
				<v-spacer />
				<v-btn @click="model = false">
					Ok
				</v-btn>
			</template>
		</v-card>
	</v-dialog>
</template>

<script lang="ts" setup>
const model = defineModel('modelValue', {
	type: Boolean,
})

const { app } = useAppConfig()

const APIVersion = useState('APIVersion')
</script>
