<template>
	<div
		id="content"
		v-bind="$attrs"
		:class="{
			'show-grid': p.editMode,
			'unmounted': !isMounted,
		}"
		:style="{
			'--col-num': colNum,
			'--row-height': rowHeight+'px',
			'--margin-x': marginX+'px',
			'--margin-y': marginY+'px',
		} "
	>
		<grid-layout
			ref="gridLayoutRef"
			v-model:layout="filteredLayout"
			:col-num="colNum"
			:row-height="rowHeight"
			:is-draggable="true"
			:is-resizable="false"
			:is-mirrored="isRtl"
			:vertical-compact="true"
			:responsive="true"
			:margin="[marginX, marginY]"
			:use-css-transforms="false"
			:is-bounded="false"
			:auto-size="true"
			restore-on-drag
			@layout-ready="$window.setTimeout(() => isMounted = true, 500)"
		>
			<template
				v-for="item in filteredLayout"
				:key="item.i"
			>
				<grid-item
					ref="gridItems"
					:x="item.x"
					:y="item.y"
					:w="item.w"
					:h="item.h"
					:i="item.i"
					:max-w="Widgets[item.c].settings.dimensions.maxW"
					:max-h="Widgets[item.c].settings.dimensions.maxH"
					:min-w="Widgets[item.c].settings.dimensions.minW"
					:min-h="Widgets[item.c].settings.dimensions.minH"
					:is-draggable="p.editMode"
					:is-resizable="p.editMode ? Widgets[item.c].settings.dimensions.resizable : false"
					:preserve-aspect-ratio="Widgets[item.c].settings.dimensions.preserveAspectRatio"
					drag-allow-from=".drag-handle"
				>
					<widget-item-wrapper
						:edit-mode="p.editMode"
						:inserting=" item.i === 'drop'"
						:i="item.i"
						@remove="removeWidgetHandler(item)"
					>
						<component
							:is="Widgets[item.c].Component"
							:data="item.data"
							:name="item.c"
							:i="item.i"
							:is-preview="item.isPreview"
							:is-layout-mounted="isMounted"
							@update:data="dataUpdateHandler($event, item)"
						/>
					</widget-item-wrapper>
				</grid-item>
			</template>
			<slot
				v-if="!filteredLayout.length && !p.editMode"
				name="empty"
			/>
		</grid-layout>
	</div>
	<!-- {{ layout }} -->
	<v-bottom-sheet

		v-model="panelModel"
	>
		<v-sheet
			ref="scrollContainer"
			color="dark"
			@vue:mounted="activateWheeListener"
			@vue:before-unmount="deactivateWheelListener"
		>
			<v-container
				fluid
			>
				<v-row
					class="flex-nowrap"
					align="center"
				>
					<v-col
						v-for="widget, name in allowedWidgets"
						:key="`panel-component-${name}`"
						cols="auto"
					>
						<v-responsive
							max-height="200"
							:max-width="widget.settings.dimensions.w * 87 "
							class="my-auto"
						>
							<v-card
								width="100%"
								height="100%"
								class="dragging-wrapper"
								draggable="true"
								unselectable="on"
								@dragstart="dragStart"
								@drag="drag($event, name)"
								@dragend="dragend"
							>
								<component
									:is="widget.Component"
									i="drop"
									:name="name"
									:is-preview="true"
									:is-layout-mounted="isMounted"
									@click.prevent
								/>
							</v-card>
						</v-responsive>
						<div class="text-center text-subtitle-1">
							{{ name }}
						</div>
					</v-col>
					<v-col v-if="!Object.keys(allowedWidgets).length">
						<v-empty-state>{{ $t('Widgets.there-are-no-widgets-available') }}</v-empty-state>
					</v-col>
				</v-row>
			</v-container>
		</v-sheet>
	</v-bottom-sheet>
</template>

<script lang="ts" setup>
import { GridLayout, GridItem } from 'vue3-grid-layout-next'
import type { VSheet } from 'vuetify/components'
import throttle from 'lodash/throttle'
import Widgets from './item'
import type { LayoutItem } from './types'
import type { Names } from '~/types/Permissions/Names'

type Props = {
	editMode: boolean
}

const p = defineProps<Props>()

const isMounted = ref(false)

const { isRtl } = useRtl()

const layout = defineModel<LayoutItem[]>({
	default: () => [],
	type: Array,
})

const filteredLayout = computed({
	get() {
		return layout.value.filter((item) => {
			return !!Widgets[item.c] && (Widgets[item.c].settings.permission ? can(Widgets[item.c].settings.permission as Names) : true)
		})
	},
	set(/* val */) {
		// console.log('🚀 ~ set ~ val:', val)
		// layout.value = val
	},
})

const colNum = 12

const rowHeight = 70

const marginX = 16

const marginY = 16

const gridLayoutRef = ref<InstanceType<typeof GridLayout> | null>(null)

const gridItems = ref<InstanceType<typeof GridItem>[]>([])

const mouseXY = { x: 0, y: 0 }
const DragPos = { x: 0, y: 0, w: 1, h: 1, i: 'null' }

const panelModel = ref(false)

const allowedWidgets = computed(() => {
	// return only the widgets that the user has permission to see
	return Object.fromEntries(Object.entries(Widgets).filter(([_name, widget]) => {
		return widget.settings.permission ? can(widget.settings.permission) : true
	})) as typeof Widgets
})

const openPanel = () => {
	panelModel.value = true
}

const closePanel = () => {
	panelModel.value = false
}

const dragStart = function (_e: any) {
	if (!_e.dataTransfer) {
		alert('Data transfer not found')
		return
	}
	const e = _e as DragEvent
	const dataTransfer = e.dataTransfer as DataTransfer
	dataTransfer.setDragImage(_e.target, window.outerWidth, window.outerHeight)
	dataTransfer.effectAllowed = 'copyMove'
	dataTransfer.dropEffect = 'copy'
	closePanel()
}

const drag = (_e: any, widgetName: keyof typeof Widgets) => {
	const parentRect = document.getElementById('content')?.getBoundingClientRect()

	if (!parentRect) {
		return
	}
	let mouseInGrid = false
	if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
		mouseInGrid = true
	}
	const widgetDimensions = Widgets[widgetName].settings.dimensions
	const data = Widgets[widgetName].settings.data || undefined
	if (mouseInGrid === true && !layout.value.some(item => item.i === 'drop')) {
		layout.value.push({
			x: (layout.value.length * 2) % (colNum),
			y: layout.value.length + (colNum), // puts it at the bottom
			w: widgetDimensions.w,
			h: widgetDimensions.h,
			i: 'drop',
			c: widgetName,
			isPreview: true,
			data,
		})
	}
	const index = layout.value.findIndex(item => item.i === 'drop')
	if (index !== -1) {
		nextTick(() => {
			const el = gridItems.value[index]
			if (!el) {
				alert('Element not found')
				return
			}
			el.dragging = { top: mouseXY.y - parentRect.top, left: mouseXY.x - parentRect.left, width: 0, height: 0 }
			const newPos = el.calcXY(mouseXY.y - parentRect.top, mouseXY.x - parentRect.left)

			if (mouseInGrid === true) {
				gridLayoutRef.value?.dragEvent('dragstart', 'drop', newPos.x, newPos.y, widgetDimensions.h, widgetDimensions.w)
				DragPos.i = String(index)
				DragPos.x = layout.value[index].x
				DragPos.y = layout.value[index].y
			}
			if (mouseInGrid === false) {
				gridLayoutRef.value?.dragEvent('dragend', 'drop', newPos.x, newPos.y, widgetDimensions.h, widgetDimensions.w)
				layout.value = layout.value.filter(obj => obj.i !== 'drop')
			}
		})
	}
}

const dragend = function (e: any) {
	e.dataTransfer.clearData()

	const parentRect = document.getElementById('content')?.getBoundingClientRect()

	if (!parentRect) {
		alert('Parent rect not found')
		return
	}
	let mouseInGrid = false
	if (((mouseXY.x > parentRect.left) && (mouseXY.x < parentRect.right)) && ((mouseXY.y > parentRect.top) && (mouseXY.y < parentRect.bottom))) {
		mouseInGrid = true
	}
	if (mouseInGrid === true) {
		// alert(`Dropped element props:\n${JSON.stringify(DragPos, ['x', 'y', 'w', 'h'], 2)}`)
		gridLayoutRef.value!.dragEvent('dragend', 'drop', DragPos.x, DragPos.y, 1, 1) // removes the placeholder effects

		const droppedWidget = layout.value.find(item => item.i === 'drop')

		if (droppedWidget) {
			droppedWidget.i = getId()
			droppedWidget.isPreview = false
		}
	}
}

const getId = function () {
	return Math.random().toString(36).substr(2, 9)
}

const dragoverListener = throttle((e: DragEvent) => {
	mouseXY.x = e.clientX
	mouseXY.y = e.clientY
}, 200) // 25fps

const removeWidgetHandler = (widget: LayoutItem) => {
	layout.value = layout.value.filter(item => item.i !== widget.i)
}

const emit = defineEmits(['save'])

const dataUpdateHandler = (data: any, item: LayoutItem) => {
	// using splice to avoid reactivity issues
	const index = layout.value.findIndex(i => i.i === item.i)
	if (index !== -1) {
		layout.value.splice(index, 1, { ...item, data })
		emit('save', layout.value)
	}
}

defineExpose({
	openPanel,
	closePanel,
	getId,
	layout,
})

const scrollContainer = ref<InstanceType<typeof VSheet>>()

const handleWheel = (event: any) => {
	event.preventDefault() // Prevents vertical scrolling
	if (scrollContainer.value) {
		scrollContainer.value.$el.scrollLeft += event.deltaY // Scroll horizontally
	}
}

const activateWheeListener = () => {
	if (scrollContainer.value) {
		scrollContainer.value.$el.addEventListener('wheel', handleWheel)
	}
}

const deactivateWheelListener = () => {
	if (scrollContainer.value) {
		scrollContainer.value.$el.removeEventListener('wheel', handleWheel)
	}
}
onMounted(() => {
	document.addEventListener('dragover', dragoverListener, false)
})

onUnmounted(() => {
	document.removeEventListener('dragover', dragoverListener, false)
})
</script>

<style lang="scss">
.vue-grid-item {
	touch-action: none;
	transition: unset;
}

.vue-grid-layout {
	min-height: calc(100dvh - var(--v-layout-top) - 80px - var(--2fa-banner-height) - 98px);
	.vue-grid-item.unmounted {
		transition: none;
	}
	.vue-grid-item {
		@at-root .unmounted & {
			transition: none;
		}

		&.vue-grid-placeholder {
			background: transparent;//rgb(var(--v-theme-background));
			border: 1px dashed rgb(var(--v-theme-on-background));
			border-radius: 8px;
			opacity: 1;
			transition-duration: .1s;
			z-index: 2;
			-webkit-user-select: none;
			-moz-user-select: none;
			-o-user-select: none;
			user-select: none;
		}

		&:not(.vue-grid-placeholder) {
			z-index: 3;
		}
	}
}

.dragging-wrapper{
	position: relative;
	cursor: grab;
	&:after{
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}
}

.show-grid {
	position: relative;
	--line-thickness: 12px;
	&::before {
		--background-size:calc((calc(100% - calc(var(--margin-x) / 2)) / var(--col-num))) calc(var(--row-height) + var(--margin-y));
		// background size = calc(calc(100% - (margin/2)) / colNum) rowHeight + margin;
		// --background-size: calc((100% - var(--margin-x)) / var(--col-num)) calc(var(--row-height) + var(--margin-y));
		content: '';
		background-size: var(--background-size);
		background-image:
            linear-gradient(to right,
                rgba(var(--v-theme-on-background), 1) var(--line-thickness),
                transparent 0),
            linear-gradient(to bottom,
                rgba(var(--v-theme-on-background), 1) var(--line-thickness),
                transparent var(--line-thickness));
		// height: calc(100% - 5px);
		// width: calc(100% - 0px);
		position: absolute;
		left:0;
		top:0;
		right:0;
		bottom:0;
		background-repeat: repeat;
		margin: 5px;
		opacity: 0.02;
	}
}
</style>
