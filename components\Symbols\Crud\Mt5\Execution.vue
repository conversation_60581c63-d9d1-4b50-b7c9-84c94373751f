<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="9"
			>
				<fieldset>
					<legend>{{ $t('Symbols.MT5.Crud.Execution.settings') }}</legend>
					<v-select
						v-model="item.execMode"
						:error-messages="errors.execMode"
						:items="execModeItems"
						:label="$t('Symbols.MT5.Crud.Execution.execution')"
					/>
					<v-text-field
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_INSTANT].includes(item.execMode)"
						v-model.number="item.ieTimeout"
						:label="$t('Symbols.MT5.Crud.Execution.max-time-deviation')"
						suffix="seconds"
					/>
					<v-text-field
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_INSTANT].includes(item.execMode)"
						v-model.number="item.ieSlipProfit"
						:label="$t('Symbols.MT5.Crud.Execution.max-profit-deviation')"
						suffix="points"
					/>
					<v-text-field
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_INSTANT].includes(item.execMode)"
						v-model.number="item.ieSlipLosing"
						:label="$t('Symbols.MT5.Crud.Execution.max-losing-deviation')"
						suffix="points"
					/>
					<v-switch
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_INSTANT].includes(item.execMode)"
						v-model="item.ieFlags"
						:true-value="Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_FAST_CONFIRMATION"
						:false-value="Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_NONE"
						color="success"
						:label="$t('Symbols.MT5.Crud.Execution.fast-confirmation')"
					/>
					<symbols-crud-fraction-ext
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_INSTANT].includes(item.execMode)"
						v-model="item.ieVolumeMax"
						v-model:ext="item.ieVolumeMaxExt"
						:label="$t('Symbols.MT5.Crud.Execution.max-volume')"
					/>
					<v-text-field
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_REQUEST].includes(item.execMode)"
						v-model.number="item.reTimeout"
						:label="$t('Symbols.MT5.Crud.Execution.timeout')"
						suffix="seconds"
					/>
					<v-switch
						v-if="[Symbols.MT5.ExecutionMode.EXECUTION_REQUEST].includes(item.execMode)"
						v-model="item.reFlags"
						color="success"
						:true-value="Symbols.MT5.RequestFlags.REQUEST_FLAGS_ORDER"
						:false-value="Symbols.MT5.RequestFlags.REQUEST_FLAGS_NONE"
						:label="$t('Symbols.MT5.Crud.Execution.confirm-orders')"
					/>
				</fieldset>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
import { Symbols } from '~/types'

withDefaults(defineProps<Props>(), defaults)

const execModeItems = enumToItems(Symbols.MT5.ExecutionMode, 'Symbols.MT5.ExecutionMode')
</script>
