<template>
	<v-sheet class="h-screen">
		<v-layout
			id="main-layout"
			full-height
		>
			<v-app-bar
				ref="appBarRef"
				class="pe-2"
				order="1"
				scroll-behavior="elevate"
				height="70"
			>
				<v-btn
					icon
					rounded="circle"
					class="hidden-md-and-up"
					@click="toggleDrawer"
				>
					<v-icon>i-mdi:menu</v-icon>
				</v-btn>
				<v-sheet
					width="250"
					height="calc(68px - 2 * 3 * 2px)"
					class="pa-3"
					color="transparent"
				>
					<nuxt-link
						:to="{ name: 'index' }"
					>
						<logo max-height="100%" />
					</nuxt-link>
				</v-sheet>
				<select-current-trading-server class="hidden-sm-and-down" />

				<v-spacer />
				<notification-btn />
				<v-divider
					vertical
					inset
					class="my-4 me-4"
				/>
				<theme-switch class="me-4 hidden-sm-and-down" />
				<user-btn />
			</v-app-bar>
			<v-navigation-drawer
				v-model:model-value="drawerModel"
				class="main-menu"
				order="1"
				mobile-breakpoint="sm"
				width="250"
				:floating="!(isRail && isDrawerHovered)"
				expand-on-hover
				:rail="isRail"
				@update:rail="drawerExpansionHandler"
			>
				<!-- <div class="d-flex h-100 position-relative"> -->
				<v-list
					v-model:opened="openedListModel"
					nav
					slim
					density="comfortable"
					color="primary"
					class="flex-grow-1 overflow-y-auto"
				>
					<template
						v-for="item in computedMenuItems"
						:key="`item-${item.title}`"
					>
						<v-list-subheader v-if="item.type === 'header'">
							<template v-if="(!isRail || isDrawerHovered)">
								{{ item.title }}
							</template>
							<v-divider
								v-else
								style="width:100px"
							/>
						</v-list-subheader>

						<v-list-item
							v-else-if="item.type === 'item'"
							v-bind="item"
						/>

						<v-list-group
							v-else-if="item.type === 'group'"
							:key="`group-${item.title}`"
						>
							<template #activator="{ props }">
								<v-list-item
									v-bind="{
										...props,
										baseColor: item.isGroupActive ? 'accent' : undefined,
									}"
									:prepend-icon="item.prependIcon"
									:title="item.title"
								/>
							</template>
							<v-list-item
								v-for="child in item.children"
								v-bind="child"
								:key="`child-${child.title}`"
							/>
						</v-list-group>
					</template>
				</v-list>
				<v-btn
					class="resize-btn"
					border
					variant="elevated"
					rounded="circle"
					color="surface"
					size="x-small"
					width="24"
					height="24"
					position="absolute"
					location="right top"
					:icon="railIcon"
					style="transform: translate3d(50%, 50%, 0)"
					@click="toggleRail"
				/>
				<!-- <div class="resize" />
				</div> -->
			</v-navigation-drawer>

			<v-main
				id="scroll-target"
				ref="mainRef"
				scrollable
				@vue:mounted="activate"
				@vue:before-unmount="deactivate"
			>
				<v-divider
					v-show="isScrolled"
					style="position:sticky; top: 0; z-index: 3;"
				/>

				<v-sheet
					border
					color="background"
					min-height="100%"
					rounded="12px"
					:style="{
						'border-top-left-radius': $i18n.localeProperties.dir !== 'rtl' && $vuetify.display.mdAndUp.value ? '12px' : 0,
						'border-top-right-radius': $i18n.localeProperties.dir === 'rtl' && $vuetify.display.mdAndUp.value ? '12px' : 0,
					}"
				>
					<v-fade-transition
						mode="out-in"
						leave-absolute
						class="h-100"
					>
						<v-container
							:key="`server-id-${preferencesStore.keys.tradingServerName}`"
							fluid
							class="h-100"
							:style="{ '--2fa-banner-height': `${authStore.user?.twoFactorEnabled ? 0 : ((twoFactorBannerRef?.$el.clientHeight ?? 0) + 16 + 2 /* margin bottom + border */)}px` }"
						>
							<two-factor-warning-banner
								ref="twoFactorBannerRef"
								:is-scrolled="isScrolled"
							/>

							<NuxtPage />
						</v-container>
					</v-fade-transition>
				</v-sheet>
			</v-main>
		</v-layout>
	</v-sheet>
</template>

<script lang="ts" setup>
import type { RouteLocationRaw } from 'vue-router'
import type { VBanner } from 'vuetify/components'
import { VList, VMain, VNavigationDrawer } from 'vuetify/components'
import ThemeSwitch from '~/components/ThemeSwitch.vue'
// import { useElementSize, useDraggable } from '@vueuse/core'
import type { Permissions } from '~/types/Permissions/index'

const prefStore = usePreferencesStore()

const isScrolled = ref(false)

const scrollElement = ref<HTMLElement | null>(null)

const isRail = ref(prefStore.getKey('rail'))

const railIcon = computed(() => (isRail.value ? 'i-mdi:chevron-right' : 'i-mdi:chevron-left'))

const isDrawerHovered = ref(false)

const openedListModel = ref<any[]>([])

const twoFactorBannerRef = ref<InstanceType<typeof VBanner> | null>(null)

const route = useRoute()

const authStore = useAuthStore()

// const fcmStore = useFcmStore()

onMounted(() => {
	// only if localhost or https
	if (location.hostname === 'localhost' || location.protocol === 'https:') {
		// fcmStore.init()
	} else {
		console.warn('FCM is not supported on this domain')
	}
})

const { t } = useI18n()

watch(() => isRail.value, (value) => {
	prefStore.updateKey('rail', value)
})

const toggleRail = () => {
	isRail.value = !isRail.value
	if (isRail.value) {
		collapseMenu()
	}
}

const drawerExpansionHandler = (value: boolean) => {
	isDrawerHovered.value = !value
	if (!isDrawerHovered.value && isRail.value) {
		collapseMenu()
	}
}

const collapseMenu = () => {
	openedListModel.value = []
}

// const drawerRef = ref<HTMLElement | null>(null)
// useDraggable(drawerRef, { axis: 'x' })
// const { width } = useElementSize(drawerRef)

const activate = () => {
	scrollElement.value = document.querySelector('.v-main > div')
	if (!scrollElement.value) {
		console.warn('Element not found')
		return
	}
	scrollElement.value.addEventListener('scroll', scrollHandler)
}

const deactivate = () => {
	if (scrollElement.value) {
		scrollElement.value.removeEventListener('scroll', scrollHandler)
	}
}

const scrollHandler = () => {
	if (scrollElement.value) {
		isScrolled.value = scrollElement.value.scrollTop > 0
	}
}

const preferencesStore = usePreferencesStore()

enum MenuType {
	Header = 'header',
	Item = 'item',
	Group = 'group',
}

type ListItem = {
	type: MenuType.Item
	title: string
	prependIcon: string
	to: string | RouteLocationRaw | undefined
}

// type ChildItem = {
// 	title: string
// 	to: string | RouteLocationNormalizedLoaded | undefined
// 	prependIcon: string
// }

type HeaderItem = {
	type: MenuType.Header
	title: string
	canAny?: Permissions.Names[]
	canAll?: Permissions.Names[]
}

type GroupItem = {
	type: MenuType.Group
	title: string
	prependIcon: string
	children: ListItem[]
	isGroupActive?: boolean
}

const menuItems = computed<Array<ListItem | HeaderItem | GroupItem>>(() => [
	{ type: MenuType.Header, title: t('default.main') },

	{
		type: MenuType.Item,
		title: t('default.dashboard'),
		prependIcon: 'i-material-symbols-light:space-dashboard-outline',
		to: ({
			name: 'index',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.transactions'),
		prependIcon: 'i-material-symbols-light:list-alt-check-outline',
		to: ({
			name: 'transactions',
		}),
	},

	{
		type: MenuType.Group,
		title: t('default.reports'),
		prependIcon: 'i-material-symbols-light:bar-chart-4-bars',
		children: [
			{
				type: MenuType.Item,
				title: t('default.prices'),
				to: ({
					name: 'reports-prices',
				}),
				prependIcon: 'i-material-symbols:price-change-outline',
			},
			{
				type: MenuType.Item,
				title: t('default.deals'),
				to: ({
					name: 'reports-deals',
				}),
				prependIcon: 'i-mdi:deal-outline',
			},
			{
				type: MenuType.Item,
				title: t('default.online-traders'),
				to: ({
					name: 'reports-online-traders',
				}),
				prependIcon: 'i-mdi:account-online-outline',
			},
			{
				type: MenuType.Item,
				title: t('default.1-min-history'),
				to: ({
					name: 'reports-1-min-history',
				}),
				prependIcon: 'i-material-symbols:history',
			},
			{
				type: MenuType.Item,
				title: t('default.bid-ask-last-ticks'),
				to: ({
					name: 'reports-bid-ask-last-ticks',
				}),
				prependIcon: 'i-material-symbols:history-toggle-off-rounded',
			},
			{
				type: MenuType.Item,
				title: 'Exposure Report',
				to: ({
					name: 'reports-exposure',
				}),
				prependIcon: 'i-material-symbols:history-toggle-off-rounded',
			},
		],
	},
	{
		type: MenuType.Item,
		title: t('default.journals'),
		prependIcon: 'i-material-symbols-light:history',
		to: ({
			name: 'journals',
		}),
	},
	{
		type: MenuType.Item,
		title: 'Server Logs',
		prependIcon: 'i-material-symbols-light:history',
		to: ({
			name: 'server-logs',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.symbols'),
		prependIcon: 'i-ph:currency-circle-dollar',
		to: ({
			name: 'symbols',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.groups'),
		prependIcon: 'i-material-symbols-light:deployed-code-account-outline',
		to: ({
			name: 'groups',
		}),
	},
	{
		type: MenuType.Group,
		title: t('default.accounts'),
		prependIcon: 'i-material-symbols-light:group-outline',
		children: [
			{
				type: MenuType.Item,
				title: t('default.trading-accounts'),
				to: ({
					name: 'accounts-trading',
				}),
				prependIcon: 'i-mdi:chart-finance',
			},
			{
				type: MenuType.Item,
				title: t('default.manager-accounts'),
				to: ({
					name: 'accounts-manager',
				}),
				prependIcon: 'i-mdi:shield-account-outline',
			},
			{
				type: MenuType.Item,
				title: 'Hedging',
				to: ({
					name: 'accounts-hedging',
				}),
				prependIcon: 'i-mdi:shield-account-outline',
			},
		],
	},
	{ type: MenuType.Header, title: t('default.content-management'), canAny: ['actionTypes.view', 'actions.view', 'operations.view', 'tradingServers.view', 'currencies.view'] },
	{
		type: MenuType.Item,
		title: 'Symbols Categories',
		prependIcon: 'i-material-symbols-light:currency-exchange',
		to: ({
			name: 'symbols-categories',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.currencies'),
		prependIcon: 'i-material-symbols-light:currency-exchange',
		to: ({
			name: 'currencies',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.trading-servers'),
		prependIcon: 'i-arcticons:metatrader-5',
		to: ({
			name: 'trading-servers',
		}),
	},
	{
		type: MenuType.Item,
		title: t('default.transactions-workflow'),
		prependIcon: 'i-material-symbols-light:account-tree-outline',
		to: ({
			name: 'transactions-workflow-actions',
		}),
		// children: [
		// 	{
		// 		type: MenuType.Item,
		// 		title: 'Operations',
		// 		prependIcon: 'i-material-symbols-light:action-key',
		// 		to: ({
		// 			name: 'transactions-workflow-operations',
		// 		}),
		// 	},
		// 	{
		// 		type: MenuType.Item,
		// 		title: 'Action Types',
		// 		prependIcon: 'i-material-symbols-light:dynamic-form-outline-rounded',
		// 		to: ({
		// 			name: 'transactions-workflow-action-types',
		// 		}),
		// 	},
		// 	{
		// 		type: MenuType.Item,
		// 		title: 'Actions',
		// 		prependIcon: 'i-material-symbols-light:bolt-outline',
		// 		to: ({
		// 			name: 'transactions-workflow-actions',
		// 		}),
		// 	},
		// ],
	},
	{
		type: MenuType.Item,
		title: t('default.users'),
		prependIcon: 'i-material-symbols-light:person-outline',
		to: {
			name: 'users',
		},
	},

])

const computedMenuItems = computed(() =>
	menuItems.value
		.map((item) => {
			if (item.type === MenuType.Group) {
				item.children = item.children!.filter(child => child.to && canByRoute(child.to))
				item.isGroupActive = item.children.some(child => child.to === route.path)
			}
			return item
		})
		.filter((item) => {
			switch (item.type) {
				case MenuType.Header: {
					// if (isRail.value && !isDrawerHovered.value) { return false }
					if (item.canAny) {
						return canAny(item.canAny)
					}

					if (item.canAll) {
						return canAll(item.canAll)
					}

					return true
				}
				case MenuType.Item:
					return item.to ? canByRoute(item.to) : undefined

				case MenuType.Group:
					return item.children.length > 0

				default:
					return true
			}
		}),
)

const { $vuetify } = useNuxtApp()

const drawerModel = ref($vuetify.display.mdAndUp.value)

const toggleDrawer = () => {
	drawerModel.value = !drawerModel.value
}

const { on, off } = useGlobalEvents()

const router = useRouter()

on('auth:invalidated', async () => {
	await router.push(({
		name: 'auth-login',
	}))
})

onUnmounted(() => {
	off('auth:invalidated')
})
</script>

<style lang="scss">
.main-menu {
	user-select: none;
	-webkit-user-drag: none;

	.resize-btn {
		visibility: hidden;
		opacity: 0;
		transition: all 0.250s;
	}

	&:hover {
		.resize-btn {
			visibility: visible;
			opacity: 1;
			transition: all 0.250s;
		}
	}

	.v-list-item--slim .v-list-item__append>.v-badge~.v-list-item__spacer,
	.v-list-item--slim .v-list-item__append>.v-icon~.v-list-item__spacer,
	.v-list-item--slim .v-list-item__append>.v-tooltip~.v-list-item__spacer {
		width: 8px;
	}
}

/* .resize {
	right:-8px;
   height: calc(100% - 12px);
   width: 4px;
   cursor: col-resize;
   flex-shrink: 0;
   position: absolute;
	top:0;
	bottom:0;
   z-index: 10;
   user-select: none;
	margin-top: 12px;

}
.resize:hover{
	background: #444857;
}
.resize::before {
   content: "";
   position: absolute;
   top: 50%;
   left: 50%;
   transform: translate(-50%, -50%);
   width: 3px;
   height: 15px;
   border-inline: 1px solid #fff;
} */
</style>
