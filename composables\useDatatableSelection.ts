import { defu } from 'defu'
import type Datatable from '~/components/Datatable.vue'

type Options = {
	/**
	 * Whether to show the select column. Auto will show the column if there are selected items.
	 * @default 'auto'
	 **/
	showSelect?: boolean | 'auto'
}

const defaultOptions: Options = {
	showSelect: 'auto',
}

export const useDatatableSelection = <T>(providedOptions: Options = {}) => {
	const options = defu	(providedOptions, defaultOptions)

	const model = ref([]) as Ref<T[]> // https://stackoverflow.com/a/69901745

	const showSelect = computed(() => {
		if (options.showSelect === 'auto') {
			return model.value.length > 0
		}
		return options.showSelect
	})

	const containsBadTag = (element: HTMLElement | null): boolean => {
		while (element && !element.classList.contains('v-data-table')) {
			if (element.tagName === 'BUTTON') {
				return true
			}
			element = element.parentElement
		}
		return false
	}

	const toggleSelection = (item: T, e: MouseEvent) => {
		const target = e.target as HTMLElement

		// Helper function to check for button tag existence in ancestors

		if (containsBadTag(target)) return

		const index = model.value.findIndex(i => isEqual(i, item))

		if (index === -1) {
			model.value.push(item)
		} else {
			model.value.splice(index, 1)
		}
	}

	const props = computed<Partial<InstanceType<typeof Datatable>['$props']>> (() => ({
		'onUpdate:modelValue': (value: unknown) => {
			if (Array.isArray(value)) {
				model.value = value
			} else {
				console.error('Unexpected type for modelValue:', typeof value)
			}
		},
		'modelValue': model.value,
		'showSelect': showSelect.value,
		'rowProps': ({ item }) => ({
			onClick: (event: MouseEvent) => {
				toggleSelection(item, event)
			},
		}),
		'returnObject': true,
		'hover': true,
	}))

	return {
		model,
		props,
	}
}
