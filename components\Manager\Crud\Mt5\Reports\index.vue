<template>
	<v-container>
		<v-toolbar
			density="compact"
			color="surface"
		>
			<v-spacer />
			<v-btn
				variant="text"
				prepend-icon="i-mdi:plus"
				@click="addReport"
			>
				{{ $t('Manager.MT5.Crud.Reports.add-report') }}
			</v-btn>
		</v-toolbar>
		<v-table
			class="draggable-rows"
			hover
			density="compact"
		>
			<thead>
				<tr>
					<th class="px-2">
						{{ $t('Manager.MT5.Crud.Reports.name') }}
					</th>
					<th class="px-2">
						{{ $t('Manager.MT5.Crud.Reports.view') }}
					</th>
					<th class="px-2">
						{{ $t('Manager.MT5.Crud.Reports.export') }}
					</th>
					<th class="px-2">
						{{ $t('Manager.MT5.Crud.Reports.history') }}
					</th>
					<th class="px-2" />
				</tr>
			</thead>
			<tbody
				ref="tbodyRef"
				v-auto-animate
			>
				<api-items
					v-slot="{ items: reportsLookupItems }"
					:url="Reports.MT5.Lookup.URL"
					:map-items="reportsMapperHandler"
				>
					<manager-crud-mt5-reports-row
						v-for="(report, index) in item.reports"
						:key="index"
						v-model="item.reports[index]"
						:index="index"
						:reports="reportsLookupItems"
						@remove="remove"
					/>
				</api-items>
				<tr v-if="!item.reports?.length">
					<td
						colspan="6"
						class="text-center"
					>
						{{ $t('$vuetify.noDataText') }}
					</td>
				</tr>
			</tbody>
		</v-table>
	</v-container>
</template>

<script lang="ts" setup>
import { useSortable } from '@vueuse/integrations/useSortable'
import type { UseSortableOptions } from '@vueuse/integrations/useSortable'
import { defaults } from '../Shared'
import type { Props, Emit } from '../Shared'
import { Manager, Reports } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const defaultItem: Manager.MT5.Report = {
	name: '*',
	limitDays: 0,
	permissions: Manager.MT5.EnPermissionsFlags.PERMISSION_ALL,
}

const defaultSortableOptions: UseSortableOptions = {
	handle: '.handle',
	animation: false,
	swapThreshold: 0.1,
	ghostClass: 'ghost',
}
const tbodyRef = ref<HTMLElement>()

useSortable(tbodyRef, computed(() => p.item.reports), defaultSortableOptions)

const confirm = useNuxtApp().$confirm

const { t } = useI18n()

const addReport = () => {
	emit('update:item', {
		reports: [
			...p.item.reports,
			{ ...defaultItem },
		],
	})
}

const remove = (index: number) => {
	confirm(t('Manager.MT5.Crud.Reports.are-you-sure-you-want-to-remove')).then(() => {
		const reports = useCloned(p.item.reports).cloned.value
		reports.splice(index, 1)
		emit('update:item', {
			reports,
		})
	}).catch(() => {})
}

const reportsMapperHelper = (item: Reports.MT5.Lookup.SingleRecord) => {
	const children: any = item.children?.map(reportsMapperHelper)

	return {
		value: item.type === 'folder' ? item.name + '\\*' : item.name,
		title: item.name,
		children: children?.length ? children : undefined,
		props: {
			class: {
				'ms-7': !children?.length,
			},
		},
	}
}

const reportsMapperHandler = (items: Reports.MT5.Lookup.GETResponse) => {
	const mappedItems = items.map(reportsMapperHelper)

	mappedItems.unshift(
		{
			value: '*',
			title: 'All',
			props: {
				class: {
					'ms-7': true,
				},
			},
		} as ReturnType<typeof reportsMapperHelper>,
	)

	return mappedItems
}
</script>
