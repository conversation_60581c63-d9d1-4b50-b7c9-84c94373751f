<template>
	<page title="Exposure Report">
		<!-- <template #actions>
			<v-btn
				prepend-icon="i-mdi:export-variant"
				color="primary"
				:loading="isExporting"
				:disabled="!hasData"
				@click="exportData"
			>
				{{ $t('bid-ask-last-ticks.export-data') }}
			</v-btn>
		</template> -->
		<datatable
			ref="table"
			url="/exposure-report"
			:headers="headers"
			:default-model="{ period: 0, fromDate: null, toDate: null, symbol: null }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
			persistent-filter
			:api-options="{
				transform: exposureReportTransformer,
			}"
		>
			<!-- <template #filter="{ model }">
				<symbols-lookup
					v-model="model.symbol"
					component="combobox"
					return="symbol"
					:label="$t('bid-ask-last-ticks.symbol')"
					:rules="rules({ required: true })"
				/>

				<date-time-input
					v-model="model.fromDate"
					:rules="rules({ required: true })"
					:max="model.toDate || new Date()"
					:label="$t('bid-ask-last-ticks.from')"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('bid-ask-last-ticks.use-mt-timezone')"
				/>
				<date-time-input
					v-model="model.toDate"
					:rules="rules({ required: true })"
					:label="$t('bid-ask-last-ticks.to')"
					:min="model.fromDate"
					:max="new Date()"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('bid-ask-last-ticks.use-mt-timezone')"
				/>
			</template> -->
		</datatable>
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type { ExposureReport } from '~/types/ExposureReport'

definePageMeta({
	permission: 'ticksHistoryPrices.view',
})

const { t } = useI18n()

const { $api } = useNuxtApp()

const { successSnackbar, errorSnackbar } = useSnackbar()

const isExporting = ref(false)

const hasData = computed(() => table.value?.items.length > 0)

const table = ref<typeof Datatable | null>(null)

const headers: Headers = [
	{
		title: t('bid-ask-last-ticks.ask-price'),
		value: 'askPrice',
	},
	{
		title: t('bid-ask-last-ticks.bid-price'),
		value: 'bidPrice',
	},
	{
		title: t('bid-ask-last-ticks.time'),
		value: 'time',
	},

]

const exposureReportTransformer = (data: ExposureReport.GETResponse) => {
	// flatten the object into array and keep the object key as category key
	const result = (Object.keys(data) as Array<keyof ExposureReport.GETResponse>).map((key) => {
		const { category, ...rest } = data[key]
		return {
			category: key,
			...rest,
		}
	})
	console.log('🚀 ~ result ~ result:', result)

	return result
}
</script>
