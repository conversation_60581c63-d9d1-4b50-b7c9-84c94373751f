<template>
	<Title>{{ $t('reset.reset-password') }}</Title>
	<v-form
		ref="formRef"
		@submit.prevent="submit"
	>
		<div class="text-center text-h6 pb-4">
			{{ $t('reset.reset-password') }}
		</div>
		<p class="mb-4">
			{{ $t('reset.please-enter-your-new-password', [form.email]) }}
		</p>
		<password
			v-model="form.password"
			:error-messages="errors.password"
			:rules="rules({ required: true })"
			:label="$t('reset.password')"
			:placeholder="$t('reset.enter-your-password')"
			tabindex="1"
		/>
		<password
			v-model="form.confirmPassword"
			:error-messages="errors.confirmPassword"
			:rules="rules({ required: true, type: 'enum', enum: [form.password], message: 'Password does not match' })"
			class="mb-2"
			:placeholder="$t('reset.confirm-your-password')"
			:label="$t('reset.confirm-password')"
			hide-details="auto"
			tabindex="2"
		/>

		<div class="d-flex justify-end">
			<v-btn
				:to="{ path: '/auth/login' }"
				variant="text"
				size="small"
				class="text-capitalize"
			>
				{{ $t('reset.remembered-your-password') }}
			</v-btn>
		</div>

		<v-btn
			class="mt-6 text-capitalize text-none"
			color="primary"
			size="large"
			type="submit"
			variant="elevated"
			block
			:disabled="loading"
			:loading="loading"
			@click="submit()"
		>
			{{ $t('reset.reset') }}
		</v-btn>
	</v-form>
</template>

<script lang="ts" setup>
import { VForm } from 'vuetify/components'
import { Auth } from '~/types'

definePageMeta({
	async validate(route) {
		const authStore = useAuthStore()
		await authStore.verifyLoginMode()
		const email = route.query.email as string
		const token = route.query.token as string
		return authStore.LoginMode === Auth.LoginMode.Modes.Database && !!email && !!token
	},
})

const formRef = ref<VForm | null>(null)

const { successSnackbar } = useSnackbar()

const loading = ref(false)

const router = useRouter()

const authStore = useAuthStore()

const { t } = useI18n()

const route = useRoute()

const queryToken = computed(() => route.query.token as string || '')

const queryEmail = computed(() => route.query.email as string || '')

const form = reactive<Auth.ResetPassword.POSTRequest>({
	password: '',
	confirmPassword: '',
	email: queryEmail.value,
	resetToken: queryToken.value,
})

const { errors, handler } = useFormHandler(form)

const submit = async () => {
	const validation = await formRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	loading.value = true

	await authStore.resetPassword(form)
		.then(() => {
			successSnackbar(t('reset.your-password-has-been-su'))

			setTimeout(() => {
				router.push({ name: 'auth-login' })
			}, 600)
		})
		.catch(handler)
		.finally(() => {
			loading.value = false
		})
}
</script>
