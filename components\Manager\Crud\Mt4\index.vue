<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Accounts.MT4.Crud.account')"
		item-identity="name"
		:navigation-drawer-props="{ width: 650 }"
		:url="Manager.MT4._Id.URL(':login')"
		:validation-callback="validate"
		:update-url="Manager.MT4._Id.URL('')"
		update-method="POST"
		v-bind="$attrs"
		@closed="closeHandler"
		@loaded="loadedLogin = $event.login"
	>
		<!-- <template #title="{isUpdateAction,item,itemName}">
			<div v-if="isUpdateAction && item.login !== loadedLogin">
				{{ $t('Accounts.MT4.Crud.Manager.create-item', [item.login, itemName]) }}
			</div>
			<div v-else-if="isUpdateAction">
				{{ $t('Accounts.MT4.Crud.Manager.update-item', [item.login, itemName]) }}
			</div>
		</template> -->
		<template #default="{ item, errors }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' }, VBtn: { density: 'default' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-3 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class="flex-grow-1"
						>
							<v-form
								v-for="tab in tabs"
								ref="tabForm"
								:key="tab.value"
								v-model="tab.isValid.value"
							>
								<v-tabs-window-item
									:value="tab.value"
									:eager="isValidated"
								>
									<component
										:is="tab.component"
										:loaded-login="loadedLogin"
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-tabs-window-item>
							</v-form>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'

import Common from './Common.vue'
import Permissions from './Permissions.vue'
import Securities from './Securities.vue'
// import Info from './Info.vue'
import type { DefaultItem } from '~/components/Crud.vue'
import Crud from '~/components/Crud.vue'
import { Manager } from '~/types'

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem<Manager.MT4._Id.PUTRequest> = {
	login: 0,
	manager: 0,
	money: 0,
	online: 0,
	riskman: 0,
	broker: 0,
	admin: 0,
	logs: 0,
	reports: 0,
	trades: 0,
	marketWatch: 0,
	email: 0,
	userDetails: 0,
	seeTrades: 0,
	news: 0,
	plugins: 0,
	serverReports: 0,
	techSupport: 0,
	market: 0,
	notifications: 0,
	ipFilter: 0,
	ipFrom: 0,
	ipTo: 0,
	mailbox: '',
	groups: '',
	securityGroups: [
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 0,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 0,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
		{
			internalData: 0,
			enable: 1,
			minimumLots: 0,
			maximumLots: 10000000,
			reserved: [
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
				0,
			],
		},
	],
	expirationTime: 0,
	name: '',
	infoDepth: 0,
	reserved: [
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
	],
	unusedRights: [],
} satisfies DefaultItem<Manager.MT4._Id.POSTRequest | Manager.MT4._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const isValidated = ref(false)

const loadedLogin = ref(0)

type TabModel = 'common' | 'permissions' | 'securities'

type Tab = {
	value: TabModel
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs = computed<Tab[]>(() => ([
	{
		value: 'common',
		text: t('Manager.MT4.Crud.common'),
		component: Common,
		isValid: ref(true),
	},
	{
		value: 'permissions',
		text: t('Accounts.MT4.Crud.permissions'),
		component: Permissions,
		isValid: ref(true),
	},
	{
		value: 'securities',
		text: t('Accounts.MT4.Crud.securities'),
		component: Securities,
		isValid: ref(true),
	},

] as Tab[])
	.map((tab, index: number) => {
		tab.errorsCount = computed(() => tabForm.value?.[index]?.errors.length || 0)
		return tab
	}),
)

const tabModel = ref(tabs.value[0].value)

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error(t('Accounts.MT4.Crud.please-fix-the-form-errors')))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.value.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs.value[0].value
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
	openTab: (tab: TabModel) => void
}

defineExpose<Expose>({
	create: () => crudRef.value!.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
	openTab: (tab: TabModel) => (tabModel.value = tab),
})
</script>
