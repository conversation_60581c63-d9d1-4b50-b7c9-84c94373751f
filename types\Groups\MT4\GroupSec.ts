// Enumerations

export enum ExecutionMode {
	EXECUTION_MANUAL,
	EXECUTION_AUTO,
	EXECUTION_ACTIVITY,
}

export enum CommissionUnit {
	COMM_TYPE_MONEY,
	COMM_TYPE_PIPS,
	COMM_TYPE_PERCENT,
}

export enum CommissionCalculationType {
	COMMISSION_PER_LOT,
	COMMISSION_PER_DEAL,
}

export enum TradeRights {
	TRADE_DENY_NONE = 0,
	TRADE_DENY_CLOSEBY = 1,
	TRADE_DENY_MUCLOSEBY = 2,
}

export enum AutoCloseoutMode {
	CLOSE_OUT_NONE,
	CLOSE_OUT_HIHI,
	CLOSE_OUT_LOLO,
	CLOSE_OUT_HILO,
	CLOSE_OUT_LOHI,
	CLOSE_OUT_FIFO,
	CLOSE_OUT_LIFO,
	CLOSE_OUT_INTRDAY_FIFO,
}

// Structure Members in Camel Case
// Structure Members in Camel Case

/**
 * Description placeholder
 *
 * @export
 * @typedef {object} GroupSec
 * @property {number} show - A nonzero value enables, 0 disables the group of symbols for the group.
 * @property {number} trade - A nonzero value enables, 0 disables trading for the group of symbols.
 * @property {ExecutionMode} execution - Execution modes. Specified by a value from the Trade request processing modes enumeration.
 * @property {number} commBase - Commission amount. The units of commission depend on comm_type.
 * @property {CommissionUnit} commType - The units, in which the commission amount is specified. It is a value from the enumeration Commission units.
 * @property {CommissionCalculationType} commLots - Commission calculation type. Specified by a value from the Commission calculation type enumeration.
 * @property {number} commAgent - Amount of the agent commission. The units of commission depend on comm_agent_type.
 * @property {CommissionUnit} commAgentType - The units, in which the commission amount is specified. It is a value from the enumeration Commission units.
 * @property {number} spreadDiff - The difference between the symbol spread for the group and the default spread (ConSymbol::spread).
 * @property {number} lotMin - The minimum volume of symbol trading operations for the group of clients. A value of 100 corresponds to 1 lot, 1 = 0.01 lot.
 * @property {number} lotMax - The maximum volume of symbol trading operations for the group of clients. A value of 100 corresponds to 1 lot, 1 = 0.01 lot.
 * @property {number} lotStep - The step of volume change of the symbol trading operations for the group of clients. One unit corresponds to one hundredth of the lot.
 * @property {number} ieDeviation - The maximum allowable deviation of the price requested by the client from the current price (only for Instant Execution). If exceeded, request is rejected with "Requote".
 * @property {number} confirmation - A nonzero value indicates confirmation required, 0 means no additional confirmation from a dealer required in Request Execution mode.
 * @property {TradeRights} tradeRights - Flags (bit mask) of client permissions to close orders by opposite ones. Specified by a value from the Closure of counter orders enumeration.
 * @property {number} ieQuickMode - Fast confirmation of a client's request during Instant Execution.
 * @property {AutoCloseoutMode} autocloseoutMode - Mode of automatic closure of opposite positions at the end of the day.
 * @property {number} commTax - Tax on commissions in percentage terms.
 * @property {CommissionCalculationType} commAgentLots - Agent commission calculation type. Specified by a value from the Commission calculation type enumeration.
 * @property {number} freeMarginMode - A nonzero value means do not re-check the free margin after a dealer's response, 0 means free margin is checked again.
 * @property {number} reserved - Reserved field.
 */

export type GroupSec = {
	show: number
	trade: number
	execution: ExecutionMode
	commBase: number
	commType: CommissionUnit
	commLots: CommissionCalculationType
	commAgent: number
	commAgentType: CommissionUnit
	spreadDiff: number
	lotMin: number
	lotMax: number
	lotStep: number
	ieDeviation: number
	confirmation: number
	tradeRights: TradeRights
	ieQuickMode: number
	autoCloseoutMode: AutoCloseoutMode
	commTax: number
	commAgentLots: CommissionCalculationType
	freeMarginMode: number
	reserved: [
		0,
		0,
		0,
	]
}
