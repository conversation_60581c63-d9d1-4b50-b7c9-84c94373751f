<template>
	<div>
		<DefaultsGroup
			v-model="useMultipleDefaultModel(item, ['tradeModeDefault', 'fillFlagsDefault', 'expirFlagsDefault', 'orderFlagsDefault']).value"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.use-default-trade-setting')"
			:keys="[
				'tradeMode',
				'fillFlags',
				'expirFlags',
				'orderFlags',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-select
				v-model="item.tradeMode"
				:items="tradeModeItems"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.trade')"
			/>
			<v-select
				v-model="fillFlagsModel"
				multiple
				chips
				closable-chips
				:items="fillFlagsItems"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.filling')"
			/>
			<v-select
				v-model="expirFlagsModel"
				multiple
				chips
				closable-chips
				:items="expirFlagsItems"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.expiration')"
			/>
			<v-select
				v-model="orderFlagsModel"
				multiple
				chips
				closable-chips
				:items="orderFlagsItems"
				:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.orders')"
			/>
		</DefaultsGroup>

		<DefaultsGroup
			v-model="useMultipleDefaultModel(item, ['stopsLevelDefault', 'freezeLevelDefault']).value"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.use-default-trade-level-s')"
			:keys="[
				'stopsLevel',
				'freezeLevel',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model.number="item.stopsLevel"
						type="number"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.limit-and-stop-level')"
						suffix="pt"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model.number="item.freezeLevel"
						type="number"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Trade.freeze-level')"
						suffix="pt"
					/>
				</v-col>
			</v-row>
		</DefaultsGroup>
	</div>
</template>

<script lang="ts" setup>
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import DefaultsGroup from './Components/DefaultsGroup.vue'
import { Symbols } from '~/types'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const tradeModeItems = enumToItems(Symbols.MT5.TradeMode, 'Symbols.MT5.TradeMode')

const fillFlagsItems = enumToItems(Symbols.MT5.FillingFlags, 'Symbols.MT5.FillingFlags', item => [
	Symbols.MT5.FillingFlags.FILL_FLAGS_NONE,
	Symbols.MT5.FillingFlags.FILL_FLAGS_ALL,
].includes(item.value)
	? undefined
	: item)

const computedFillingFlags = computed<Symbols.MT5.FillingFlags>({
	get: () => p.item.fillFlags as Symbols.MT5.FillingFlags,
	set: (value: Symbols.MT5.FillingFlags) => emit('update:item', { fillFlags: value }),
})

const { model: fillFlagsModel } = useMultiSelectEnum(computedFillingFlags, Symbols.MT5.FillingFlags, {
	exclude: [
		Symbols.MT5.FillingFlags.FILL_FLAGS_NONE,
		Symbols.MT5.FillingFlags.FILL_FLAGS_ALL,
	],
})

const expirFlagsItems = enumToItems(Symbols.MT5.ExpirationFlags, 'Symbols.MT5.ExpirationFlags', item => [
	Symbols.MT5.ExpirationFlags.TIME_FLAGS_ALL,
].includes(item.value)
	? undefined
	: item)

const computedExpirFlags = computed<Symbols.MT5.ExpirationFlags>({
	get: () => p.item.expirFlags as Symbols.MT5.ExpirationFlags,
	set: (value: Symbols.MT5.ExpirationFlags) => emit('update:item', { expirFlags: value }),
})

const { model: expirFlagsModel } = useMultiSelectEnum(computedExpirFlags, Symbols.MT5.ExpirationFlags)

const orderFlagsItems = enumToItems(Symbols.MT5.OrderFlags, 'Symbols.MT5.OrderFlags', item => [
	Symbols.MT5.OrderFlags.ALL,
	Symbols.MT5.OrderFlags.NONE,
].includes(item.value)
	? undefined
	: item)

const computedOrderFlags = computed<Symbols.MT5.OrderFlags>({
	get: () => p.item.orderFlags as Symbols.MT5.OrderFlags,
	set: (value: Symbols.MT5.OrderFlags) => emit('update:item', { orderFlags: value }),
})

const { model: orderFlagsModel } = useMultiSelectEnum(computedOrderFlags, Symbols.MT5.OrderFlags, {
	exclude: [
		Symbols.MT5.OrderFlags.ALL,
		Symbols.MT5.OrderFlags.NONE,
	],
})
</script>
