export namespace ActionTypes {
	export namespace _Id {
		export const URL = (_Id: number | string) => `/action-types/${_Id}`

		export interface GETResponse {
			id: number
			// name: string
			displayName: string
			isIncremental: boolean
			operationId: number
			operation: {
				id: number
				name: null
				displayName: null
				type: null
				tradingPlatformId: null
				tradingPlatform: null
				createdAt: string
				updatedAt: null
			}
			createdAt: null
			updatedAt: null
		}

		export interface PUTRequest {
			// name: string
			displayName: string
			operationId: number
			isIncremental: boolean
		}

		export interface POSTRequest {
			// name: string
			displayName: string
			operationId: number
			isIncremental: boolean
		}

	}

}
