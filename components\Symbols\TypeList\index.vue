<template>
	<v-list
		:selected="selectedModel"
		select-strategy="single-independent"
		open-strategy="single"
		active-strategy="single-independent"
		class="position-relative"
		width="100%"
		slim
		density="compact"
	>
		<v-skeleton-loader
			type="list-item@10"
			:loading="status === 'pending'"
		>
			<div class="flex-grow-1">
				<TypeListItem
					v-for="item in computedTypes"
					:key="item.value"
					:item="item"
					@select="selectHandler"
				/>
				<div v-if="!hasTypes">
					<v-empty-state
						v-if="!!searchTypeModel"
						:text="$t('TypeList.no-type-found-matching-an', [searchTypeModel])"
					>
						<template #media>
							<v-icon
								class="mb-3"
								size="x-large"
								icon="i-material-symbols:group-work-outline"
							/>
						</template>
					</v-empty-state>
					<v-empty-state
						v-else
						:text="$t('TypeList.no-type-found')"
					>
						<template #media>
							<v-icon
								class="mb-3"
								size="x-large"
								icon="i-material-symbols:group-work-outline"
							/>
						</template>
					</v-empty-state>
				</div>
			</div>
		</v-skeleton-loader>
	</v-list>
</template>

<script lang="ts" setup>
import TypeListItem from './TypeListItem.vue'
import { Symbols } from '~/types'

defineOptions({
	inheritAttrs: false,
})
type Type = Symbols.MT4.Type.Lookup.SingleRecord

type Emit = {
	(e: 'loaded', value: Symbols.MT4.Type.Lookup.GETResponse): void
}

const emit = defineEmits<Emit>()

const model = defineModel<Symbols.MT4.Type.Lookup.SingleRecord | null>('modelValue', {

	default: null,
})

const searchTypeModel = defineModel('search', {
	type: String,
	default: '',
})

const debouncedSearchTypeModel = useDebounce(searchTypeModel, 300)

const types = ref<Type[]>([])

// const openedModel = defineModel('opened', {
// 	type: Array,
// 	default: [],
// })

const menuModel = ref(false)

const { data, status, refresh } = useApi<Symbols.MT4.Type.Lookup.GETResponse>(Symbols.MT4.Type.Lookup.URL, {
	key: Symbols.MT4.Type.Lookup.URL,
})

watch(() => data.value, (value) => {
	if (value) {
		types.value = value
		emit('loaded', value)
	}
})

const computedTypes = computed(() => {
	if (!debouncedSearchTypeModel.value) {
		return types.value
	}
	// filter the data based on the debouncedSearchTypeModel model, find the item.title or subtitle that contains the search string
	return types.value.filter((item) => {
		const search = debouncedSearchTypeModel.value.toLowerCase()
		const title = item.title.toLowerCase()
		const subtitle = item.subtitle.toLowerCase()
		return title.includes(search) || subtitle.includes(search)
	})
})

const hasTypes = computed(() => !!computedTypes.value.length)

const selectedModel = computed(() => model.value ? [model.value.value] : [])

const selectHandler = (item: Symbols.MT4.Type.Lookup.SingleRecord) => {
	model.value = item
	menuModel.value = false
}

type Exposed = {
	refresh: typeof refresh
}

defineExpose<Exposed>({
	refresh,
})
</script>
