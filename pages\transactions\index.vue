<template>
	<div>
		<page :title="$t('transactions.transactions')">
			<template #actions>
				<v-btn
					v-if="can('transactions.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="createCrud?.create()"
				>
					{{ $t('transactions.create-transaction') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/transactions"
				:headers="headers"
				no-search
			>
				<template #filter="{ model }">
					<ApiItems
						v-slot="props"
						url="/users/lookup"
					>
						<v-autocomplete
							v-model="model.users"
							:label="$t('transactions.users')"
							chips
							v-bind="props"
							multiple
							item-title="name"
							item-value="id"
							clearable
						/>
					</ApiItems>
					<ApiItems
						v-slot="props"
						url="/my/actions"
					>
						<v-autocomplete
							v-model="model.actionId"
							:label="$t('transactions.actions')"
							chips
							v-bind="props"
							item-title="displayName"
							item-value="id"
							clearable
						/>
					</ApiItems>
					<ApiItems
						v-slot="props"
						url="/my/accounts"
						:api-options="{
							params: {
								loginNumber: computed(() => fromAccountSearch || undefined),
							},
						}"
						:map-items="accountsMapper"
					>
						<v-autocomplete
							v-model="model.fromAccount"
							v-model:search="fromAccountSearch"
							:placeholder="$t('transactions.start-typing-to-search')"
							:label="$t('transactions.from-account')"
							v-bind="props"
							clearable
						/>
					</ApiItems>
					<ApiItems
						v-slot="props"
						url="/my/accounts"
						:api-options="{
							params: {
								loginNumber: computed(() => toAccountSearch || undefined),
							},
						}"
						:map-items="accountsMapper"
					>
						<v-autocomplete
							v-model="model.toAccount"
							v-model:search="toAccountSearch"
							:placeholder="$t('transactions.start-typing-to-search')"
							:label="$t('transactions.to-account')"
							v-bind="props"
							clearable
						/>
					</ApiItems>

					<v-label>{{ $t('transactions.status') }}</v-label>
					<v-chip-group
						v-model="model.status"
						filter
						column
						multiple
					>
						<TransactionsStatusLabel
							v-for="item in transactionStatuses"
							:key="(item.title as string)"
							:model-value="(item.value as Transactions.Status)"
						/>
					</v-chip-group>
				</template>
				<template #item.requestedAmount="{ item }">
					{{ money(item.requestedAmount, item.requestedCurrencySymbol) }}
				</template>
				<template #item.receivedAmount="{ item }">
					{{ money(item.requestedAmount, item.receivedCurrencySymbol) }}
				</template>
				<template #item.usdAmount="{ item }">
					{{ money(item.usdAmount, 'USD') }}
				</template>
				<template #item.status="{ value }">
					<TransactionsStatusLabel :model-value="value" />
				</template>

				<template #item.executedAt="{ value }">
					<template v-if="value">
						{{ dateTime(value) }}
					</template>
					<template v-else>
						<v-divider />
					</template>
				</template>

				<template #item.executedByName="{ value }">
					<template v-if="value">
						{{ (value) }}
					</template>
					<template v-else>
						<v-divider />
					</template>
				</template>

				<template #item.actions="{ item }">
					<v-btn
						size="small"
						variant="text"
						icon="i-mdi:eye"
						@click="viewCrud?.update(item)"
					/>
				</template>
			</datatable>
		</page>
		<create-transaction-crud
			ref="createCrud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
		<view-transaction-crud
			ref="viewCrud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type CreateTransactionCrud from '~/components/CreateTransactionCrud.vue'
import TransactionsStatusLabel from '~/components/Transactions/StatusLabel.vue'
import { Transactions } from '~/types/Transactions'

definePageMeta({
	permission: 'transactions.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)

const createCrud = ref<typeof CreateTransactionCrud | null>(null)

const viewCrud = ref<typeof CreateTransactionCrud | null>(null)

const fromAccountSearch = ref('')

const toAccountSearch = ref('')

const headers: Headers = [
	{
		title: '',
		value: 'index',

	},

	{
		title: t('transactions.action'),
		value: 'actionName',
		fixed: true,
		width: 200,
		minWidth: '200',
		cellProps: {
			class: 'text-capitalize',
		},

	},
	{
		title: t('transactions.status'),
		value: 'status',
		width: 120,
		minWidth: '120',
		fixed: true,
	},
	{
		value: 'actions',
		fixed: true,
	},

	{
		title: t('transactions.operation-name'),
		value: 'operationName',
	},
	{
		title: t('transactions.trading-server'),
		value: 'tradingServerName',
		nowrap: true,
	},
	{
		title: t('transactions.sender-account'),
		value: 'senderAccount',
		nowrap: true,
	},
	{
		title: t('transactions.receiver-account'),
		value: 'receiverAccount',
		nowrap: true,
	},
	{
		title: t('transactions.requested-by'),
		value: 'requestedByUserName',
		nowrap: true,
	},
	{
		title: t('transactions.requested-amount'),
		value: 'requestedAmount',
		nowrap: true,
	},
	{
		title: t('transactions.received-amount'),
		value: 'receivedAmount',
		nowrap: true,
	},
	{
		title: t('transactions.usd-amount'),
		value: 'usdAmount',
		nowrap: true,
	},

	{
		title: t('transactions.executed-by'),
		value: 'executedByName',
		nowrap: true,
	},
	{
		title: t('transactions.executed-at'),
		value: 'executedAt',
		nowrap: true,
	},
	{
		title: t('transactions.timestamp'),
		value: 'timestamp',
		nowrap: true,
	},
]

const transactionStatuses = enumToItems(Transactions.Status)

const accountsMapper = (items: any[]) => items
	.map(i => ({
		title: i.login,
		value: i.login,
		props: {
			subtitle: i.group,
		},
	}))
</script>
