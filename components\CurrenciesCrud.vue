<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Currency"
		item-identity="name"
		:navigation-drawer-props="{ width: 550 }"
		url="/currencies/:id"
		v-bind="$attrs"
	>
		<template #default="{ item, errors }: { item: Item<Currencies._Id.GETResponse>, errors: ItemErrors<Currencies._Id.GETResponse>}">
			<v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				:rules="rules({ required: true })"
				:label="$t('CurrenciesCrud.name')"
			/>
			<v-text-field
				v-model="item.symbol"
				:error-messages="errors.symbol"
				:rules="rules({ required: true })"
				:label="$t('CurrenciesCrud.symbol')"
				@update:model-value="symbolCapitalized($event, item)"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from './Crud.vue'
import type { DefaultItem, Item, ItemErrors } from './Crud.vue'
import type { Currencies } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Currencies._Id.POSTRequest | Currencies._Id.PUTRequest> = {
	name: '',
	symbol: '',
}

const symbolCapitalized = (value: string, item: Item<Currencies._Id.GETResponse>) => {
	item.symbol = value?.toUpperCase()
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
