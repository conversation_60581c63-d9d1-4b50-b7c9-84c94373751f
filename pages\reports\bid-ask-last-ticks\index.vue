<template>
	<page :title="$t('bid-ask-last-ticks.bid-ask-last-ticks')">
		<template #actions>
			<v-btn
				prepend-icon="i-mdi:export-variant"
				color="primary"
				:loading="isExporting"
				:disabled="!hasData"
				@click="exportData"
			>
				{{ $t('bid-ask-last-ticks.export-data') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/report/historical-symbol-prices"
			:headers="headers"
			:api-options="{ immediate: false }"
			no-search
			:on-mounted="table?.showFilter()"
			:default-model="{ period: 0, fromDate: null, toDate: null, symbol: null }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
			persistent-filter
		>
			<template #filter="{ model }">
				<symbols-lookup
					v-model="model.symbol"
					component="combobox"
					return="symbol"
					:label="$t('bid-ask-last-ticks.symbol')"
					:rules="rules({ required: true })"
				/>

				<date-time-input
					v-model="model.fromDate"
					:rules="rules({ required: true })"
					:max="model.toDate || new Date()"
					:label="$t('bid-ask-last-ticks.from')"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('bid-ask-last-ticks.use-mt-timezone')"
				/>
				<date-time-input
					v-model="model.toDate"
					:rules="rules({ required: true })"
					:label="$t('bid-ask-last-ticks.to')"
					:min="model.fromDate"
					:max="new Date()"
					clearable
					format="YYYY-MM-DD HH:mm:ss"
					:time-picker-props="{
						useSeconds: true,
						ampmInTitle: true,
					}"
					:hint="$t('bid-ask-last-ticks.use-mt-timezone')"
				/>
			</template>
		</datatable>
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'

definePageMeta({
	permission: 'ticksHistoryPrices.view',
})

const { t } = useI18n()

const { $api } = useNuxtApp()

const { successSnackbar, errorSnackbar } = useSnackbar()

const isExporting = ref(false)

const hasData = computed(() => table.value?.items.length > 0)

const exportData = () => {
	isExporting.value = true

	const query = table.value?.query

	const { cloned: clonedQuery } = useCloned(query, { deep: true })

	delete clonedQuery.value.page
	delete clonedQuery.value.pageSize

	$api('/report/historical-symbol-prices/export', {
		params: {
			...clonedQuery.value,
		},
	})
		.then(() => {
			successSnackbar({
				title: t('bid-ask-last-ticks.exporting-data'),
				text: t('bid-ask-last-ticks.the-data-is-being-exported'),
			})
		})
		.catch((e) => {
			errorSnackbar(e.value.message)
		})
		.finally(() => {
			isExporting.value = false
		})
}
const table = ref<typeof Datatable | null>(null)

const headers: Headers = [
	{
		title: t('bid-ask-last-ticks.ask-price'),
		value: 'askPrice',
	},
	{
		title: t('bid-ask-last-ticks.bid-price'),
		value: 'bidPrice',
	},
	{
		title: t('bid-ask-last-ticks.time'),
		value: 'time',
	},

]
</script>
