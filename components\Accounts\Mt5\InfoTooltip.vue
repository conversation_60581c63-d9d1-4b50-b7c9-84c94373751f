<template>
	<info-tooltip
		v-model="lazyModel"
		open-on-hover
		:disabled="cannot('accounts.view')"
	>
		<slot />
		<template #info>
			<v-lazy
				min-width="220"
			>
				<v-skeleton-loader
					:loading="status === 'pending'"
					type="paragraph@1"
				>
					<div v-if="status === 'success'">
						<h4 class="mb-2">
							{{ data?.name }}
						</h4>
						<div class="mb-1">
							<b>{{ $t('Accounts.InfoTooltip.group') }}:</b> {{ data?.group || 'N/A' }}
						</div>
						<div class="mb-1">
							<b>{{ $t('Accounts.InfoTooltip.company') }}:</b> {{ data?.company || 'N/A' }}
						</div>
						<div class="my-1">
							<b>{{ $t('Accounts.InfoTooltip.city') }}:</b> {{ data?.city || 'N/A' }}
						</div>
						<div class="mt-1">
							<b>{{ $t('Accounts.InfoTooltip.email') }}:</b> {{ data?.address || 'N/A' }}
						</div>
						<div class="mt-1">
							<b>{{ $t('Accounts.InfoTooltip.balance') }}:</b> {{ data?.balance || 'N/A' }}
						</div>
					</div>
					<div v-else-if="isNotFound">
						{{ $t('Accounts.InfoTooltip.there-is-no-account-with-', [p.login]) }}
					</div>
					<v-list-item
						v-else
						:title="$t('Accounts.InfoTooltip.couldnt-load-account-info')"
						:subtitle="$t('Accounts.InfoTooltip.it-could-be-the-account-h')"
					>
						<template #prepend>
							<v-icon
								icon="i-mdi:alert"
								color="warning"
							/>
						</template>
					</v-list-item>
				</v-skeleton-loader>
			</v-lazy>
		</template>

		<template #actions>
			<v-spacer />

			<template v-if="!isNotFound">
				<v-btn
					v-if="can('accounts.edit')"
					:disabled="status !== 'success'"
					size="small"
					prepend-icon="i-mdi:pencil"
					variant="text"
					@click="emit('update', { login })"
				>
					{{ $t('Accounts.InfoTooltip.edit') }}
				</v-btn>
			</template>
			<template v-else>
				<v-btn
					v-if="can('accounts.create')"
					size="small"
					prepend-icon="i-mdi:pencil"
					variant="text"
					@click="emit('create', { login })"
				>
					{{ $t('Accounts.InfoTooltip.create') }}
				</v-btn>
			</template>
		</template>
	</info-tooltip>
</template>

<script lang="ts" setup>
import InfoTooltip from '~/components/InfoTooltip.vue'
import { Accounts } from '~/types'

type Emit = {
	(e: 'update' | 'create', value: { login: number }): void
}

type Props = {
	login: number
}

defineOptions({
	extends: InfoTooltip,
})

const p = withDefaults(defineProps<Props>(), {
})

const emit = defineEmits<Emit>()

const lazyModel = ref(false)

const { data, execute, status, error } = useApi<Accounts.MT5._Id.GETResponse>(Accounts.MT5._Id.URL(p.login), {
	immediate: false,
	cache: 'default',
	key: `accounts-info-tooltip:groups/${p.login}`,
	headers: {
		'Cache-Control': 'max-age=3600',
	},
})

const unwatch = watch(() => lazyModel.value, (value) => {
	if (value) {
		execute()
	}
})

watch(() => status.value, (value) => {
	if (value === 'success') {
		unwatch()
	}
})

const isNotFound = computed(() => error.value?.statusCode === 404)
</script>
