import type { Groups } from '../Groups'

export namespace Accounts {
	export namespace MT4 {

		export namespace Lookup {
			export const URL = '/accounts/mt4/lookup'
			export interface SingleRecord {

			}

			export type GETResponse = SingleRecord[]
		}
		export enum EnUserFlags {
			USER_FLAGS_NONE = 0x00000000,
			USER_FLAGS_SPONSORED_HOSTING = 0x00000001,
		}
		export type MTAccountStandard = {
			login: number
			group: string
			password: string
			enable: number
			enableChangePassword: number
			enableReadOnly: number
			enableOtp: Groups.MT4.OtpMode
			enableFlags: EnUserFlags
			enableReserved: number[]
			passwordInvestor: string
			passwordPhone: string
			name: string
			country: string
			city: string
			state: string
			zipcode: string
			address: string
			leadSource: string
			phone: string
			email: string
			comment: string
			id: string
			status: string
			leverage: number
			agentAccount: number
			timestamp: string
			lastIp: number
			balance: number
			previousMonthBalance: number
			previousBalance: number
			credit: number
			interestRate: number
			taxes: number
			previousMonthEquity: number
			previousEquity: number
			reserved2: number[]
			otpSecret: string
			secureReserved: string
			sendReports: number
			mqid: number
			userColor: number
			unused: string
			apiData: string
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/accounts/mt4/${_Id}`

			export interface GETResponse {
				login: number
				group: string
				password: string
				enable: number
				enableChangePassword: number
				enableReadOnly: number
				enableOtp: number
				enableFlags: number
				enableReserved: number[]
				passwordInvestor: string
				passwordPhone: string
				name: string
				country: string
				city: string
				state: string
				zipcode: string
				address: string
				leadSource: string
				phone: string
				email: string
				comment: string
				id: string
				status: string
				registrationDate: string
				lastConnectionDate: string
				leverage: number
				agentAccount: number
				timestamp: Date
				lastIp: number
				balance: number
				previousMonthBalance: number
				previousBalance: number
				credit: number
				interestRate: number
				taxes: number
				previousMonthEquity: number
				previousEquity: number
				reserved2: number[]
				otpSecret: string
				secureReserved: string
				sendReports: number
				mqid: number
				userColor: number
				unused: string
				apiData: string
			}

			export interface POSTRequest extends MTAccountStandard { }

			export interface PUTRequest extends MTAccountStandard { }

		}
		export namespace ChangePassword {
			export const URL = '/accounts/mt4/ChangeAccountPassword'
			export interface POSTRequest {
				login: MTAccountStandard['login']
				password: string
				changeInvestor: 0 | 1
				cleanPubkey: 0 | 1
			}
		}

		export namespace CheckPassword {
			export const URL = '/accounts/mt4/CheckUserPassword'
			export interface POSTRequest {
				login: MTAccountStandard['login']
				password: string
			}
		}
	}

	export namespace MT5 {

		/**
		 * Permissions that can be given to a user are enumerated in IMTUser::EnUsersRights.
		 */
		export enum EnUsersRights {
			/**
			 * No permissions.
			 */
			USER_RIGHT_NONE = 0,

			/**
			 * The user is allowed to connect.
			 */
			USER_RIGHT_ENABLED = 1,

			/**
			 * The user is allowed to change the password.
			 */
			USER_RIGHT_PASSWORD = 2,

			/**
			 * Trading is disabled for the user.
			 */
			USER_RIGHT_TRADE_DISABLED = 4,

			/**
			 * Service value for internal use.
			 */
			USER_RIGHT_INVESTOR = 8,

			/**
			 * User's certificate is confirmed.
			 */
			USER_RIGHT_CONFIRMED = 16,

			/**
			 * The user is allowed to use trailing stop.
			 */
			USER_RIGHT_TRAILING = 32,

			/**
			 * The user is allowed to use Expert Advisors.
			 */
			USER_RIGHT_EXPERT = 64,

			/**
			 * The flag is obsolete and is not used.
			 */
			USER_RIGHT_OBSOLETE = 128,

			/**
			 * The user is allowed to receive daily reports. If the permission is not enabled, daily reports are neither generated nor sent for the account.
			 */
			USER_RIGHT_REPORTS = 256,

			/**
			 * Service value for internal use.
			 */
			USER_RIGHT_READONLY = 512,

			/**
			 * The user must change password during the next connection.
			 */
			USER_RIGHT_RESET_PASS = 1024,

			/**
			 * The user can use OTP authentication.
			 */
			USER_RIGHT_OTP_ENABLED = 2048,

			/**
			 * Brokers can pay the virtual hosting fee for their customers. The service is extremely important for traders, and the opportunity to receive a VPS for free can give them a good reason to choose your company over competitors. The availability of a broker-sponsored VPS is controlled at the individual account level. Only if this flag is enabled, the appropriate payment plan will be shown to the trader in the client terminal. For more details, please read the appropriate section.
			 */
			USER_RIGHT_SPONSORED_HOSTING = 8192,

			/**
			 * The user is allowed to connect via the Web API. The flag is obsolete and is not used.
			 */
			USER_RIGHT_API_ENABLED = 16384,

			/**
			 * The user has enabled Push notifications from the trade server in the client terminal. The ability to subscribe to such notifications is defined by the group settings (IMTConGroup::PERMISSION_NOTIFY_*).
			 */
			USER_RIGHT_PUSH_NOTIFICATION = 32768,

			/**
			 * Permission for convenient work with technical accounts. Disable it for testing accounts to hide them from all managers who do not have special access to technical accounts (IMTConManager::RIGHT_ACC_TECHNICAL). Such technical accounts can be confusing for the managers working with clients, in which case hiding them can be useful.
			 * The permission affects the visibility in the general list of accounts in the Administrator and Manager terminals, as well as in the list of online accounts in the Manager terminal.
			 */
			USER_RIGHT_TECHNICAL = 65536,

			/**
			 * Allows excluding an account from server reports. Like the previous permission, it is intended for more convenient work with technical accounts.
			 */
			USER_RIGHT_EXCLUDE_REPORTS = 131072,

			USER_RIGHT_ALLOW_DATAFEED = 262144,
		}

		export enum Language {
			Afrikaans = 54,
			Albanian = 28,
			Arabic = 1,
			Armenian = 43,
			Azerbaijani = 44,
			Basque = 45,
			Belarusian = 35,
			Bulgarian = 2,
			ChineseTraditional = 4,
			Croatian = 26,
			Czech = 3,
			Danish = 6,
			Dutch = 7,
			English = 0,
			Estonian = 8,
			Finnish = 9,
			French = 12,
			Georgian = 46,
			German = 13,
			Greek = 14,
			Hebrew = 15,
			Hindi = 47,
			Hungarian = 16,
			Indonesian = 17,
			Italian = 18,
			Japanese = 19,
			Korean = 20,
			Latvian = 38,
			Lithuanian = 39,
			Macedonian = 29,
			Norwegian = 21,
			Oriya = 48,
			Persian = 49,
			Polish = 22,
			Portuguese = 23,
			Romanian = 24,
			Russian = 25,
			Slovak = 27,
			Slovenian = 30,
			Spanish = 10,
			Swedish = 11,
			Tatar = 40,
			Thai = 31,
			Turkish = 32,
			Ukrainian = 33,
			Urdu = 50,
			Vietnamese = 34,
		}

		export enum EnUsersPasswords {
			USER_PASS_MAIN = 0,
			USER_PASS_INVESTOR = 1,
			USER_PASS_API = 2,
		}
		export namespace Lookup {
			export const URL = '/accounts/mt5/lookup'

			export interface SingleRecord {

			}

			export type GETResponse = SingleRecord[]
		}

		export type MTAccountStandard = {
			login: number
			group: string
			certSerialNumber: number
			rights: EnUsersRights
			registration: number
			lastAccess: number
			lastIP: string
			name: string
			company: string
			account: string
			country: string
			language: Language
			city: string
			state: string
			zipCode: string
			address: string
			phone: string
			eMail: string
			id: string
			status: string
			comment: string
			color: number
			phonePassword: string
			leverage: number
			agent: number
			balance: number
			credit: number
			interestRate: number
			commissionDaily: number
			commissionMonthly: number
			commissionAgentDaily: number
			commissionAgentMonthly: number
			balancePrevDay: number
			balancePrevMonth: number
			equityPrevDay: number
			equityPrevMonth: number
			lastPassChange: number
			leadCampaign: string
			leadSource: string
			externalAccountTotal: number
			mqid: string
			clientID: number
			firstName: string
			lastName: string
			middleName: string
			otpSecret: string
			limitOrders: number
			limitPositionsValue: number
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/accounts/mt5/${_Id}`

			export interface GETResponse extends MTAccountStandard {

			}

			export interface POSTRequest extends MTAccountStandard {
				// webLogin?: number;
				// webPassword?: string;
				// ipAddress?: string;
				// port?: number;
				// login: number;
				// group: string;
				// certSerialNumber: number;
				// rights: EnUsersRights;
				// registration: number;
				// lastAccess: number;
				// lastIP: string;
				// name: string;
				// company: string;
				// account: string;
				// country: string;
				// language: Language;
				// city: string;
				// state: string;
				// zipCode: string;
				// address: string;
				// phone: string;
				// email: string;
				// id: string;
				// status: string;
				// comment: string;
				// color: number;
				// phonePassword: string;
				// leverage: number;
				// agent: number;
				// balance: number;
				// credit: number;
				// interestRate: number;
				// commissionDaily: number;
				// commissionMonthly: number;
				// commissionAgentDaily: number;
				// commissionAgentMonthly: number;
				// balancePrevDay: number;
				// balancePrevMonth: number;
				// equityPrevDay: number;
				// equityPrevMonth: number;
				// lastPassChange: number;
				// leadCampaign: string;
				// leadSource: string;
				// externalAccountTotal: number;
				// mqid: string;
				// clientID: number;
				// firstName: string;
				// lastName: string;
				// middleName: string;
				// otpSecret: string;
				// limitOrders: number;
				// limitPositionsValue: number;
				masterPass: string
				investorPass: string
			}

			export interface PUTRequest extends MTAccountStandard {

			}

		}

		export namespace ChangePassword {
			export const URL = '/accounts/mt5/change-password'
			export interface POSTRequest {
				login: number
				password: string
				passwordType: EnUsersPasswords
			}
		}

		export namespace CheckPassword {
			export const URL = '/accounts/mt5/check-password'

			export interface POSTRequest {
				login: number
				password: string
				passwordType: EnUsersPasswords
			}
		}

	}
}
