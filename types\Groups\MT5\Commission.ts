// IMTConCommission::EnCommMode
export enum EnCommMode {
	COMM_STANDARD = 0, // Standard commission.
	COMM_AGENT = 1, // Agent commission.
	COMM_FEE = 2, // Fee similar to standard commission.
}

// IMTConCommission::EnCommRangeMode
export enum EnCommRangeMode {
	COMM_RANGE_VOLUME = 0, // Commission levels by volume.
	COMM_RANGE_TURNOVER_MONEY = 1, // Commission levels by turnover in money.
	COMM_RANGE_TURNOVER_VOLUME = 2, // Commission levels by turnover in lots.
}

// IMTConCommission::EnCommChargeMode
export enum EnCommChargeMode {
	COMM_CHARGE_DAILY = 0, // Charging at the end of the day.
	COMM_CHARGE_MONTHLY = 1, // Charging at the end of the month.
	COMM_CHARGE_INSTANT = 2, // Immediate commission charging.
}

// IMTConCommission::EnCommEntryMode
export enum EnCommEntryMode {
	COMM_ENTRY_ALL = 0, // Commission charged irrespective of deal direction.
	COMM_ENTRY_IN = 1, // Commission only on entry deals.
	COMM_ENTRY_OUT = 2, // Commission only on exit deals.
}

// IMTConCommission::EnCommActionMode
export enum EnCommActionMode {
	COMM_ACTION_ALL = 0, // All transactions.
	COMM_ACTION_BUY = 1, // Only Buy deals.
	COMM_ACTION_SELL = 2, // Only Sell deals.
}

// IMTConCommission::EnCommProfitMode
export enum EnCommProfitMode {
	COMM_PROFIT_ALL = 0, // All deals.
	COMM_PROFIT_PROFIT = 1, // Only profitable deals.
	COMM_PROFIT_LOSS = 2, // Only losing deals.
}

// IMTConCommission::EnCommReasonFlags
export enum EnCommReasonFlags {
	COMM_REASON_FLAG_NONE = 0x00000000, // No commission.
	COMM_REASON_FLAG_CLIENT = 0x00000001, // Client manual transaction.
	COMM_REASON_FLAG_EXPERT = 0x00000002, // Expert Advisor transaction.
	COMM_REASON_FLAG_DEALER = 0x00000004, // Dealer transaction.
	COMM_REASON_FLAG_EXTERNAL_CLIENT = 0x00000008, // External trading system transaction.
	COMM_REASON_FLAG_MOBILE = 0x00000010, // Mobile terminal transaction.
	COMM_REASON_FLAG_WEB = 0x00000020, // Web terminal transaction.
	COMM_REASON_FLAG_SIGNAL = 0x00000040, // Trading signal subscription transaction.
	COMM_REASON_FLAG_ALL = 0xFFFFFFFF, // Enables commissions for all reasons.
}

/**
 * Enumeration of commission charging methods.
 */
export enum EnCommissionMode {
	/**
	 * Money, in the group deposit currency.
	 */
	COMM_MONEY_DEPOSIT = 0,

	/**
	 * Money, in the base currency of the symbol.
	 */
	COMM_MONEY_SYMBOL_BASE = 1,

	/**
	 * Money, in the symbol profit currency.
	 */
	COMM_MONEY_SYMBOL_PROFIT = 2,

	/**
	 * Money, in the symbol margin currency.
	 */
	COMM_MONEY_SYMBOL_MARGIN = 3,

	/**
	 * In pips.
	 */
	COMM_PIPS = 4,

	/**
	 * Percent from the real price of the deal / turnover. The real price is calculated in base currency of the symbol as the product of its volume, contract size and price (and Tick price/Tick size ratio for Futures). By default, the trade/turnover value calculated in the base currency is converted to the group deposit currency, and the final commission (the specified percentage) is calculated based on this value.
	 *
	 * The currency to which the trade/turnover value is converted, can be overridden using the IMTConCommission::TurnoverCurrency property. For example, the price of Buy 1 lot EURUSD with a contract size of 100,000 is 100,000 EUR. If the client's deposit currency is USD, the position price is converted at EURUSD rate as of the time of the trade execution. If a specific currency is specified in the Currency field, e.g. AUD, then the value of this position will be converted to Australian dollars at the EURAUD rate. Commission (percent) will be calculated from the resulting value. Further, this value will be converted to the group deposit currency and deducted from the client.
	 */
	COMM_PERCENT = 5,

	/**
	 * Money, in the specified currency. The currency is specified in the IMTConCommTier::Currency field.
	 */
	COMM_MONEY_SPECIFIED = 6,

	/**
	 * The commission will be calculated as a percentage of the deal profit (if charged instantly) or of the trader's total profit for the day or month (if charged at the end of the day or month). In case of a loss, the corresponding commission value will not be debited from the trader, but will be credited to the trader's account instead. To avoid such charges, set the minimum commission size to 0 and/or set the filter by deal profit.
	 */
	COMM_PERCENT_PROFIT = 7,
}

/**
 * Enumeration of commission charging types depending on the trade volume.
 */
export enum EnCommissionVolumeType {
	/**
	 * Commission from a deal.
	 */
	COMM_TYPE_DEAL = 0,

	/**
	 * Commission from a volume.
	 */
	COMM_TYPE_VOLUME = 1,
}

export interface CommissionLevel {
	/**
	 * The method of commission charging. Passed in a value of the EnCommissionMode enumeration.
	 */
	mode: EnCommissionMode

	/**
	 * The type of commission charging. Passed in a value of the EnCommissionVolumeType enumeration.
	 */
	type: EnCommissionVolumeType

	/**
	 * Commission amount.
	 */
	value: number

	/**
	 * The minimum amount of commission charged.
	 */
	minimal: number
	/**
	 * The maximal amount of commission charged.
	 */
	maximal: number

	/**
	 * The minimum trade volume (turnover) from which the commission will be charged.
	 */
	rangeFrom: number

	/**
	 * The maximum trade volume (turnover) from which the commission will be charged.
	 */
	rangeTo: number

	/**
	 * Currency, which is used for commission calculation. This field is used only in the COMM_MONEY_SPECIFIED mode of commission calculation.
	 */
	currency: string
}
export interface Commission {
	/**
	 * Name of the commission configuration.
	 */
	name: string

	/**
	 * Description of the commission configuration.
	 */
	description: string

	/**
	 * The path to a symbol or group of symbols that are subject to commission.
	 */
	path: string

	/**
	 * Type of commission. Passed in a value of the EnCommMode enumeration.
	 */
	mode: EnCommMode

	/**
	 * Type of commission ranges — by trade volume or turnover. Passed in a value of the EnCommRangeMode enumeration.
	 */
	rangeMode: EnCommRangeMode

	/**
	 * Time of commission charging. Passed in a value of the EnCommChargeMode enumeration.
	 */
	chargeMode: EnCommChargeMode

	/**
	 * Currency, in which the money turnover is calculated.
	 */
	turnoverCurrency: string

	/**
	 * Commission calculation mode depending on the trade direction. The mode is passed as a value of the EnCommEntryMode enumeration.
	 */
	entryMode: EnCommEntryMode

	/**
	 * Commission calculation mode depending on the trade type. The mode is passed as a value of the EnCommActionMode enumeration.
	 */
	actionMode: EnCommActionMode

	/**
	 * Commission calculation mode depending on the trade result (profit/loss). The mode is passed as a value of the EnCommProfitMode enumeration.
	 */
	profitMode: EnCommProfitMode

	/**
	 * Commission calculation mode depending on the trade reason. The mode is passed as a value of the EnCommReasonFlags enumeration.
	 */
	reasonMode: EnCommReasonFlags

	/**
	 * An array of commission ranges.
	 */
	tiers: CommissionLevel[]
}
