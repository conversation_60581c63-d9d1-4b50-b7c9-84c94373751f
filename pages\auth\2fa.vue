<template>
	<Title>{{ $t('2fa.two-factors-authentications') }}</Title>
	<v-form
		ref="formRef"
		class="d-flex flex-column justify-center align-center h-100"
		@submit.prevent="verify"
	>
		<p class="text-medium-emphasis">
			{{ $t('2fa.please-enter-the-6-digit-generated-by-your-authent') }}
		</p>

		<v-input
			:model-value="code"
			:rules="rules({ required: true, type: 'string', len: 6 })"
			validate-on="blur"
			width="auto"
			class="d-inline-block mx-auto"
		>
			<v-otp-input
				ref="otpInputRef"
				v-model="code"
				dir="ltr"
				:disabled="loading"
				autofocus
				length="6"
				class="mx-n2"
				@finish="verify"
			/>
		</v-input>

		<!-- <div class="d-flex justify-end">
			<v-btn :disabled="!authStore.canGenerate2FA" variant="text" size="small" class="text-capitalize" @click="authStore.generate2FA()">
				Resend 2FA Code ({{ authStore.gen2FaRemainingTime }})
			</v-btn>
		</div> -->

		<v-btn
			class="mt-0 mb-4"
			color="primary"
			size="large"
			type="submit"
			variant="elevated"
			block
			:disabled="loading"
			:loading="loading"
		>
			{{ $t('2fa.verify') }}
		</v-btn>
		<v-btn

			color="primary"
			type="submit"
			variant="plain"
			block
			:to="{ name: 'auth-login' }"
		>
			{{ $t('2fa.login-with-a-different-account') }}
		</v-btn>
		<v-spacer />
	</v-form>
</template>

<script lang="ts" setup>
import type { VForm, VOtpInput } from 'vuetify/components'

definePageMeta({
	middleware: 'has-token-but-no2fa',
})

const authStore = useAuthStore()

const loading = ref(false)

const formRef = ref<InstanceType<typeof VForm> | null>(null)

const otpInputRef = ref<InstanceType<typeof VOtpInput> | null>(null)

const code = ref<string>('')

const { handler } = useFormHandler({ code })

const router = useRouter()

const baseRouteName = useRouteBaseName()

const verify = async () => {
	const validation = await formRef.value?.validate()

	if (!validation?.valid || code.value.length !== 6) {
		return
	}
	loading.value = true
	authStore.verify2FA(code.value, 'app')
		.then(() => {
			router.push({ name: 'auth-logging', hash: router.currentRoute.value.hash })
		})
		.catch(handler)
		.finally(() => {
			loading.value = false
		})
}

watch(() => router.currentRoute.value, (route) => {
	if (baseRouteName(route) === 'auth-2fa') {
		nextTick(() => {
			otpInputRef.value?.$el.querySelector('input')?.focus()
		})
	}
})
</script>
