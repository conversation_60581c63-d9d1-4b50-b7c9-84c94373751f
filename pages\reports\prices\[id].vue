<template>
	<page :title="$t('prices-id.report-details')">
		<template #actions>
			<v-btn
				variant="text"
				class="me-2"
				prepend-icon="i-mdi:upload"
				exact
				:to="{ name: 'reports-prices' }"
			>
				{{ $t('prices-id.upload-another-file') }}
			</v-btn>
			<v-btn
				prepend-icon="i-mdi:download"
				:loading="isDownloading"
				@click="downloadFile"
			>
				{{ $t('prices-id.download-report') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			:url="`/report/prices/${id}`"
			:headers="headers"
			no-search
			no-pagination
		/>
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'

definePageMeta({
	permission: 'reports.prices',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)

const route = useRoute()

const { $api } = useNuxtApp()

const { errorSnackbar } = useSnackbar()

const isDownloading = ref(false)

// @ts-ignore
const { id } = route.params

const headers: Headers = [
	{
		title: t('prices-id.date'),
		value: 'date',
		nowrap: true,
	},
	{
		title: t('prices-id.symbol'),
		value: 'symbol',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus3-seconds-high-ask'),
		value: 'priceMinus3SecondsHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus3-seconds-low-bid'),
		value: 'priceMinus3SecondsLowBid',
		nowrap: true,
	},
	{
		title: t('prices-id.price-plus3-seconds-high-ask'),
		value: 'pricePlus3SecondsHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-plus3-seconds-low-bid'),
		value: 'pricePlus3SecondsLowBid',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus1-minute-high-ask'),
		value: 'priceMinus1MinuteHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus1-minute-low-bid'),
		value: 'priceMinus1MinuteLowBid',
		nowrap: true,
	},

	{
		title: t('prices-id.price-plus1-minutes-high-ask'),
		value: 'pricePlus1MinuteHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-plus1-minutes-low-bid'),
		value: 'pricePlus1MinuteLowBid',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus3-minutes-high-ask'),
		value: 'priceMinus3MinutesHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-minus3-minutes-low-bid'),
		value: 'priceMinus3MinutesLowBid',
		nowrap: true,
	},
	{
		title: t('prices-id.price-plus3-minutes-high-ask'),
		value: 'pricePlus3MinutesHighAsk',
		nowrap: true,
	},
	{
		title: t('prices-id.price-plus3-minutes-low-bid'),
		value: 'pricePlus3MinutesLowBid',
		nowrap: true,
	},
	{
		title: t('prices-id.lowest-price-minus6-minutes'),
		value: 'lowesetPriceMinusIn6Minutes',
		nowrap: true,
	},
	{
		title: t('prices-id.highest-price-minus6-minutes'),
		value: 'highestPriceMinusIn6Minutes',
		nowrap: true,
	},
	{
		title: t('prices-id.lowest-price-plus6-minutes'),
		value: 'lowesetPricePlusIn6Minutes',
		nowrap: true,
	},
	{
		title: t('prices-id.highest-price-plus6-minutes'),
		value: 'highestPricePlusIn6Minutes',
		nowrap: true,
	},

]

const downloadFile = () => {
	isDownloading.value = true
	$api<Blob>(`/report/prices/${id}/export`)
		.then((res) => {
			downloadFileFromApi(res, `report-${id}.xlsx`)
		})
		.catch((err) => {
			errorSnackbar(err.message)
		})
		.finally(() => {
			isDownloading.value = false
		})
}
</script>
