import type { FetchError } from 'ofetch'
import { reactive, watch } from 'vue' // Make sure to import these as necessary

export interface Form {
	[key: string]: any
}
export type Errors<FormType> = {
	[K in keyof FormType]?: string | string[];
}
export interface FormErrorResponse<FormType> {
	type: string
	title: string
	status: number
	errors: Errors<FormType>
	traceId: string
}

export const useFormHandler = <FormType extends Form>(form: FormType) => {
	const formKeys = Object.keys(form)

	const errors = reactive<Errors<FormType>>({})

	const { errorSnackbar } = useSnackbar()

	const snackbarDefaults: Partial<Parameters<typeof errorSnackbar>[0]> = {
		text: '',
		duration: 5000,
		// NOT Working
		// 'v-slot:actions': (props:any) => {
		//  console.log('🚀 ~ useFormHandler ~ props:', props)
		//  alert('props')
		//  return h('v-btn', {
		//    text: true,
		//    color: 'white',
		//    onClick: props.close,
		//  }, 'Close 3')
		// },
	}

	function errorsArrayHandler(errors: string[]) {
		errors.forEach((errorMsg) => {
			errorSnackbar({
				...snackbarDefaults as Record<string, any>,
				text: errorMsg,
			})
		})
	}

	function errorsObjectHandler(e: Errors<FormType>) {
		Object.assign(errors, e)

		Object.keys(e).forEach((key) => {
			const LKey = key.toString()// .toLowerCase()
			if (formKeys.includes(LKey)) {
				if ((e as Record<keyof FormType, any>)[key]) {
					(e as Record<keyof FormType, any>)[LKey as keyof FormType] = (e as Record<keyof FormType, any>)[LKey as keyof FormType]
				}
				const unwatch = watch(() => form[LKey], () => {
					const errs = e as unknown as Record<keyof FormType, any>

					// Delete the error with the correct type
					delete errs[key as keyof FormType]
					unwatch()
				})
			} else if (Array.isArray((e as Record<keyof FormType, any>)[key])) {
				((e as Record<keyof FormType, any>)[key] as string[]).forEach((errorMsg) => {
					errorSnackbar({
						...snackbarDefaults as Record<string, any>,
						text: errorMsg,
					})
				})
			} else {
				errorSnackbar({
					...snackbarDefaults as Record<string, any>,
					text: (e as Record<keyof FormType, any>)[key] as string,
				})
			}
		})
	}

	function handler(resp: FetchError<FormErrorResponse<FormType>>) {
		switch (resp.statusCode) {
			case 401:{
				if (resp.data?.errors) {
					if (Array.isArray(resp.data?.errors)) {
						errorsArrayHandler(resp.data.errors)

						if (!resp.data?.errors.length) {
							errorSnackbar({
								...snackbarDefaults as Record<string, any>,
								text: resp.data.title,
							})
						}
					}
				}
				break
			}
			case 400:
				if (resp.data?.errors) {
					if (typeof errors === 'object') {
						errorsObjectHandler(resp.data.errors)

						if (!Object.keys(resp.data.errors).length) {
							errorSnackbar({
								...snackbarDefaults as Record<string, any>,
								text: resp.data.title,
							})
						}
					}
				}
				break

			case 500:
				console.error('Server Error:', resp.data)
				errorSnackbar({
					...snackbarDefaults as Record<string, any>,
					text: 'A server error occurred, please try again later.',
				})
				break

			default:
				console.error('Unknown Error:', resp)
				errorSnackbar({
					...snackbarDefaults as Record<string, any>,
					text: resp.message || 'An unknown error occurred, please try again later.',
				})
				break
		}
	}

	return { errors, handler }
}
