module.exports = {
	extends: [
		'@commitlint/config-conventional',
	],
	rules: {
		// Disable the body-max-line-length rule for semantic-release
		'body-max-line-length': [0],
		'footer-max-line-length': [0],
		'type-enum': [
			2,
			'always',
			['feat', 'fix', 'docs', 'style', 'refactor', 'perf', 'test', 'chore', 'wip', 'pump'],
		],
		// Disable case checking for type
		'type-case': [0],
		// Disable case checking for scope
		'subject-case': [0],
		// If header max length is an issue, adjust this rule accordingly
		'header-max-length': [0], // Example: limit header length to 72 characters
	},
}
