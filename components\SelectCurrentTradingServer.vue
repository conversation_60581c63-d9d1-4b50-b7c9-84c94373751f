<template>
	<api-items
		v-slot="props"
		url="/my/trading-servers"
	>
		<v-list-item
			v-bind="{ ...$attrs, ...menuProps }"
			link
			rounded="lg"
			append-icon="i-mdi:chevron-down"
			class="mx-4"
		>
			<v-list-item-title class="py-2">
				<find-item-by-id
					:id="tradingServerName"
					v-slot="{ item }"
					item-key="name"
					:items="props.items"
				>
					{{ $t('SelectCurrentTradingServer.server-item-displayname', [item.displayName]) }}
				</find-item-by-id>
			</v-list-item-title>
			<!-- <v-list-item-subtitle>
				<find-item-by-id :id="tradingServerName" v-slot="{item}" item-key="name" :items="props.items">
					{{ item.ipAddress }}:{{ item.port }}
				</find-item-by-id>
			</v-list-item-subtitle> -->

			<v-menu
				v-model="menuModel"
				activator="parent"
				open-on-hover
				open-on-click
			>
				<v-list>
					<v-list-item
						v-for="item in props.items"
						:key="item.id"
						:title="item.displayName"
						:subtitle="`${item.ipAddress}:${item.port}`"
						link
						:active="tradingServerName === item.name"
						@click="tradingServerName = item.name"
					/>
				</v-list>
			</v-menu>
		</v-list-item>
	</api-items>
</template>

<script lang="ts" setup>
const prefStore = usePreferencesStore()

const tradingServerName = computed({
	get: () => prefStore.getKey('tradingServerName'),
	set: value => prefStore.updateKey('tradingServerName', value),
})

const menuModel = ref(false)

const { props: menuProps } = useKeepMenuVisible(menuModel)
</script>
