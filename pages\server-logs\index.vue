<template>
	<page title="Server Logs">
		<datatable
			ref="table"
			url="/server-log"
			:headers="headers"
			search-key="message"
			:default-model="{ fromDate: null, toDate: null, level: null }"
			lazyload
			hide-default-footer
			item-value="message"
		>
			<template #filter="{ model }">
				<v-text-field
					v-model="model.exception"
					label="Exception"
					clearable
				/>
				<date-time-input
					v-model="model.fromDate"
					:max="model.toDate || now"
					:label="$t('journals.from')"
					clearable
					:time-picker-props="{
						ampmInTitle: true,
					}"
				/>
				<date-time-input
					v-model="model.toDate"
					:label="$t('journals.to')"
					:min="model.fromDate"
					:max="now"
					clearable
					:time-picker-props="{
						ampmInTitle: true,
					}"
				/>

				<v-select
					v-model="model.level"
					:items="[0, 1, 2, 3, 4, 5]"

					label="Level"
				>
					<template #selection="{ item }">
						<server-logs-level
							size="x-small"
							:level="item.value"
						/>
					</template>
					<template #item="{ props: selectProps, item }">
						<v-list-item
							v-bind="selectProps"
						>
							<template #title>
								<server-logs-level :level="item.value" />
							</template>
						</v-list-item>
					</template>
				</v-select>
			</template>

			<template #item.level="{ value }">
				<server-logs-level :level="value" />
			</template>

			<template #item.data-table-expand="{ internalItem, isExpanded, toggleExpand }">
				<v-btn
					v-if="internalItem.raw.exception"
					:append-icon="isExpanded(internalItem) ? 'i-mdi-chevron-up' : 'i-mdi-chevron-down'"
					:text="isExpanded(internalItem) ? 'Hide Exception' : 'Show Exception'"
					class="text-none"
					color="medium-emphasis"
					size="small"
					variant="text"
					border
					slim
					@click="toggleExpand(internalItem)"
				/>
			</template>
			<template #expanded-row="{ item, columns }">
				<tr>
					<td
						:colspan="columns.length"
						class="py-2"
					>
						<div
							class="text-caption bg-grey-lighten-3 overflow-x-auto rounded mb-2 pa-2"
							style="max-width: 100%; white-space: break-spaces"
						>
							{{ item.exception }}
						</div>
					</td>
				</tr>
			</template>

			<template #item.timestamp="{ value }">
				{{ dayjs(value).format(DateFormat.App) }}
			</template>
			<!-- <template #item.actions="{item}">
				<v-btn v-if="can('users.edit')" icon="i-mdi:account-eye" variant="text" @click="crud?.update({id:item.userId})" />
			</template> -->
		</datatable>
		<users-crud ref="crud" />
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type UsersCrud from '~/components/UsersCrud.vue'
import { DateFormat } from '~/types/DateFormat'

definePageMeta({
	permission: 'journals.view',
})

const crud = ref<typeof UsersCrud | null>(null)

const dayjs = useDayjs()

const now = useNow()

const table = ref<typeof Datatable | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
		width: 20,
	},

	{
		title: 'Message',
		value: 'message',
		maxWidth: '500px',
	},
	{
		title: 'Level',
		value: 'level',
		align: 'center',
	},
	{
		title: '',
		value: 'data-table-expand',
	},
	{
		title: 'Date',
		value: 'timestamp',
		nowrap: true,
	},
	// {
	// 	// title: 'Actions',
	// 	value: 'actions',
	// },

]
</script>
