import type { ModuleOptions } from '@sentry/nuxt/module'

const config: ModuleOptions = {
	sourceMapsUploadOptions: {
		org: process.env.NUXT_PUBLIC_SENTRY_ORG,
		project: process.env.NUXT_PUBLIC_SENTRY_PROJECT,
		authToken: process.env.NUXT_PUBLIC_SENTRY_AUTH_TOKEN,
		telemetry: false,
	},
	unstable_sentryBundlerPluginOptions: {
		disable: !process.env.NUXT_PUBLIC_SENTRY_ENVIRONMENT,
	},
}

export default config
