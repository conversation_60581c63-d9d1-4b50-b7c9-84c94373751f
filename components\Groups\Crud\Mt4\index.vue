<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Groups.MT4.Crud.group')"
		item-identity="group"
		:navigation-drawer-props="{ width: 700 }"
		url="/groups/mt4/:group"
		:validation-callback="validate"
		update-method="POST"
		update-url="/groups/mt4"
		v-bind="$attrs"
		@closed="closeHandler"
		@loaded="loadedGroupName = $event.group"
	>
		<template #title="{ isUpdateAction, item }">
			<div v-if="isUpdateAction && item.group !== loadedGroupName">
				{{ $t('Groups.MT4.Crud.create-group', [item.group]) }}
			</div>
			<div v-else-if="isUpdateAction">
				{{ $t('Groups.MT4.Crud.update-group', [item.group]) }}
			</div>
		</template>
		<template #default="{ item, errors }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-3 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class="flex-grow-1"
						>
							<v-form
								v-for="tab in tabs"
								ref="tabForm"
								:key="tab.value"
								v-model="tab.isValid.value"
							>
								<v-tabs-window-item
									:value="tab.value"
									:eager="isValidated"
								>
									<component
										:is="tab.component"
										:loaded-group-name="loadedGroupName"
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-tabs-window-item>
							</v-form>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Common from './Common.vue'
import Permissions from './Permissions.vue'
import Archiving from './Archiving.vue'
import Margins from './Margins.vue'
import Securities from './Securities.vue'
import Symbols from './Symbols.vue'
import Reports from './Reports.vue'
import type { DefaultItem } from '~/components/Crud.vue'
import Crud from '~/components/Crud.vue'
import type { Groups } from '~/types'

const loadedGroupName = ref('')

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultSecurityGroup: Groups.MT4.GroupSec = {
	show: 1,
	trade: 1,
	execution: 1,
	commBase: 0,
	commType: 1,
	commLots: 0,
	commAgent: 0,
	commAgentType: 1,
	spreadDiff: 0,
	lotMin: 0,
	lotMax: 100000,
	lotStep: 10,
	ieDeviation: 2,
	confirmation: 0,
	tradeRights: 0,
	ieQuickMode: 0,
	autoCloseoutMode: 0,
	commTax: 0,
	commAgentLots: 0,
	freeMarginMode: 0,
	reserved: [
		0,
		0,
		0,
	],
}

const defaultItem: DefaultItem< Groups.MT4._Id.POSTRequest | Groups.MT4._Id.PUTRequest> = {
	group: '',
	enable: 1,
	timeout: 7,
	otpMode: 0,
	company: 'Ingot Brokers (Australia) Pty Ltd.',
	signature: '',
	supportPage: '',
	smtpServer: '',
	smtpLogin: '',
	smtpPassword: '',
	supportEmail: '',
	templatesPath: '',
	copies: 0,
	reports: 0,
	defaultLeverage: 0,
	defaultDeposit: 0,
	maxSecurities: 4096,
	securityGroups: Array(32).fill(defaultSecurityGroup),
	specialSecuritiesSettings: [],
	securitiesSettingsCount: 0,
	currency: 'USD',
	credit: 0,
	marginCallLevel: 50,
	marginMode: 1,
	marginStopOut: 30,
	interestRate: 0,
	useSwap: 1,
	news: 1,
	rights: 31,
	checkIEPrices: 1,
	maxPositions: 0,
	closeReopen: 0,
	hedgeProhibited: 0,
	closeFIFO: 0,
	hedgeLargeLeg: 0,
	unusedRights: [
		0,
		0,
	],
	securitiesHash: '',
	marginType: 0,
	archivePeriod: 0,
	archiveMaxBalance: 0,
	stopoutSkipHedged: 0,
	archivePendingPeriod: 0,
	newsLanguages: [
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
	],
	newsLanguagesTotal: 0,
	reserved: [
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
		0,
	],
} satisfies DefaultItem<Groups.MT4._Id.POSTRequest | Groups.MT4._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs: Tab[] = ([
	{
		value: 'common',
		text: t('Groups.MT4.Crud.common'),
		component: Common,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[0]?.errors.length || 0),
	},
	{
		value: 'permissions',
		text: t('Groups.MT4.Crud.permissions'),
		component: Permissions,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[1]?.errors.length || 0),
	},
	{
		value: 'archiving',
		text: t('Groups.MT4.Crud.archiving'),
		component: Archiving,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[2]?.errors.length || 0),
	},
	{
		value: 'margins',
		text: t('Groups.MT4.Crud.margins'),
		component: Margins,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[3]?.errors.length || 0),
	},
	{
		value: 'securities',
		text: t('Groups.MT4.Crud.securities'),
		component: Securities,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[4]?.errors.length || 0),
	},
	{
		value: 'symbols',
		text: t('Groups.MT4.Crud.symbols'),
		component: Symbols,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[5]?.errors.length || 0),
	},
	{
		value: 'reports',
		text: t('Groups.MT4.Crud.reports'),
		component: Reports,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[6]?.errors.length || 0),
	},
])

const tabModel = ref(tabs[0].value)

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error(t('Groups.MT4.Crud.please-fix-the-form-errors')))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs[0].value
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: () => crudRef.value!.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
