<template>
	<teleport
		v-if="!p.isPreview"
		defer
		:to="`#widget-wrapper-${i} .tools .data`"
	>
		<v-btn
			icon="i-mdi-cog"
			variant="text"
			size="small"
			color="accent"
			@click="dataDialogModel = true"
		/>
	</teleport>
	<v-card
		ref="card"
		height="100%"
		flat
		border
		class="d-flex flex-column"
	>
		<v-card-title class="d-flex align-start">
			<div>
				<!-- <div class="text-subtitle-1">
					Current Rate
				</div> -->
				<div class="text-subtitle-1">
					<span>{{ computedData?.symbol }}</span> | <span>MT5 Real</span>
				</div>
				<div class="text-caption">
					Dec 24 2024 10:30 am
				</div>
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text class="d-flex flex-column flex-grow-1 justify-space-between">
			<v-list-item
				title="High"
				class="bg-background mb-1"
				density="compact"
			>
				<template #append>
					<span class="text-success">2730.88</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Low"
				class="bg-background mb-1"
			>
				<template #append>
					<span class="text-error">2730.47</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Spread"
				class="bg-background mb-1"
			>
				<template #append>
					<span>36</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Range"
				class="bg-background mb-1"
			>
				<template #append>
					<span>2739.59 - 2739.95</span>
				</template>
			</v-list-item>
		</v-card-text>
		<v-dialog
			v-model="dataDialogModel"
			max-width="400"
		>
			<v-confirm-edit
				v-slot="{ model, actions }"
				:model-value="data"
				cancel-text="Reset"
				@save="saveHandler"
			>
				<v-form ref="dataFormRef">
					<v-card>
						<v-card-title>
							<div class="text-h6">
								Settings
							</div>
						</v-card-title>
						<v-card-text>
							<v-text-field
								v-model="model.value.symbol"
								label="Symbol"
							/>
						</v-card-text>
						<v-card-actions>
							<v-btn
								color="primary"
								variant="text"
								@click="dataDialogModel = false"
							>
								Cancel
							</v-btn>

							<v-spacer />
							<component :is="actions" />
						</v-card-actions>
					</v-card>
				</v-form>
			</v-confirm-edit>
		</v-dialog>
	</v-card>
</template>

<script lang="ts" setup>
import type { VCard, VForm } from 'vuetify/components'
import type { WidgetItemProps } from '../../types'
import type { Data } from './settings'
import settings from './settings'

type Props = WidgetItemProps & {
	data: Data
}

type Emit = {
	(event: 'update:data', value: Data): void
}

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), settings.data ?? {})

const dataDialogModel = ref(false)

const dataFormRef = ref<InstanceType<typeof VForm>>()

const card = ref<InstanceType<typeof VCard> | null>(null)

const cardWidth = ref(170)

const widthHandler = () => {
	if (card.value) {
		cardWidth.value = card.value.$el.clientWidth
	}
}

useResizeObserver(card, widthHandler)

const _isLoading = ref(false)

const previewData = ref<Data>({
	symbol: 'EUR/USD',
})

const computedData = computed<Data>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return p.data
})

watch(() => p.isPreview, (isPreview) => {
	console.log('isPreview', isPreview, p.i)
})

onMounted(() => {
	if (!p.isPreview && p.isLayoutMounted) {
		dataDialogModel.value = true
	}
})

const saveHandler = async (data: Data) => {
	const validation = await dataFormRef.value?.validate()
	if (!validation?.valid) {
		return
	}

	dataDialogModel.value = false

	dataFormRef.value?.resetValidation()

	emit('update:data', data)
}
</script>
