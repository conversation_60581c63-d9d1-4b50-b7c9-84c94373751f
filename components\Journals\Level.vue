<template>
	<v-chip
		rounded
		:color="color"
		variant="tonal"
	>
		{{ text }}
	</v-chip>
</template>

<script lang="ts" setup>
import { Journals } from '~/types'

const model = defineModel<Journals.Level>()

const text = computed(() => {
	switch (model.value) {
		case Journals.Level.Information:
			return 'Info'
		case Journals.Level.Error:
			return 'Error'
		case Journals.Level.Critical:
			return 'Critical'
		case Journals.Level.Debug:
			return 'Debug'
		case Journals.Level.Warning:
			return 'Warning'
		case Journals.Level.None:
			return 'None'
		case Journals.Level.Trace:
			return 'Trace'
		default:
			return 'Unknown'
	}
})

const color = computed(() => {
	switch (model.value) {
		case Journals.Level.Information:
			return 'info'
		case Journals.Level.Error:
			return 'error'
		case Journals.Level.Critical:
			return 'red'
		case Journals.Level.Debug:
			return 'blue'
		case Journals.Level.Warning:
			return 'warning'
		case Journals.Level.None:
		default:
			return 'grey'
	}
})
</script>
