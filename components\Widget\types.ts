import type Widgets from './item'
import type { Permissions } from '~/types/Permissions'

export type LayoutItem<T = any> = {
	x: number
	y: number
	w: number
	h: number
	i: string
	c: keyof typeof Widgets
	isPreview: boolean
	moved?: boolean
	data?: T
}

export interface WidgetItemSettings<T = undefined> {
	dimensions: {
		w: number
		h: number
		minW: number | undefined
		minH: number | undefined
		maxW: number | undefined
		maxH: number | undefined
		resizable: boolean
		preserveAspectRatio: boolean
	}

	data?: T

	permission?: Permissions.Names | undefined
}

export function EnsureImplementation<U extends WidgetItemSettings>(constructor: U) {
	return constructor
}

export type WidgetItemProps = {
	i: string
	name: string
	isPreview: boolean
	isLayoutMounted: boolean
}
