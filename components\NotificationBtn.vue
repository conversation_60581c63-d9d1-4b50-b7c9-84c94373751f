<template>
	<v-btn
		icon
		disabled
	>
		<v-badge
			color="red"
			bordered
			:model-value="!!data.length"
			dot
		>
			<v-icon
				:icon="btnIcon"
				size="large"
			/>
		</v-badge>
		<v-menu
			activator="parent"
			:close-on-content-click="false"
		>
			<v-card>
				<v-tabs
					v-model="tabModel"
					grow
				>
					<v-tab
						value="unread"
						tile
						rounded="0"
					>
						Unread
					</v-tab>

					<v-tab
						value="read"
						tile
						rounded="0"
					>
						Read
					</v-tab>
				</v-tabs>

				<v-banner
					v-if="!isNotificationAllowed"
					color="warning"
					icon="i-mdi:bell-off"
				>
					<template #text>
						<div class="text-subtitle-1 font-weight-bold">
							Your Push Notifications Are Off
						</div>
						<p class="text-body-1 text-medium-emphasis">
							Turn on notifications to stay connected
						</p>
					</template>
					<template #actions>
						<v-btn
							color="error"
							:loading="isAllowing"
							@click="grantNotificationPermission"
						>
							Allow Notifications
						</v-btn>
					</template>
				</v-banner>

				<v-tabs-window v-model="tabModel">
					<v-tabs-window-item value="unread">
						<v-list
							slim
							lines="two"
							max-height="500"
						>
							<v-list-item
								v-for="x in 10"
								:key="x"
								:active="false"
								prepend-icon="i-mdi:swap-vertical"
								title="Pending Transaction"
								subtitle="4 Pending transactions can be processed now"
								:to="{ name: 'transactions' }"
							/>
						</v-list>
					</v-tabs-window-item>
					<v-tabs-window-item value="read">
						<v-list
							slim
							lines="two"
							max-height="500"
						>
							<v-list-item
								v-for="x in 3"
								:key="x"
								prepend-icon="i-mdi:swap-vertical"
								:active="false"
								title="Pending Transaction"
								subtitle="4 Pending transactions can be processed now"
								:to="{ name: 'transactions' }"
							/>
						</v-list>
					</v-tabs-window-item>
				</v-tabs-window>

				<v-btn
					height="48"
					block
					variant="text"
					rounded="0"
				>
					Read More
				</v-btn>
			</v-card>
		</v-menu>
	</v-btn>
</template>

<script lang="ts" setup>
const { on } = useGlobalEvents()

const data = ref([{}])

const tabModel = ref<'read' | 'unread'>('unread')

const isAllowing = ref(false)

const isNotificationAllowed = computed(() => 'Notification' in window && Notification.permission === 'granted')

const btnIcon = computed(() => isNotificationAllowed.value ? 'i-material-symbols-light:notifications-outline' : 'i-material-symbols-light:notifications-off-outline-sharp')

const grantNotificationPermission = async () => {
	if ('Notification' in window) {
		try {
			isAllowing.value = true
			await Notification.requestPermission()
			isAllowing.value = false
		} catch (e) {
			console.error(e)
		}
	}
}

const startListening = () => {
	on('notification:received', () => {
		// refresh data
	})
}

onMounted(startListening)
</script>
