<template>
	<ClientOnly>
		<teleport to="#main-layout">
			<v-navigation-drawer
				v-model="drawerModel"
				tag="div"
				location="end"
				temporary
				class="crud"
				v-bind="computedNavigationDrawerProps"

				@update:model-value="navigationDrawerUpdateHandler"
			>
				<template #prepend>
					<v-card-title>
						<v-skeleton-loader
							:loading="isGetting"
							type="heading"
						>
							<slot
								v-bind="$titleSlot"
								name="title"
							>
								{{ title }}
							</slot>
							<v-spacer />
							<slot
								name="title-actions"
								v-bind="$titleSlot"
							>
								<v-btn
									variant="plain"
									:icon="closeIcon"
									@click="close()"
								/>
							</slot>
						</v-skeleton-loader>
					</v-card-title>
				</template>
				<v-card-text class="pt-0">
					<v-skeleton-loader
						type="paragraph@3"
						:loading="isGetting"
					>
						<slot
							v-if="hasGettingError"
							name="error"
							v-bind="{ error: hasGettingError }"
						>
							<v-banner
								stacked
								lines="three"
							>
								<template #prepend>
									<v-icon
										size="x-large"
										color="error"
									>
										i-mdi:alert-circle-outline
									</v-icon>
								</template>
								<div>
									<div><b>{{ t('crud.error-while-getting') }}</b></div>
									{{ hasGettingError }}
								</div>
								<template #actions>
									<v-btn
										variant="plain"
										@click="close()"
									>
										{{ t('crud.cancel') }}
									</v-btn>
									<v-btn
										variant="text"
										color="primary"
										:loading="isGetting"
										@click="openForUpdate(currentURLVars)"
									>
										{{ t('crud.retry') }}
									</v-btn>
								</template>
							</v-banner>
						</slot>
						<v-form
							v-else
							ref="formRef"
							v-model="isMainFormValid"
							class="d-flex flex-column flex-grow-1"
							:disabled="$actionsSlot.isSaving"
						>
							<v-lazy>
								<div>
									<v-defaults-provider

										:defaults="{
											// VField: {
											// 	variant: 'solo',
											// 	flat:true
											// }, not working
											VTextField: {
												// variant: 'solo',
												// flat:true
											},
										}"
									>
										<slot v-bind="$defaultSlot" />
									</v-defaults-provider>
								</div>
							</v-lazy>
						</v-form>
					</v-skeleton-loader>
				</v-card-text>

				<template #append>
					<v-skeleton-loader
						:loading="isGetting"
						type="actions"
					>
						<v-card-actions class="flex-grow-1">
							<slot
								v-if="!hasGettingError"
								name="actions"
								v-bind="$actionsSlot"
							>
								<v-btn
									variant="text"
									color="secondary"
									@click="close()"
								>
									{{ t('crud.cancel') }}
								</v-btn>
								<v-spacer />
								<v-btn
									:disabled="!isFormDirty"
									@click="resetData"
								>
									{{ t('crud.reset') }}
								</v-btn>
								<v-badge
									dot
									color="warning"
									:model-value="isFormDirty"
								>
									<v-btn
										variant="text"
										color="primary"
										:loading="$actionsSlot.isSaving"
										@click="save"
									>
										{{ t('crud.save') }}
									</v-btn>
								</v-badge>
							</slot>
						</v-card-actions>
					</v-skeleton-loader>
				</template>
			</v-navigation-drawer>
		</teleport>
	</ClientOnly>
</template>

<script  lang="ts" setup>
import { VNavigationDrawer, VForm, VCardActions, VCardText, VCardTitle, VBtn, VSpacer, VSkeletonLoader, VBanner } from 'vuetify/components'
import { normalizeURL } from 'ufo'
import dot from 'dot-object'
import type { Nullable } from '~/types/Helpers'

export type ItemErrors<T> = {
	[K in keyof T]?: string | string[];
}

export type DefaultItem<T> = Nullable<T>

export type Item<T> = T

type FeedbackPayload = Parameters<typeof successSnackbar>[0]

const { t } = useI18n()

const { $api } = useNuxtApp()

const { $confirm } = useNuxtApp()

const { successSnackbar, errorSnackbar } = useSnackbar()

export type URLVars = Record<string, string | number>

type Props = {
	navigationDrawerProps?: Partial<InstanceType<typeof VNavigationDrawer>['$props']>
	skeletonLoaderProps?: Partial<InstanceType<typeof VSkeletonLoader>['$props']>
	itemName: string
	itemIdentity?: string
	handler?: typeof useFetch
	createMapper?: (item: any) => any
	updateMapper?: (item: any) => any
	defaultItem: any
	closeIcon?: string
	disableFeedback?: boolean
	validationCallback?: () => Promise<true>
	url: string | false | (() => string | false)
	showUrl?: string | false | (() => string | false)
	createUrl?: string | false | (() => string | false)
	updateUrl?: string | false | (() => string | false)
	deleteUrl?: string | false | (() => string | false)
	getMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	createMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	updateMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	deleteMethod?: 'GET' | 'POST' | 'PUT' | 'DELETE'
}

const p = withDefaults(
	defineProps<Props>(), {
		navigationDrawerProps: () => ({
			width: 500,
			temporary: true,
			// absolute: true,
			style: {
			// zIndex: 10000,
			},
			order: 0,
		}),
		skeletonLoaderProps: () => ({}),
		itemName: '',
		itemIdentity: '',
		handler: () => useFetch, // TODO: causes extra request on page load http://localhost:3000/en/[object%20Object]
		createMapper: (item: any) => item,
		updateMapper: (item: any) => item,
		defaultItem: {},
		closeIcon: 'i-mdi:close',
		disableFeedback: false,
		validationCallback: undefined,
		url: '',
		showUrl: '',
		createUrl: '',
		updateUrl: '',
		deleteUrl: '',
		getMethod: 'GET',
		createMethod: 'POST',
		updateMethod: 'PUT',
		deleteMethod: 'DELETE',
	})

// const snackbar = useSnackbar()

const dataSnapshot = ref<any>({})

const { errors: formErrors, handler: formHandler } = useFormHandler(p.defaultItem)

const formRef = ref<InstanceType<typeof VForm>>()

const isMainFormValid = ref<boolean>(false)

const isGetting = ref(false)

const title = computed(() => {
	const itemIdentity: string = dot.pick(p.itemIdentity, $defaultSlot.item)

	const itemName = p.itemName

	return isCreateAction.value ? t('crud.create-item', { itemName }) : itemIdentity ? t('crud.update-item-identity', { itemName, itemIdentity }) : t('crud.update-item', { itemName })
})

const lastSavedResponse = ref<any>({})

const hasGettingError = ref<false | string>(false)

const defaultNavigationDrawerProps: InstanceType<typeof VNavigationDrawer>['$props'] = {
	width: 500,
	temporary: true,
	// absolute: true,
	style: {
		// zIndex: 10000,
	},
	order: 0,
}

const computedNavigationDrawerProps = computed(() => {
	return {
		...defaultNavigationDrawerProps,
		// persistent: isFormDirty.value,
		...p.navigationDrawerProps,
	}
})

const getURLAddressByMethod = (method: 'get' | 'post' | 'put' | 'delete'): string | false => {
	let mappedMethod: keyof Props = 'showUrl' // default

	switch (method) {
		case 'get':
			mappedMethod = 'showUrl'
			break
		case 'post':
			mappedMethod = 'createUrl'
			break
		case 'put':
			mappedMethod = 'updateUrl'
			break
		case 'delete':
			mappedMethod = 'deleteUrl'
			break
		default:
			break
	}

	if (typeof p[mappedMethod] === 'function') {
		const methodFn = p[mappedMethod] as () => string | false // Type assertion to specify the correct function signature
		let url = methodFn()
		if (typeof url === 'string') {
			url = normalizeURL(url)
		}
		return url
	}

	if (typeof p[mappedMethod] === 'string' && p[mappedMethod]) {
		const methodStr = p[mappedMethod] as string
		return normalizeURL(methodStr)
	}

	if (typeof p.url === 'function') {
		let url = p.url()
		if (typeof url === 'string') {
			url = normalizeURL(url)
		}
		return url
	}

	if (typeof p.url === 'string' && p.url) {
		return normalizeURL(p.url)
	}
	return false
}

const parseURL = (method: Parameters<typeof getURLAddressByMethod>[0], URLVars: URLVars = {}): false | string => {
	let url = getURLAddressByMethod(method)

	if (!url) {
		return false
	}

	const vars = Object.keys(URLVars) || []

	if (vars.length) {
		return vars.reduce((acc, key) => {
			const encodedValue = encodeURIComponent(String(URLVars[key]))
			return acc.replace(`:${key}`, encodedValue)
		}, url)
	}

	// remove any words that starts with :
	url = url.replace(/\/:[^/]+/g, '')

	return url
}

export type CrudEventData = {
	payload: any
	response: any
	url: ReturnType<typeof parseURL>
}

export type Emit = {
	(e: 'closed'): void
	(e: 'opened'): void
	(e: 'updated', data: CrudEventData): void
	(e: 'created', data: CrudEventData): void
	(e: 'deleted', data: CrudEventData): void
	(e: 'loaded', data: any): void
	(e: 'error', data: any): void
	(e: 'feedback', data: FeedbackPayload): void
	(e: 'reset'): void
}

const emit = defineEmits<Emit>()

const action = ref<'create' | 'update'>('create')

const isCreateAction = computed(() => {
	return action.value === 'create'
})

const isUpdateAction = computed(() => {
	return action.value === 'update'
})

const isDeleting = ref(false)

const isCreating = computed(() => {
	return isCreateAction.value && $actionsSlot.isSaving
})

const isUpdating = computed(() => {
	return isUpdateAction.value && $actionsSlot.isSaving
})

const isFormDirty = computed(() => {
	// if (isUpdateAction.value) {
	// 	return JSON.stringify((lastSavedResponse.value)) !== JSON.stringify(($defaultSlot.item))
	// } else {
	// 	const { cloned } = useCloned($defaultSlot.item, { deep: true })

	// 	// console.log('isFormDirty',
	// 	// 	JSON.stringify((p.defaultItem)) !== JSON.stringify(toRaw(cloned.value)),

	// 	// 	JSON.stringify((p.defaultItem)),
	// 	// 	JSON.stringify(toRaw(cloned.value)))

	// 	return JSON.stringify((p.defaultItem)) !== JSON.stringify(toRaw(cloned.value))
	// }
	return !hasGettingError.value && !isEqual(dataSnapshot.value, $defaultSlot.item)
})

const drawerModel = ref(false)

const currentURLVars = ref<URLVars>({})

const navigationDrawerUpdateHandler = (val: boolean) => {
	if (val === false) {
		close()
	}
}

const reset = () => {
	currentURLVars.value = {}
	hasGettingError.value = false
	isGetting.value = false
	formRef.value?.reset()
	$defaultSlot.item = { ...p.defaultItem }
	saveSnapshot($defaultSlot.item)
	formRef.value?.resetValidation()
	// remove all api errors
	Object.keys(formErrors).forEach((key) => {
		formErrors[key] = undefined
		delete formErrors[key]
	})
}

const resetData = () => {
	$confirm(t('crud.are-you-sure-you-want-to-reset-the-form')).then(() => {
		$defaultSlot.item = useCloned(dataSnapshot.value, { deep: true }).cloned.value
		formRef.value?.reset()
		emit('reset')
	}).catch(() => {})
}

const saveSnapshot = (data: any) => {
	dataSnapshot.value = toRaw(useCloned(data, { deep: true }).cloned.value)
}

export type OpenOptions = {
	override?: DefaultItem<any> | undefined
}

const openForCreate = (urlVars: URLVars = {}, options?: OpenOptions) => {
	currentURLVars.value = urlVars
	action.value = 'create'
	$defaultSlot.item = { ...p.defaultItem, ...(options?.override || {}) }
	saveSnapshot($defaultSlot.item)
	drawerModel.value = true
	emit('opened')
}

const openForUpdate = (urlVars: URLVars = {}, options?: OpenOptions) => {
	currentURLVars.value = urlVars
	action.value = 'update'
	$defaultSlot.item = { ...p.defaultItem, ...(options?.override || {}) }
	drawerModel.value = true
	const url = parseURL('get', urlVars)
	if (url) {
		isGetting.value = true
		$api(url, {
			method: p.getMethod,
		})
			.then((data) => {
				isGetting.value = false
				const { cloned } = useCloned(data, { deep: true }) // for comparison
				lastSavedResponse.value = toRaw(cloned.value)
				$defaultSlot.item = data
				saveSnapshot(data)
				hasGettingError.value = false
				emit('loaded', data)
			})
			.catch((error) => {
				hasGettingError.value = error.message || error.value?.toString() || t('crud.unknown-error')
				emit('error', error.value)
			})
			.finally(() => {
				isGetting.value = false
			})
	}

	emit('opened')
}

const del = async (urlVars: URLVars = {}) => {
	const itemIdentity = dot.pick(p.itemIdentity, urlVars)
	const hasIdentity = !!itemIdentity

	const text = !hasIdentity ? t('Crud.are-you-sure-you-want-to-delete', { itemName: p.itemName, itemIdentity }) : t('Crud.are-you-sure-you-want-to-delete-identity', { itemName: p.itemName, itemIdentity })
	await $confirm({ text, async: true })

		.then(async ({ hide, loading }) => {
			loading.value = true
			const url = parseURL('delete', urlVars)

			if (url) {
				isDeleting.value = true
				await $api(url, {
					method: p.deleteMethod,
				})
					.then((data) => {
						hide()
						emit('deleted', {
							payload: false,
							response: data,
							url,
						})
						feedback({
							text: t('crud.item-deleted', { name: p.itemName }),
						}, 'success')
						isDeleting.value = false
					})
					.catch((error) => {
						emit('error', error)
						formHandler(error)
					})
					.finally(() => {
						isDeleting.value = false
						loading.value = false
					})
			}
		})
		.catch(() => {})
}

const save = async () => {
	try {
		if (p.validationCallback) {
			// eslint-disable-next-line @typescript-eslint/no-unused-vars
			const result = await p.validationCallback()
		}

		const validationResult = await formRef.value?.validate()
		if (!validationResult?.valid) {
			return
		}

		const method = isCreateAction.value ? p.createMethod : p.updateMethod
		const mapper = isCreateAction.value ? p.createMapper : p.updateMapper
		const { cloned: clonedDefaultSlotItem } = useCloned($defaultSlot.item, { deep: true })
		const body = mapper(toRaw(clonedDefaultSlotItem.value))
		$actionsSlot.isSaving = true

		const url = (isCreateAction.value ? parseURL('post', currentURLVars.value) : parseURL('put', currentURLVars.value)) as string | false

		if (url) {
			$api(url, {
				method,
				body,
			})
				.then((data) => {
					lastSavedResponse.value = clonedDefaultSlotItem.value

					const event = isCreateAction.value ? 'created' : 'updated' as keyof Emit
					emit(event, {
						payload: body,
						response: data,
						url,
					})
					feedback({
						text: isCreateAction.value ? t('crud.item-created', { name: p.itemName }) : t('crud.item-updated', { name: p.itemName }),
					}, 'success')
					close(true)

					$actionsSlot.isSaving = false
				})
				.catch((error) => {
					emit('error', error)
					formHandler(error)
				})
				.finally(() => {
					$actionsSlot.isSaving = false
				})
		}
	} catch (error) {
		// If validationCallback returned a rejected promise, handle it here.
		console.error('Validation failed', error)
		// Optionally, you can show an error message or perform any other action.
	}
}

const close = (force: boolean = false) => {
	const _closeProcedure = () => {
		drawerModel.value = false
		reset()
		window.onbeforeunload = null
		emit('closed')
	}

	if (force || !isFormDirty.value) {
		_closeProcedure()
	} else {
		drawerModel.value = true // make sure it keeps open
		$confirm(t('Crud.you-have-unsaved-changes-are-you-sure-you-want-to-')).then(_closeProcedure).catch(() => {})
	}
	// if (isFormDirty.value) {
	// 	drawerModel.value = true // make sure it keeps open
	// 	$confirm('You have unsaved changes. Are you sure you want to close?').then(_closeProcedure)
	// } else {
	// 	_closeProcedure()
	// }
}

const feedback = (payload: FeedbackPayload, type: 'success' | 'error') => {
	if (p.disableFeedback) {
		return
	}
	switch (type) {
		case 'success':
			successSnackbar(payload)
			break
		case 'error':
			errorSnackbar(payload)
			break
		default:
			break
	}
	emit('feedback', payload)
}

const isOpen = computed(() => {
	return drawerModel.value
})

const $defaultSlot = reactive({
	item: { ...p.defaultItem } as any,
	errors: formErrors,
	isCreateAction,
	isUpdateAction,
})

const $actionsSlot = reactive({
	close,
	save,
	isSaving: false,
	item: computed(() => ($defaultSlot.item)),
})

const $titleSlot = reactive({
	itemName: p.itemName,
	isCreateAction,
	isUpdateAction,
	item: computed(() => ($defaultSlot.item)),
	close,
	save,
})

const getItem = () => {
	return toRaw($defaultSlot.item)
}

const setItem = (item: any) => {
	$defaultSlot.item = item
}

watch(isFormDirty, (dirty) => {
	// prevent closing the browser window if there are unsaved changes
	if (dirty) {
		window.onbeforeunload = () => true
	} else {
		window.onbeforeunload = null
	}
})

type Exposed = {
	create: typeof openForCreate
	update: typeof openForUpdate
	delete: typeof del
	close: typeof close
	isOpen: Ref<boolean>
	isDeleting: Ref<boolean>
	isCreating: Ref<boolean>
	isUpdating: Ref<boolean>
	getItem: typeof getItem
	setItem: typeof setItem
	isCreateAction: Ref<boolean>
	isUpdateAction: Ref<boolean>
	$defaultSlot: typeof $defaultSlot
	formRef: Ref<InstanceType<typeof VForm> | undefined>
}

defineExpose<Exposed>({
	create: openForCreate,
	update: openForUpdate,
	delete: del,
	close,
	isOpen,
	isDeleting,
	isCreating,
	isUpdating,
	getItem,
	setItem,
	isCreateAction,
	isUpdateAction,
	$defaultSlot,
	formRef,
})

type Slots = {
	'default'(props: typeof $defaultSlot): any
	'actions'(props: typeof $actionsSlot): any
	'title'(props: typeof $titleSlot): any
	'title-actions'(props: typeof $titleSlot): any
	'error'(props: { error: string }): any
}

defineSlots<Slots>()

// add event listener when drawer is open and esc pressed close drawer
watch(isOpen, (val) => {
	if (val) {
		window.addEventListener('keydown', escListener)
	} else {
		window.removeEventListener('keydown', escListener)
	}
})

const escListener = (e: KeyboardEvent) => {
	if (e.key === 'Escape') {
		close()
	}
}

onUnmounted(() => {
	window.removeEventListener('keydown', escListener)

	window.onbeforeunload = null
})
</script>
