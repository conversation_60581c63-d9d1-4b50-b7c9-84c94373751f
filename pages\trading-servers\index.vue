<template>
	<div>
		<page :title="$t('trading-servers.trading-servers')">
			<template #actions>
				<v-btn
					v-if="can('tradingServers.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('trading-servers.create-server') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/trading-servers"
				:headers="headers"
				search-key="name"
			>
				<template #item.integrationType="{ value }">
					<span v-if="value === TradingServers.IntegrationType.MANAGER_ACCOUNT">{{ $t('trading-servers.manager-account') }}</span>
					<span v-else>{{ $t('trading-servers.rest-api') }}</span>
				</template>
				<template #item.isActive="{ value }">
					<is-active :model-value="value" />
				</template>
				<template #item.address="{ item }">
					<span v-if="item.integrationType === TradingServers.IntegrationType.MANAGER_ACCOUNT">{{ item.ipAddress }}:{{ item.port }}</span>
					<span v-else>{{ item.apiUrl }}</span>
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('tradingServers.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('tradingServers.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<trading-servers-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type TradingServersCrud from '~/components/TradingServers/Crud.vue'
import { TradingServers } from '~/types'

definePageMeta({
	permission: 'tradingServers.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof TradingServersCrud | null>(null)

const headers = computed<Headers>(() => [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('trading-servers.display-name'),
		value: 'displayName',
	},
	{
		title: t('trading-servers.integration-type'),
		value: 'integrationType',
	},
	{
		title: t('trading-servers.address'),
		value: 'address',
	},
	{
		title: t('trading-servers.trading-platform'),
		value: 'tradingPlatform.name',
	},
	{
		title: t('trading-servers.is-active'),
		value: 'isActive',
	},
	{
		value: 'actions',
	},

])
</script>
