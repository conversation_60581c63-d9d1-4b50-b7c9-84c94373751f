import { exec } from 'node:child_process'
import fs from 'node:fs'
import { glob } from 'glob'

class PostmanCollectionDownloader {
	constructor(collections) {
		this.collections = collections
		this.baseDir = 'types/collections'
	}

	ensureDirectoryExists(dir) {
		if (!fs.existsSync(dir)) {
			fs.mkdirSync(dir, { recursive: true })
		}
	}

	async downloadCollections() {
		for (const key in this.collections) {
			const url = this.collections[key]
			this.ensureDirectoryExists(this.baseDir)
			const collectionPath = `${this.baseDir}/${key}.json`

			try {
				const response = await fetch(url)
				const json = await response.json()

				fs.writeFileSync(collectionPath, JSON.stringify(json.collection, null, 2))
				console.info(`Downloaded ${key} collection to ${collectionPath}`)

				this.generateTypes(collectionPath)
			} catch (error) {
				console.error(error)
			}
		}
	}

	generateTypes(collectionPath) {
		exec(`npx @n1rjal/pm_ts -i ${collectionPath} -o types`, (error, _stdout, _stderr) => {
			if (error) {
				console.error(`exec error: ${error}`)
				console.dir(error)
				return
			}
			this.fixIndentationAndLint()
		})
	}

	fixIndentationAndLint() {
		const dirsToProcess = ['response', 'request', 'queries']
		dirsToProcess.forEach(async (dirName) => {
			const filePattern = `./types/${dirName}/**/*.ts`
			await glob(filePattern)
				.then((files) => {
					files.forEach((file) => {
						fs.readFile(file, 'utf8', (readErr, data) => {
							if (readErr) {
								console.error(readErr)
								return
							}

							const fixedIndent = data.replace(/^( )+/gm, match => '\t'.repeat(match.length / 2))
							fs.writeFile(file, fixedIndent, 'utf8', (writeErr) => {
								if (writeErr) {
									console.error(writeErr)
								} else {
									console.log(`Indentation removed from "${file}".`)
								}
							})
						})
					})
				})

			exec(`npx eslint ./types/${dirName} --fix`, (eslintError, _stdout, _stderr) => {
				if (eslintError) {
					console.error(`exec error: ${eslintError}`)
					console.dir(eslintError)
				} else {
					console.log('Types generated and linted successfully for', dirName)
				}
			})
		})
	}
}

// Define your collection URLs here
const collections = {
	auth: 'https://api.postman.com/collections/19439670-39c3871a-47d5-4cd7-891a-5173cf98e6db?access_key=PMAT-01HXY62NT35PJH9DMM0AGJRJJV',
	resources: 'https://api.postman.com/collections/19439670-4a262f45-f7bf-405e-ba14-5a7e8edc6902?access_key=PMAT-01HYJ6CJG0EEMQW923A89SS277',
	my: 'https://api.postman.com/collections/19439670-7a25321d-d65d-42f1-8c7b-99cf9701d603?access_key=PMAT-01HYT3GHK2HMPAM9KN812SZTRE',
	generalLookups: 'https://api.postman.com/collections/19439670-1359d495-0cf3-404d-a91c-245d19cbd8a0?access_key=PMAT-01HYWNXW8BSKGPHMWZGAM6D5YN',
}

const downloader = new PostmanCollectionDownloader(collections)
downloader.downloadCollections()
