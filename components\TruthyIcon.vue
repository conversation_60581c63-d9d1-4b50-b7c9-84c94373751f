<template>
	<v-icon v-bind="computedProps" />
</template>

<script lang="ts" setup>
import { VIcon } from 'vuetify/components'

type VIconPropTypes = Omit<VIcon['$props'], 'color' | 'icon' | '$children'>

interface Props extends /* @vue-ignore */ VIconPropTypes {
	modelValue?: boolean | number
	trueIcon?: string
	trueIconColor?: string
	falseIcon?: string
	falseIconColor?: string
	indeterminateIcon?: string
	indeterminateIconColor?: string
	size?: string | number
	type?: 'boolean' | 'number'
}

const p = withDefaults(defineProps<Props>(), {
	modelValue: false,
	trueIcon: 'i-mdi:check',
	trueIconColor: 'success',
	falseIcon: 'i-mdi:close',
	falseIconColor: 'error',
	indeterminateIcon: 'i-mdi:circle-outline',
	indeterminateIconColor: 'grey',
	size: 'x-large',
	type: 'boolean',
})

const isTrue = computed(() => {
	if (p.type === 'boolean') {
		return p.modelValue === true
	} else if (p.type === 'number') {
		return p.modelValue === 1
	}

	return true
})

const isFalse = computed(() => {
	if (p.type === 'boolean') {
		return p.modelValue === false
	} else if (p.type === 'number') {
		return p.modelValue === 0
	}

	return true
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const isIndeterminate = computed(() => {
	return !isTrue.value && !isFalse.value
})

const icon = computed(() => {
	if (isTrue.value) {
		return p.trueIcon
	} else if (isFalse.value) {
		return p.falseIcon
	} else {
		return p.indeterminateIcon
	}
},
)

const color = computed(() => {
	if (isTrue.value) {
		return p.trueIconColor
	} else if (isFalse.value) {
		return p.falseIconColor
	} else {
		return p.indeterminateIconColor
	}
})

const computedProps = computed(() => {
	return {
		...p,
		icon: icon.value,
		color: color.value,
	}
})
</script>
