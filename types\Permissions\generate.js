import fs from 'node:fs'
/* eslint-disable-next-line */
import permissions from './permissions.json' assert { type: 'json' };
// assert { type: 'json' }
const permissionsSet = new Set()
permissions.forEach((category) => {
	category.permissions.forEach((permission) => {
		permissionsSet.add(`${category.name}.${permission.name}`)
	})
})

const permissionsArray = Array.from(permissionsSet).sort()
const permissionTypes = permissionsArray.map(permission => `  | '${permission}'`).join('\n')

// Generate the TypeScript content
const tsContent = `export type Names =\n${permissionTypes};\n`

// Write to a file
fs.writeFileSync('./types/Permissions/Names.ts', tsContent)
console.info('\x1B[32m%s\x1B[0m', 'Permissions.ts generated successfully')
