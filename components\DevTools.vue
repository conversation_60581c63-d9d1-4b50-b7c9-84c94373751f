<template>
	<v-dialog
		v-model="dialogModel"
		max-width="500"
	>
		<template #activator="{ props }">
			<v-btn
				v-bind="props"
				position="fixed"
				style="bottom: 4px; left: 4px;z-index:10000"
				variant="text"
				color="red-darken-2"
				icon="i-mdi:bug"
			/>
		</template>
		<template #default="{}">
			<v-defaults-provider :defaults="{ VTextField: { density: 'compact', hideDetails: 'auto', class: 'mb-2' } }">
				<v-confirm-edit
					v-slot="{ model, actions }"
					v-model="storage"
					cancel-text="Reset"
					@save="$window.location.reload()"
				>
					<v-card title="Developer Tools">
						<template #item>
							<v-tabs v-model="tab">
								<v-tab value="general">
									General
								</v-tab>
								<v-tab value="fcm">
									FCM
								</v-tab>
							</v-tabs>
						</template>

						<template #text>
							<v-defaults-provider
								:defaults="{
									VTextField: {
										density: 'comfortable',
									},
								}"
							>
								<v-tabs-window v-model="tab">
									<v-tabs-window-item value="general">
										<v-text-field
											v-model.trim="model.value.apiBaseUrl"
											label="API Base URL"
										/>
										<v-text-field
											v-model.trim="model.value.socketUrl"
											label="Socket URL"
										/>
										<v-switch
											v-model="model.value.log.showLog"
											color="success"
											label="Show Console Logs"
										/>
									</v-tabs-window-item>

									<v-tabs-window-item value="fcm">
										<v-text-field
											:model-value="fcmStore.token"
											label="Token"
											readonly
											append-inner-icon="i-mdi:content-copy"
											@click:append-inner="copy(fcmStore.token)"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.apiKey"
											label="apiKey"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.authDomain"
											label="authDomain"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.projectId"
											label="projectId"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.storageBucket"
											label="storageBucket"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.messagingSenderId"
											label="messagingSenderId"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.firebaseOptions.appId"
											label="appId"
										/>
										<v-text-field
											v-model.trim="model.value.fcm.vapidKey"
											label="vapidKey"
										/>
									</v-tabs-window-item>
								</v-tabs-window>
							</v-defaults-provider>
						</template>

						<template #actions>
							<v-btn @click="dialogModel = false">
								Cancel
							</v-btn>
							<v-btn
								:disabled="isDefault"
								@click="restoreDefaults"
							>
								Restore Defaults
							</v-btn>
							<v-spacer />
							<component :is="actions" />
						</template>
					</v-card>
				</v-confirm-edit>
			</v-defaults-provider>
		</template>
	</v-dialog>
</template>

<script lang="ts" setup>
import type { PublicRuntimeConfig } from 'nuxt/schema'

const config = useRuntimeConfig()

const storage = useLocalStorage('devTools', config.public, { mergeDefaults: true })

const defaults = useLocalStorage('defaults', {} as PublicRuntimeConfig)

// storage.value = useCloned(defaults.value).cloned.value

const dialogModel = ref(false)

const tab = ref('general')

const fcmStore = useFcmStore()

const { copy } = useClipboard()

function restoreDefaults() {
	storage.value = useCloned(defaults.value).cloned.value
	window.location.reload()
}

const isDefault = computed(() => {
	return isEqual(storage.value, defaults.value)
})
</script>
