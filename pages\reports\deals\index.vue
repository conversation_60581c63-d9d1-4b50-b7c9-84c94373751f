<template>
	<page :title="$t('deals.historical-deals')">
		<template #actions>
			<v-btn
				prepend-icon="i-mdi:export-variant"
				color="primary"
			>
				{{ $t('deals.export-data') }}
				<v-dialog
					v-model="exportDialogModel"
					activator="parent"
					max-width="400"
				>
					<v-card>
						<v-card-title>
							{{ $t('deals.export-data') }}
						</v-card-title>
						<v-card-text>
							<p class="text-medium-emphasis text-body-2 mb-4">
								{{ $t('deals.control-the-number-description') }}
							</p>
							<p class="text-medium-emphasis text-body-2 mb-4">
								Total Records: {{
									//@ts-ignore
									table?.data?.total || 0
								}}
							</p>
							<v-radio-group
								v-model="isExportLimited"
								density="compact"
								hide-details
							>
								<div v-auto-animate>
									<v-radio
										:value="true"
										:label="$t('deals.limited-records')"
									/>
									<v-text-field
										v-if="isExportLimited"
										v-model.number="exportSize"
										class="my-2"
										type="number"
										density="compact"
										:label="$t('deals.number-of-exported-record')"
										single-line
										hide-details
										:rules="rules({
											required: true,
											min: 1,
											//@ts-ignore
											max: table?.data?.total,
											type: 'number',
										})"
									/>
									<v-radio
										:value="false"
										:label="$t('deals.all-records') + ' (' + (
											// @ts-ignore
											table?.data?.total || 0
										) + ')'"
									/>
									<v-alert
										v-if="!isExportLimited"
										type="warning"
										density="compact"
										variant="tonal"
										class="mt-2 text-subtitle-2"
									>
										{{ $t('deals.exporting-all-records-warning') }}
									</v-alert>
								</div>
							</v-radio-group>
						</v-card-text>
						<v-card-actions>
							<v-btn
								color="secondary"
								variant="text"
								@click="exportDialogModel = false"
							>
								{{ $t('deals.cancel') }}
							</v-btn>
							<v-btn
								:loading="isExporting"
								:disabled="isExportLimited && !exportSize"
								variant="text"
								@click="exportData"
							>
								{{ $t('deals.export') }}
							</v-btn>
						</v-card-actions>
					</v-card>
				</v-dialog>
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/report/historical-deals"
			:api-options="{
				// @ts-ignore
				priority: 'high',
				immediate: false,
			}"
			no-search
			:headers="headers"
			:lazyload="{ rootMargin: '0px 0px 25% 0px' }"
			:on-mounted="table?.showFilter()"
			persistent-filter
		>
			<template #filter="{ model }">
				<v-text-field
					v-model="model.positionId"
					clearable
					:label="$t('deals.position-id')"
				/>
				<v-text-field
					v-model="model.loginNumber"
					clearable
					:label="$t('deals.login-number')"
				/>
				<symbols-lookup
					v-model="model.symbol"
					component="combobox"
					clearable
					:label="$t('deals.symbol')"
				/>
				<v-text-field
					v-model="model.fromDate"
					clearable
					:max="model.toDate"
					type="date"
					:label="$t('deals.from-date')"
					:rules="rules({ required: true })"
				/>
				<v-text-field
					v-model="model.toDate"
					:min="model.fromDate"
					type="date"
					:label="$t('deals.to-date')"
					:rules="rules({ required: true })"
					clearable
				/>
			</template>
			<template #item.action="{ value }">
				{{ value === 0 ? 'Buy' : 'Sell' }}
			</template>
			<template #item.minPrice="{ item }">
				<v-lazy>
					<deals-min-max-price
						item-key="minPrice"
						:symbol="item.symbol"
						:from="item.openTime"
						:to="item.closeTime"
						:action="item.action"
					/>
				</v-lazy>
			</template>
			<template #item.maxPrice="{ item }">
				<deals-min-max-price
					readonly
					item-key="maxPrice"
					:symbol="item.symbol"
					:from="item.openTime"
					:to="item.closeTime"
					:action="item.action"
				/>
			</template>
			<template #item.minPriceTime="{ item }">
				<deals-min-max-price
					readonly
					item-key="minPriceTime"
					:symbol="item.symbol"
					:from="item.openTime"
					:to="item.closeTime"
					:action="item.action"
				/>
			</template>
			<template #item.maxPriceTime="{ item }">
				<deals-min-max-price
					readonly
					item-key="maxPriceTime"
					:symbol="item.symbol"
					:from="item.openTime"
					:to="item.closeTime"
					:action="item.action"
				/>
			</template>
			<!-- <template #item.openTime="{value}">
				{{ dateTime(value, DateFormat.MT) }}
			</template>
			<template #item.closeTime="{value}">
				{{ dateTime(value, DateFormat.MT) }}
			</template> -->
		</datatable>
	</page>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'

definePageMeta({
	permission: 'reports.deals',
})

const { t } = useI18n()

const table = ref<typeof Datatable>()

const { $api } = useNuxtApp()

const { errorSnackbar, successSnackbar } = useSnackbar()

const exportDialogModel = ref(false)

const isExporting: Ref<boolean> = ref(false)

const isExportLimited = ref<boolean>(true)

const exportSize = ref<number>()

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('deals.position-id'),
		value: 'id',
		nowrap: true,
	},
	{
		title: t('deals.login'),
		value: 'login',
	},
	{
		title: t('deals.symbol'),
		value: 'symbol',
	},
	{
		title: t('deals.action'),
		value: 'action',
	},
	{
		title: t('deals.volume'),
		value: 'volume',
	},
	{
		title: t('deals.open-price'),
		value: 'openPrice',
		nowrap: true,
	},
	{
		title: t('deals.close-price'),
		value: 'closePrice',
		nowrap: true,
	},
	{
		title: t('deals.min-price'),
		value: 'minPrice',
		nowrap: true,
	},
	{
		title: t('deals.max-price'),
		value: 'maxPrice',
		nowrap: true,
	},
	{
		title: t('deals.min-price-time'),
		value: 'minPriceTime',
		nowrap: true,
	},
	{
		title: t('deals.max-price-time'),
		value: 'maxPriceTime',
		nowrap: true,
	},
	{
		title: t('deals.open-time'),
		value: 'openTime',
		nowrap: true,
	},
	{
		title: t('deals.close-time'),
		value: 'closeTime',
		nowrap: true,
	},
	{
		title: t('deals.profit'),
		value: 'profit',
	},

]

const exportData = () => {
	isExporting.value = true

	const query = table.value?.query

	const { cloned: clonedQuery } = useCloned(query, { deep: true })

	delete clonedQuery.value.page

	$api('/report/historical-deals/export', {
		params: {
			...clonedQuery.value,
			pageSize: isExportLimited.value ? exportSize.value : undefined,
		},
	})
		.then(() => {
			exportDialogModel.value = false
			successSnackbar({
				title: t('deals.exporting-data'),
				text: t('deals.the-data-is-being-exported'),
			})
		})
		.catch((e) => {
			errorSnackbar(e.value.message)
		})
		.finally(() => {
			isExporting.value = false
		})
}
</script>
