<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Operation"
		item-identity="displayName"
		:navigation-drawer-props="{ width: 500 }"
		url="/operations/:id"
		v-bind="$attrs"
	>
		<template #default="{ item, errors }: { item: Item<Operations._Id.GETResponse>, errors: ItemErrors<Operations._Id.GETResponse>}">
			<!-- <v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				:rules="rules({ required: true })"
				:label="$t('OperationCrud.name')"
			/> -->
			<v-text-field
				v-model="item.displayName"
				:error-messages="errors.displayName"
				:rules="rules({ required: true })"
				:label="$t('OperationCrud.display-name')"
			/>

			<api-items
				v-slot="props"
				url="/trading-platforms/lookup"
				:error-messages="errors?.tradingPlatformId"
			>
				<v-select
					v-model="item.tradingPlatformId"
					:rules="rules({ required: true })"
					:label="$t('OperationCrud.trading-platform')"
					v-bind="props"
					item-title="name"
					item-value="id"
				/>
			</api-items>

			<api-items
				v-if="item.tradingPlatformId"
				v-slot="props"
				:url="`/lookups/mt-reference-codes`"
				:api-options="{
					params: { tradingPlatformId: computed(() => item.tradingPlatformId) },
				}"
				:error-messages="errors.type"
			>
				<v-select
					v-model="item.type"
					:rules="rules({ required: true })"
					:label="$t('OperationCrud.mt-reference-type')"
					v-bind="props"
					item-title="name"
					item-value="value"
					:disabled="!item.tradingPlatformId"
				/>
			</api-items>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from './Crud.vue'
import type { DefaultItem, Item, ItemErrors } from './Crud.vue'
import type { Operations } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Operations._Id.POSTRequest | Operations._Id.PUTRequest> = {
	// name: '',
	displayName: '',
	type: null,
	tradingPlatformId: null,
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
