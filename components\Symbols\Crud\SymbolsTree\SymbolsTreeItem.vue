<template>
	<v-lazy min-height="40">
		<v-list-group
			v-if="item.type === 'path'"
			:value="item.value"
			expand-icon="i-mdi:folder text-yellow-darken-1"
			collapse-icon="i-mdi:folder-open text-yellow-darken-1"
			f
			subgroup
		>
			<template #activator="{ props }">
				<v-list-item
					v-bind="props"
					:title="item.title"
					:subtitle="item.subtitle"
					s
				>
					<template #title="{ title }">
						{{ title }} <span class="text-caption">({{ item.children.length }})</span>
					</template>
				</v-list-item>
			</template>

			<SymbolsTreeItem
				v-for="(child, i) in item.children"
				:key="i"
				:item="child"
				@symbol-select="selectSymbol"
			/>
		</v-list-group>

		<v-list-item
			v-else
			:title="item.title"
			:subtitle="item.subtitle"
			@click="selectSymbol(item.value)"
		>
			<template #prepend>
				<v-icon
					size="small"
					icon="i-ph:currency-circle-dollar"
				/>
			</template>
		</v-list-item>
	</v-lazy>
</template>

<script lang="ts" setup>
// import SymbolsTreeItem from './SymbolsTreeItem.vue'
import type { Symbols } from '~/types'

type Props = {
	item: Symbols.MT5.Lookup.SingleRecord
}

type Emit = {
	(e: 'symbol-select', value: string): void
}

defineProps<Props>()

const emit = defineEmits<Emit>()

const selectSymbol = (value: string) => {
	emit('symbol-select', value)
}
</script>
