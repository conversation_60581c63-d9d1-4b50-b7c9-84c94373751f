<template>
	<VTextField
		v-model="model"
		v-bind="computedProps"
		step="0.01"
	/>
</template>

<script lang="ts" setup>
import { VTextField } from 'vuetify/components/VTextField'

defineOptions({
	extends: VTextField,
})

type Props = {
	decimals?: number | undefined
	type?: 'number' | undefined
	modelValue?: number | string | undefined
}

const p = withDefaults(defineProps<Props>(), {
	decimals: 2,
	type: 'number',
	modelValue: 0,
})

const computedProps = computed(() => {
	const { modelValue, ...rest } = p
	return {
		...rest,
		type: p.type,
	}
})

const factor = computed(() => Number('1' + '0'.repeat(p.decimals)))

const emit = defineEmits(['update:modelValue'])

const model = computed({
	get: () => {
		// Convert the integer to a decimal based on p.decimals
		// add . at in according to p.decimals from right to left
		const numStr = (p.modelValue || 0).toString().padStart(p.decimals + 1, '0')
		let integerPart = numStr.slice(0, -p.decimals) || '0'
		const fractionalPart = numStr.slice(-p.decimals)

		if (integerPart === '') {
			integerPart = '0'
		}

		const newValue = integerPart + '.' + fractionalPart
		return newValue
	},
	set: (value: number | string) => {
		// Convert the decimal to an integer based on p.decimals
		emit('update:modelValue', Math.round(Number(value) * factor.value))
	},
})
</script>
