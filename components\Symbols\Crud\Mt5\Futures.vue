<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.ieTimeout"
					:label="$t('Symbols.MT5.Crud.Futures.settlement-price')"
					:error-messages="errors.ieTimeout"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.ieTimeout"
					:label="$t('Symbols.MT5.Crud.Futures.minimum-price')"
					:error-messages="errors.ieTimeout"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.ieTimeout"
					:label="$t('Symbols.MT5.Crud.Futures.maximum-price')"
					:error-messages="errors.ieTimeout"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.spliceType"
					:label="$t('Symbols.MT5.Crud.Futures.splice-type')"
					:items="spliceTypeItems"
					:error-messages="errors.spliceType"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model.number="item.spliceTimeType"
					:label="$t('Symbols.MT5.Crud.Futures.splice-date')"
					:items="spliceTimeTypeItems"
					:disabled="!item.spliceType"
					:error-messages="errors.spliceTimeType"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.spliceTimeDays"
					:label="$t('Symbols.MT5.Crud.Futures.splice-type')"
					:error-messages="errors.spliceTimeDays"
					:disabled="!item.spliceType"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
import { Symbols } from '~/types'

withDefaults(defineProps<Props>(), defaults)

const spliceTypeItems = enumToItems(Symbols.MT5.SpliceType, 'Symbols.MT5.SpliceType')

const spliceTimeTypeItems = enumToItems(Symbols.MT5.SpliceTimeType, 'Symbols.MT5.SpliceTimeType')
</script>
