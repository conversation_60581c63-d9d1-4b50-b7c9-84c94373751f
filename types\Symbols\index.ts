export namespace Symbols {

	export namespace MT5 {
		export namespace Lookup {
			export const URL = '/symbols/mt5/lookup'

			export type Type = 'path' | 'symbol'
			export interface SingleRecord {
				title: string
				subtitle: string
				value: string
				type: Type
				children: SingleRecord[]
			}

			export type GETResponse = SingleRecord[]
		}
		export namespace Paths {

			export namespace Lookup {
				export const URL = '/symbols/mt5/paths/lookup'
				export interface SingleRecord {
					title: string
					value: string
					children: SingleRecord[]
					totalItems: number
				}

				export type GETResponse = SingleRecord[]

			}

			export namespace Create {
				export const URL = '/symbols/mt5/path'

				export type POSTRequest = {
					path: string
				}

			}

			export namespace Delete {
				export const URL = (_Path: string) => {
					const p = `/symbols/mt5/path/${_Path.replaceAll('\\', '%5C')}`

					console.log('🚀 ~ URL ~ p:', p)

					return p
				}
			}
		}
		export enum Sectors {
			SECTOR_UNDEFINED = 0,
			SECTOR_BASIC_MATERIALS = 1,
			SECTOR_COMMUNICATION_SERVICES = 2,
			SECTOR_CONSUMER_CYCLICAL = 3,
			SECTOR_CONSUMER_DEFENSIVE = 4,
			SECTOR_ENERGY = 5,
			SECTOR_FINANCIAL = 6,
			SECTOR_HEALTHCARE = 7,
			SECTOR_INDUSTRIALS = 8,
			SECTOR_REAL_ESTATE = 9,
			SECTOR_TECHNOLOGY = 10,
			SECTOR_UTILITIES = 11,
			SECTOR_CURRENCY = 12,
			SECTOR_CURRENCY_CRYPTO = 13,
			SECTOR_INDEXES = 14,
			SECTOR_COMMODITIES = 15,
			// --- enumeration borders
			// SECTOR_FIRST = 0,
			// SECTOR_LAST = 15,
		}

		export enum Industries {
			UNDEFINED = 0,
			// --- Basic Materials
			AGRICULTURAL_INPUTS = 1,
			ALUMINIUM = 2,
			BUILDING_MATERIALS = 3,
			CHEMICALS = 4,
			COKING_COAL = 5,
			COPPER = 6,
			GOLD = 7,
			LUMBER_WOOD = 8,
			INDUSTRIAL_METALS = 9,
			PRECIOUS_METALS = 10,
			PAPER = 11,
			SILVER = 12,
			SPECIALTY_CHEMICALS = 13,
			STEEL = 14,
			// BASIC_MATERIALS_FIRST = 1,
			// BASIC_MATERIALS_LAST = 14,
			// BASIC_MATERIALS_END = 50,
			// --- Communication Services
			ADVERTISING = 51,
			BROADCASTING = 52,
			GAMING_MULTIMEDIA = 53,
			ENTERTAINMENT = 54,
			INTERNET_CONTENT = 55,
			PUBLISHING = 56,
			TELECOM = 57,
			// COMMUNICATION_FIRST = 51,
			// COMMUNICATION_LAST = 57,
			// COMMUNICATION_END = 100,
			// --- Consumer Cyclical
			APPAREL_MANUFACTURING = 101,
			APPAREL_RETAIL = 102,
			AUTO_MANUFACTURERS = 103,
			AUTO_PARTS = 104,
			AUTO_DEALERSHIP = 105,
			DEPARTMENT_STORES = 106,
			FOOTWEAR_ACCESSORIES = 107,
			FURNISHINGS = 108,
			GAMBLING = 109,
			HOME_IMPROV_RETAIL = 110,
			INTERNET_RETAIL = 111,
			LEISURE = 112,
			LODGING = 113,
			LUXURY_GOODS = 114,
			PACKAGING_CONTAINERS = 115,
			PERSONAL_SERVICES = 116,
			RECREATIONAL_VEHICLES = 117,
			RESIDENT_CONSTRUCTION = 118,
			RESORTS_CASINOS = 119,
			RESTAURANTS = 120,
			SPECIALTY_RETAIL = 121,
			TEXTILE_MANUFACTURING = 122,
			TRAVEL_SERVICES = 123,
			// CONSUMER_CYCL_FIRST = 101,
			// CONSUMER_CYCL_LAST = 123,
			// CONSUMER_CYCL_END = 150,
			// --- Consumer Defensive
			BEVERAGES_BREWERS = 151,
			BEVERAGES_NON_ALCO = 152,
			BEVERAGES_WINERIES = 153,
			CONFECTIONERS = 154,
			DISCOUNT_STORES = 155,
			EDUCATION_TRAINIG = 156,
			FARM_PRODUCTS = 157,
			FOOD_DISTRIBUTION = 158,
			GROCERY_STORES = 159,
			HOUSEHOLD_PRODUCTS = 160,
			PACKAGED_FOODS = 161,
			TOBACCO = 162,
			// CONSUMER_DEF_FIRST = 151,
			// CONSUMER_DEF_LAST = 162,
			// CONSUMER_DEF_END = 200,
			// --- Energy
			OIL_GAS_DRILLING = 201,
			OIL_GAS_EP = 202,
			OIL_GAS_EQUIPMENT = 203,
			OIL_GAS_INTEGRATED = 204,
			OIL_GAS_MIDSTREAM = 205,
			OIL_GAS_REFINING = 206,
			THERMAL_COAL = 207,
			URANIUM = 208,
			// ENERGY_FIRST = 201,
			// ENERGY_LAST = 208,
			// ENERGY_END = 250,
			// --- Financial
			EXCHANGE_TRADED_FUND = 251,
			ASSETS_MANAGEMENT = 252,
			BANKS_DIVERSIFIED = 253,
			BANKS_REGIONAL = 254,
			CAPITAL_MARKETS = 255,
			CLOSE_END_FUND_DEBT = 256,
			CLOSE_END_FUND_EQUITY = 257,
			CLOSE_END_FUND_FOREIGN = 258,
			CREDIT_SERVICES = 259,
			FINANCIAL_CONGLOMERATE = 260,
			FINANCIAL_DATA_EXCHANGE = 261,
			INSURANCE_BROKERS = 262,
			INSURANCE_DIVERSIFIED = 263,
			INSURANCE_LIFE = 264,
			INSURANCE_PROPERTY = 265,
			INSURANCE_REINSURANCE = 266,
			INSURANCE_SPECIALTY = 267,
			MORTGAGE_FINANCE = 268,
			SHELL_COMPANIES = 269,
			// FINANCIAL_FIRST = 251,
			// FINANCIAL_LAST = 269,
			// FINANCIAL_END = 300,
			// --- Healthcare
			BIOTECHNOLOGY = 301,
			DIAGNOSTICS_RESEARCH = 302,
			DRUGS_MANUFACTURERS = 303,
			DRUGS_MANUFACTURERS_SPEC = 304,
			HEALTHCARE_PLANS = 305,
			HEALTH_INFORMATION = 306,
			MEDICAL_FACILITIES = 307,
			MEDICAL_DEVICES = 308,
			MEDICAL_DISTRIBUTION = 309,
			MEDICAL_INSTRUMENTS = 310,
			PHARM_RETAILERS = 311,
			// HEALTHCARE_FIRST = 301,
			// HEALTHCARE_LAST = 311,
			// HEALTHCARE_END = 350,
			// --- Industrials
			AEROSPACE_DEFENSE = 351,
			AIRLINES = 352,
			AIRPORTS_SERVICES = 353,
			BUILDING_PRODUCTS = 354,
			BUSINESS_EQUIPMENT = 355,
			CONGLOMERATES = 356,
			CONSULTING_SERVICES = 357,
			ELECTRICAL_EQUIPMENT = 358,
			ENGINEERING_CONSTRUCTION = 359,
			FARM_HEAVY_MACHINERY = 360,
			INDUSTRIAL_DISTRIBUTION = 361,
			INFRASTRUCTURE_OPERATIONS = 362,
			FREIGHT_LOGISTICS = 363,
			MARINE_SHIPPING = 364,
			METAL_FABRICATION = 365,
			POLLUTION_CONTROL = 366,
			RAILROADS = 367,
			RENTAL_LEASING = 368,
			SECURITY_PROTECTION = 369,
			SPEALITY_BUSINESS_SERVICES = 370,
			SPEALITY_MACHINERY = 371,
			STUFFING_EMPLOYMENT = 372,
			TOOLS_ACCESSORIES = 373,
			TRUCKING = 374,
			WASTE_MANAGEMENT = 375,
			// INDUSTRIALS_FIRST = 351,
			// INDUSTRIALS_LAST = 375,
			// INDUSTRIALS_END = 400,
			// --- Real Estate
			REAL_ESTATE_DEVELOPMENT = 401,
			REAL_ESTATE_DIVERSIFIED = 402,
			REAL_ESTATE_SERVICES = 403,
			REIT_DIVERSIFIED = 404,
			REIT_HEALTCARE = 405,
			REIT_HOTEL_MOTEL = 406,
			REIT_INDUSTRIAL = 407,
			REIT_MORTAGE = 408,
			REIT_OFFICE = 409,
			REIT_RESIDENTAL = 410,
			REIT_RETAIL = 411,
			REIT_SPECIALITY = 412,
			// REAL_ESTATE_FIRST = 401,
			// REAL_ESTATE_LAST = 412,
			// REAL_ESTATE_END = 450,
			// --- Technology
			COMMUNICATION_EQUIPMENT = 451,
			COMPUTER_HARDWARE = 452,
			CONSUMER_ELECTRONICS = 453,
			ELECTRONIC_COMPONENTS = 454,
			ELECTRONIC_DISTRIBUTION = 455,
			IT_SERVICES = 456,
			SCIENTIFIC_INSTRUMENTS = 457,
			SEMICONDUCTOR_EQUIPMENT = 458,
			SEMICONDUCTORS = 459,
			SOFTWARE_APPLICATION = 460,
			SOFTWARE_INFRASTRUCTURE = 461,
			SOLAR = 462,
			// TECHNOLOGY_FIRST = 451,
			// TECHNOLOGY_LAST = 462,
			// TECHNOLOGY_END = 500,
			// --- Utilities
			UTILITIES_DIVERSIFIED = 501,
			UTILITIES_POWERPRODUCERS = 502,
			UTILITIES_RENEWABLE = 503,
			UTILITIES_REGULATED_ELECTRIC = 504,
			UTILITIES_REGULATED_GAS = 505,
			UTILITIES_REGULATED_WATER = 506,
			// UTILITIES_FIRST = 501,
			// UTILITIES_LAST = 506,
			// UTILITIES_END = 550,
			// --- Commodities
			COMMODITIES_AGRICULTURAL = 551,
			COMMODITIES_ENERGY = 552,
			COMMODITIES_METALS = 553,
			COMMODITIES_PRECIOUS = 554,
			// COMMODITIES_FIRST = 551,
			// COMMODITIES_LAST = 554,
			// COMMODITIES_END = 600,
			// FIRST = 0,
			// LAST = COMMODITIES_LAST
		}

		export enum FillingFlags {
			FILL_FLAGS_NONE = 0, // none
			FILL_FLAGS_FOK = 1, // allowed FOK
			FILL_FLAGS_IOC = 2, // allowed IOC
			FILL_FLAGS_BOC = 4, // allowed BOC
			// --- flags borders
			FILL_FLAGS_ALL = 1 | 2 | 4,
		}

		export enum ExpirationFlags {
			TIME_FLAGS_NONE = 0, // none
			TIME_FLAGS_GTC = 1, // allowed Good Till Cancel
			TIME_FLAGS_DAY = 2, // allowed Good Till Day
			TIME_FLAGS_SPECIFIED = 4, // allowed specified expiration date
			TIME_FLAGS_SPECIFIED_DAY = 8, // allowed specified expiration date as day
			// --- flags borders
			// TIME_FLAGS_FIRST = 1,
			TIME_FLAGS_ALL = 1 | 2 | 4 | 8,
		}

		export enum TradeMode {
			TRADE_DISABLED = 0, // trade disabled
			TRADE_LONGONLY = 1, // only long positions allowed
			TRADE_SHORTONLY = 2, // only short positions allowed
			TRADE_CLOSEONLY = 3, // only positions closure
			TRADE_FULL = 4, // all trade operations are allowed
			// --- public enum eration borders
			// TRADE_FIRST = 0,
			// TRADE_LAST = 4,
		}

		export enum ExecutionMode {
			EXECUTION_REQUEST = 0, // Request Execution
			EXECUTION_INSTANT = 1, // Instant Execution
			EXECUTION_MARKET = 2, // Market Execution
			EXECUTION_EXCHANGE = 3, // Exchange Execution
			// --- public enum eration borders
			// EXECUTION_FIRST = 0,
			// EXECUTION_LAST = 3,
		}

		export enum CalcMode {
			// --- market maker modes
			TRADE_MODE_FOREX = 0,
			TRADE_MODE_FUTURES = 1,
			TRADE_MODE_CFD = 2,
			TRADE_MODE_CFDINDEX = 3,
			TRADE_MODE_CFDLEVERAGE = 4,
			TRADE_MODE_FOREX_NO_LEVERAGE = 5,
			// --- market makers public enum erations
			// TRADE_MODE_MM_FIRST = 0,
			// TRADE_MODE_MM_LAST = 5,
			// --- exchange modes
			TRADE_MODE_EXCH_STOCKS = 32,
			TRADE_MODE_EXCH_FUTURES = 33,
			TRADE_MODE_EXCH_FUTURES_FORTS = 34,
			TRADE_MODE_EXCH_OPTIONS = 35,
			TRADE_MODE_EXCH_OPTIONS_MARGIN = 36,
			TRADE_MODE_EXCH_BONDS = 37,
			TRADE_MODE_EXCH_STOCKS_MOEX = 38,
			TRADE_MODE_EXCH_BONDS_MOEX = 39,
			// --- exchange public enum erations
			// TRADE_MODE_EXCH_FIRST = 32,
			// TRADE_MODE_EXCH_LAST = 39,
			// --- service modes
			TRADE_MODE_SERV_COLLATERAL = 64,
			// --- service enumerations
			// TRADE_MODE_SERV_FIRST = 64,
			// TRADE_MODE_SERV_LAST = 64,
			// --- public enum eration borders
			// TRADE_MODE_FIRST = 0,
			// TRADE_MODE_LAST = 64,
		}

		export enum GTCMode {
			ORDERS_GTC = 0, // Good Till Cancelled
			ORDERS_DAILY = 1, // Daily Orders
			ORDERS_DAILY_NO_STOPS = 2, // Daily Orders Without Stops
			// --- public enum eration borders
			// ORDERS_FIRST = 0,
			// ORDERS_LAST = 2,
		}

		export enum TickFlags {
			TICK_REALTIME = 1, // allow realtime tick apply
			TICK_COLLECTRAW = 2, // allow to collect raw ticks
			TICK_FEED_STATS = 4, // allow to receive price statisticks from datafeeds
			TICK_NEGATIVE_PRICES = 8, // allow to receive negative prices
			// --- flags borders
			TICK_NONE = 0,
			TICK_ALL = 1 | 2 | 4 | 8,
		}

		export enum ChartMode {
			CHART_MODE_BID_PRICE = 0,
			CHART_MODE_LAST_PRICE = 1,
			// CHART_MODE_OLD = 255,
			// CHART_MODE_FIRST = ChartMode.CHART_MODE_BID_PRICE,
			// CHART_MODE_LAST = ChartMode.CHART_MODE_OLD
		}

		export enum MarginFlags {
			MARGIN_FLAGS_NONE = 0,
			MARGIN_FLAGS_CHECK_PROCESS = 1,
			MARGIN_FLAGS_CHECK_SLTP = 2,
			MARGIN_FLAGS_HEDGE_LARGE_LEG = 4,
			MARGIN_FLAGS_EXCLUDE_PL = 8,
			MARGIN_FLAGS_RECALC_RATES = 16,
		}

		export enum SwapMode {
			SWAP_DISABLED = 0,
			SWAP_BY_POINTS = 1,
			SWAP_BY_SYMBOL_CURRENCY = 2,
			SWAP_BY_MARGIN_CURRENCY = 3,
			SWAP_BY_GROUP_CURRENCY = 4,
			SWAP_BY_INTEREST_CURRENT = 5,
			SWAP_BY_INTEREST_OPEN = 6,
			SWAP_REOPEN_BY_CLOSE_PRICE = 7,
			SWAP_REOPEN_BY_BID = 8,
			SWAP_BY_PROFIT_CURRENCY = 9,
			// SWAP_FIRST = SwapMode.SWAP_DISABLED,
			// SWAP_LAST = SwapMode.SWAP_BY_PROFIT_CURRENCY
		}

		export enum SwapFlags {
			SWAP_FLAGS_NONE = 0,
			SWAP_FLAGS_CONSIDER_HOLIDAYS = 1,
		}

		export enum InstantMode {
			INSTANT_CHECK_NORMAL = 0,
			// INSTANT_CHECK_FIRST = InstantMode.INSTANT_CHECK_NORMAL,
			// INSTANT_CHECK_LAST = InstantMode.INSTANT_CHECK_NORMAL
		}

		export enum RequestFlags {
			REQUEST_FLAGS_NONE = 0,
			REQUEST_FLAGS_ORDER = 1,
			// REQUEST_FLAGS_ALL = RequestFlags.REQUEST_FLAGS_ORDER
		}

		export enum TradeInstantFlags {
			INSTANT_FLAGS_NONE = 0,
			INSTANT_FLAGS_FAST_CONFIRMATION = 1,
			// INSTANT_FLAGS_ALL = TradeInstantFlags.INSTANT_FLAGS_FAST_CONFIRMATION
		}

		export enum MarginRateTypes {
			MARGIN_RATE_BUY = 0,
			MARGIN_RATE_SELL = 1,
			MARGIN_RATE_BUY_LIMIT = 2,
			MARGIN_RATE_SELL_LIMIT = 3,
			MARGIN_RATE_BUY_STOP = 4,
			MARGIN_RATE_SELL_STOP = 5,
			MARGIN_RATE_BUY_STOP_LIMIT = 6,
			MARGIN_RATE_SELL_STOP_LIMIT = 7,
			// MARGIN_RATE_FIRST = MarginRateTypes.MARGIN_RATE_BUY,
			// MARGIN_RATE_LAST = MarginRateTypes.MARGIN_RATE_SELL_STOP_LIMIT
		}

		export enum OrderFlags {
			NONE = 0,
			MARKET = 1,
			LIMIT = 2,
			STOP = 4,
			STOP_LIMIT = 8,
			SL = 16,
			TP = 32,
			CLOSEBY = 64,
			// FIRST = MARKET,
			ALL = MARKET | LIMIT | STOP | STOP_LIMIT | SL | TP | CLOSEBY,
		}

		export enum TradeFlags {
			NONE = 0, // profit by deal
			PROFIT_BY_MARKET = 1,
			ALLOW_SIGNALS = 2,
			ALL = PROFIT_BY_MARKET | ALLOW_SIGNALS,
			// DEFAULT = ALLOW_SIGNALS
		}

		export enum OptionMode {
			EUROPEAN_CALL = 0,
			EUROPEAN_PUT = 1,
			AMERICAN_CALL = 2,
			AMERICAN_PUT = 3,
			// FIRST = EUROPEAN_CALL,
			// LAST = AMERICAN_PUT
		}

		export enum SpliceType {
			NONE = 0,
			UNADJUSTED = 1,
			ADJUSTED = 2,
			// FIRST = NONE,
			// LAST = ADJUSTED
		}

		export enum SpliceTimeType {
			EXPIRATION = 0,
			// FIRST = EXPIRATION,
			// LAST = EXPIRATION
		}

		export interface MTConSymbolSession {
			/**
			 * Session start in minutes (60 = 01:00)
			 */
			open: number

			/**
			 * Session start hours and minutes
			 */
			openHours: number

			/**
			 * Session end in minutes (60 = 01:00)
			 */
			close: number

			/**
			 * Session end hours and minutes
			 */
			closeHours: number
		}

		export type MTSymbolStandard = {
			symbol: string
			path: string
			isin: string
			description: string
			international: string
			basis: string
			source: string
			page: string
			currencyBase: string
			currencyBaseDigits: number
			currencyProfit: string
			currencyProfitDigits: number
			currencyMargin: string
			currencyMarginDigits: number
			color: number
			colorBackground: number
			digits: number
			point: number
			multiply: number
			tickFlags: TickFlags
			tickBookDepth: number
			tickChartMode: ChartMode
			filterSoft: number
			filterSoftTicks: number
			filterHard: number
			filterHardTicks: number
			filterDiscard: number
			filterSpreadMax: number
			filterSpreadMin: number
			filterGap: number
			filterGapTicks: number
			tradeMode: TradeMode
			tradeFlags: TradeFlags
			calcMode: CalcMode
			execMode: ExecutionMode
			gtcMode: GTCMode
			fillFlags: FillingFlags
			orderFlags: OrderFlags
			expirFlags: ExpirationFlags
			spread: number
			spreadBalance: number
			spreadDiff: number
			spreadDiffBalance: number
			tickValue: number
			tickSize: number
			contractSize: number
			stopsLevel: number
			freezeLevel: number
			quotesTimeout: number
			volumeMin: number
			volumeMinExt: number
			volumeMax: number
			volumeMaxExt: number
			volumeStep: number
			volumeStepExt: number
			volumeLimit: number
			volumeLimitExt: number
			// marginCheckMode: MarginFlags;
			marginFlags: MarginFlags
			marginInitial: number
			marginInitialBuy: number
			marginInitialSell: number
			marginInitialBuyLimit: number
			marginInitialSellLimit: number
			marginInitialBuyStop: number
			marginInitialSellStop: number
			marginInitialBuyStopLimit: number
			marginInitialSellStopLimit: number
			marginMaintenance: number
			marginMaintenanceBuy: number
			marginMaintenanceSell: number
			marginMaintenanceBuyLimit: number
			marginMaintenanceSellLimit: number
			marginMaintenanceBuyStop: number
			marginMaintenanceSellStop: number
			marginMaintenanceBuyStopLimit: number
			marginMaintenanceSellStopLimit: number
			marginLiquidity: number
			marginHedged: number
			marginCurrency: number
			// marginLong: number;
			// marginShort: number;
			// marginLimit: number;
			// marginStop: number;
			// marginStopLimit: number;
			swapMode: SwapMode
			swapFlags: SwapFlags
			swapLong: number
			swapShort: number
			swap3Day: number
			swapYearDay: number
			swapRateSunday: number
			swapRateMonday: number
			swapRateTuesday: number
			swapRateWednesday: number
			swapRateThursday: number
			swapRateFriday: number
			swapRateSaturday: number
			timeStart: number
			timeExpiration: number
			sessionsQuotes: MTConSymbolSession[][]
			sessionsTrades: MTConSymbolSession[][]
			reFlags: RequestFlags
			reTimeout: number
			ieCheckMode: InstantMode
			ieTimeout: number
			ieSlipProfit: number
			ieSlipLosing: number
			ieVolumeMax: number
			ieVolumeMaxExt: number
			priceSettle: number
			priceLimitMax: number
			priceLimitMin: number
			priceStrike: number
			optionsMode: OptionMode
			faceValue: number
			accruedInterest: number
			spliceType: SpliceType
			spliceTimeType: SpliceTimeType
			spliceTimeDays: number
			ieFlags: TradeInstantFlags
			category: string
			exchange: string
			cfi: string
			sector: Sectors
			industry: Industries
			country: string
			subscriptionsDelay: number
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/symbols/mt5/${_Id}`

			export interface GETResponse extends MTSymbolStandard { }

			export interface POSTRequest extends MTSymbolStandard { }

			export interface PUTRequest extends MTSymbolStandard { }

		}

	}

	export namespace MT4 {

		export namespace Type {
			export namespace Lookup {
				export const URL = '/symbols/mt4/type/lookup'
				export interface SingleRecord {
					title: string
					subtitle: string
					value: number // index
				}

				export type GETResponse = SingleRecord[]
			}
		}
		export namespace Lookup {
			export const URL = '/symbols/mt4/lookup'
			export interface SingleRecord {
				title: string
				subtitle: string
				value: string // index
			}

			export type GETResponse = SingleRecord[]
		}

		export type COLORREF = number

		export type ExternalUnused = [number, number, number, number, number, number, number]

		export enum RealTime {
			Disabled = 0,
			Enabled = 1,
		}

		export type ConSession = {
			openHour: number
			openMin: number
			closeHour: number
			closeMin: number
		}

		export type ConSessions = {
			quote: ConSession[]
			trade: ConSession[]
			// quoteOvernight : number // Internal data.
			// tradeOvernight: number, // Internal data.
			reserved: [0, 0]// Internal data.
		}
		// --- symbol execution mode
		export enum ExecutionMode { EXE_REQUEST, EXE_INSTANT, EXE_MARKET };
		// --- trade mode
		export enum TradingMode { TRADE_NO, TRADE_CLOSE, TRADE_FULL };
		// --- swap type
		export enum SwapType { SWAP_BY_POINTS, SWAP_BY_DOLLARS, SWAP_BY_INTEREST, SWAP_BY_MARGIN_CURRENCY };
		// --- profit calculation mode
		export enum ProfitCalculationMode { PROFIT_CALC_FOREX, PROFIT_CALC_CFD, PROFIT_CALC_FUTURES };
		// --- margin calculation mode
		export enum MarginCalculationMode { MARGIN_CALC_FOREX, MARGIN_CALC_CFD, MARGIN_CALC_FUTURES, MARGIN_CALC_CFDINDEX, MARGIN_CALC_CFDLEVERAGE };
		// --- GTC mode
		export enum GTCMode { ORDERS_DAILY, ORDERS_GTC, ORDERS_DAILY_NO_STOPS };

		export enum Days { SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY }

		export type MTSymbolStandard = {
			symbol: string
			description: string
			source: string
			currency: string
			type: number// The index of a group of symbols, to which this symbol belongs, in the ConGroup::secgroups array.,
			digits: number
			trade: TradingMode
			backgroundColor: COLORREF
			count: number
			countOriginal: number
			externalUnused: ExternalUnused
			realtime: RealTime
			starting: EpochTimeStamp
			expiration: EpochTimeStamp
			sessions: ConSessions[]
			profitMode: ProfitCalculationMode
			profitReserved: number
			filter: number
			filterCounter: number
			filterLimit: number
			filterSmoothing: number
			filterReserved: number
			logging: 0 | 1
			spread: number // Symbol spread. If you set a value other than "0" in this parameter, the spread will be considered fixed and will be calculated for the symbol using the spread_balance option: The 0 value means that the spread is floating.
			spreadBalance: number
			exemode: ExecutionMode
			swapEnable: 0 | 1
			swapType: SwapType
			swapLong: number
			swapShort: number
			swapRollover3Days: Omit<Days, Days.SUNDAY | Days.SATURDAY>
			contractSize: number
			tickValue: number
			tickSize: number
			stopsLevel: number
			gtcPendings: GTCMode
			marginMode: MarginCalculationMode
			marginInitial: number
			marginMaintenance: number
			marginHedged: number
			marginDivider: number
			point: number
			multiply: number
			bidTickValue: number
			askTickValue: number
			longOnly: number
			instantMaxVolume: number
			marginCurrency: string
			freezeLevel: number
			marginHedgedStrong: 0 | 1
			valueDate: number
			quotesDelay: number
			swapOpenPrice: number
			swapVariationMargin: number
			unused: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/symbols/mt4/${_Id}`

			export interface GETResponse extends MTSymbolStandard { }

			export interface POSTRequest extends MTSymbolStandard { }

			export interface PUTRequest extends MTSymbolStandard { }

		}
	}
}
