<template>
	<info-tooltip
		open-on-hover
		:disabled="cannot('group.view')"
	>
		<slot />
		<template #info>
			<v-lazy
				v-model="lazyModel"
				min-width="220"
			>
				<v-skeleton-loader
					:loading="status === 'pending'"
					type="paragraph@1"
				>
					<div v-if="status === 'success'">
						<div class="mb-1">
							<b>{{ $t('GroupTooltip.company') }}</b> {{ data?.company }}
						</div>
						<div class="my-1">
							<b>{{ $t('GroupTooltip.currency') }}</b> {{ data?.currency }}
						</div>
						<!-- <div class="mt-1">
							<b>{{$t('GroupTooltip.margin')}}</b> {{ data?.marginCallLevel  }}/{{ data?.marginStopOut }}%
						</div>	 -->
					</div>
					<v-list-item
						v-else
						title="Couldn't Load Group Info"
						subtitle="It could be the Group has been deleted."
					>
						<template #prepend>
							<v-icon
								icon="i-mdi:alert"
								color="warning"
							/>
						</template>
					</v-list-item>
				</v-skeleton-loader>
			</v-lazy>
		</template>

		<template #actions>
			<v-spacer />

			<v-btn
				v-if="can('group.create')"
				:disabled="status !== 'success'"
				size="small"
				prepend-icon="i-mdi:pencil"
				variant="text"
				@click="emit('update', { group })"
			>
				{{ $t('GroupTooltip.edit-group') }}
			</v-btn>
		</template>
	</info-tooltip>
</template>

<script lang="ts" setup>
import InfoTooltip from '~/components/InfoTooltip.vue'
import { Groups } from '~/types/Groups'

type Emit = {
	(e: 'update', value: { group: string }): void
}

type Props = {
	group: string
}

defineOptions({
	extends: InfoTooltip,
})

const p = defineProps<Props>()

const emit = defineEmits<Emit>()

const lazyModel = ref(false)

const { data, execute, status } = useApi<Groups.MT4.MTGroupStandard>(Groups.MT4._Id.URL(p.group), {
	immediate: false,
	cache: 'default',
	key: `group-info-tooltip:groups/${p.group}`,
	headers: {
		'Cache-Control': 'max-age=3600',
	},
})

const unwatch = watch(() => lazyModel.value, (value) => {
	if (value) {
		execute()
		unwatch()
	}
})
</script>
