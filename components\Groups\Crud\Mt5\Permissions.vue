<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model="item.limitSymbols"
					:items="limitItems"
					:label="$t('Groups.MT5.Crud.Permissions.maximum-symbols')"
					:return-object="false"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.limitHistory"
					:items="limitHistoryItems"
					:label="$t('Groups.MT5.Crud.Permissions.maximum-symbols-0')"
					:return-object="false"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model="item.limitPositions"
					:items="limitItems"
					:label="$t('Groups.MT5.Crud.Permissions.maximum-positions')"
					:return-object="false"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model="item.limitOrders"
					:items="limitItems"
					:label="$t('Groups.MT5.Crud.Permissions.maximum-orders')"
					:return-object="false"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.demoDeposit"
					type="number"
					:items="limitItems"
					:label="$t('Groups.MT5.Crud.Permissions.deposit-by-default')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.demoLeverage"
					:items="demoLeverageItems"
					:label="$t('Groups.MT5.Crud.Permissions.leverage-by-default')"
					:return-object="false"
				/>
			</v-col>
		</v-row>

		<v-select
			v-model="tradeSignalModel"
			:items="tradeSignalsItems"
			:label="$t('Groups.MT5.Crud.Permissions.trading-signals')"
		/>
		<v-select
			v-model="item.tradeTransferMode"
			:items="tradeTransferModeItems"
			:label="$t('Groups.MT5.Crud.Permissions.transfer-mode')"
		/>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.enable-trading-by-expert-')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_EXPERTS"
					color="primary"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.enable-position-closing-a')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_FIFO_CLOSE"
					color="primary"
					class="compact"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.enable-trailing-stops')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_TRAILING"
					color="primary"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.prohibit-hedge-positions')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_HEDGE_PROHIBIT"
					color="primary"
					class="compact"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.enable-charge-of-swaps')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_SWAPS"
					color="primary"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="tradeFlagsModel"
					hide-details
					:label="$t('Groups.MT5.Crud.Permissions.enable-deal-cost-calculation')"
					:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_DEAL_COST"
					color="primary"
					class="compact"
				/>
			</v-col>
		</v-row>
		<v-row>
			<v-col
				v-if="item.group.startsWith('demo')"
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.demoTradesClean"
					:label="$t('Groups.MT5.Crud.Permissions.inactivity-period')"
					type="number"
					suffix="days"
					hint="Orders & Positions will be deleted after inactivity period"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.tradeInterestrate "
					:items="demoLeverageItems"
					:label="$t('Groups.MT5.Crud.Permissions.trade-interestrate')"
					type="number"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { t } = useI18n()

const limitItems = [
	{ title: t('Groups.MT5.Crud.Permissions.unlimited'), value: 0 },
	{ title: '10', value: 10 },
	{ title: '30', value: 30 },
	{ title: '50', value: 50 },
]

const limitHistoryItems = enumToItems(Groups.MT5.EnHistoryLimit, 'Groups.MT5.EnHistoryLimit')

const demoLeverageItems = [

	{
		title: '',
		value: 0,
	},
	{
		title: '1:5000',
		value: 5000,
	},
	{
		title: '1:1000',
		value: 1000,
	},
	{
		title: '1:500',
		value: 500,
	},
	{
		title: '1:400',
		value: 400,
	},
	{
		title: '1:300',
		value: 300,
	},
	{
		title: '1:200',
		value: 200,
	},
	{
		title: '1:175',
		value: 175,
	},
	{
		title: '1:150',
		value: 150,
	},
	{
		title: '1:125',
		value: 125,
	},
	{
		title: '1:100',
		value: 100,
	},
	{
		title: '1:75',
		value: 75,
	},
	{
		title: '1:50',
		value: 50,
	},
	{
		title: '1:33',
		value: 33,
	},
	{
		title: '1:25',
		value: 25,
	},
	{
		title: '1:20',
		value: 20,
	},
	{
		title: '1:10',
		value: 10,
	},
	{
		title: '1:5',
		value: 5,
	},
	{
		title: '1:2',
		value: 2,
	},
	{
		title: '1:1',
		value: 1,
	},

]

const tradeSignalsEnums = [
	Groups.MT5.EnTradeFlags.TRADEFLAGS_NONE,
	Groups.MT5.EnTradeFlags.TRADEFLAGS_SIGNALS_ALL,
	Groups.MT5.EnTradeFlags.TRADEFLAGS_SIGNALS_OWN,
]
const tradeSignalsItems = enumToItems(Groups.MT5.EnTradeFlags, 'Groups.MT5.EnTradeFlags', item => tradeSignalsEnums.includes(item.value) ? item : undefined)

const computedTradeFlags = computed({
	get: () => p.item.tradeFlags,
	set: (value: number) => emit('update:item', { tradeFlags: value }),
})
const { model: tradeFlagsModel } = useMultiSelectEnum(computedTradeFlags, Groups.MT5.EnTradeFlags, {
	exclude: [Groups.MT5.EnTradeFlags.TRADEFLAGS_NONE],
})

const tradeSignalModel = computed({
	get: () => {
		for (const item of tradeSignalsItems) {
			if (tradeFlagsModel.value.includes(item.value)) {
				return item.value
			}
		}

		return tradeSignalsItems[0].value
	},
	set: (value: number) => {
		const newValue = [...tradeFlagsModel.value.filter(item => !tradeSignalsEnums.includes(item)), value]
		tradeFlagsModel.value = newValue
	},
})

const tradeTransferModeItems = enumToItems(Groups.MT5.EnTransferMode, 'Groups.MT5.EnTransferMode')
</script>
