<template>
	<tr>
		<td class="px-2">
			<v-tree-select
				v-model="model.name"
				:items="reports"
				hide-details
				variant="plain"
				min-width="200"
				single-line
				:tree-props="{
					selectStrategy: 'single-independent',
				}"
			/>
		</td>
		<td class="px-2">
			<v-checkbox
				v-model="permissionsModel"
				:true-value="Manager.MT5.EnPermissionsFlags.PERMISSION_VIEW"
				color="primary"
				hide-details
				class="compact"
			/>
		</td>
		<td class="px-2">
			<v-checkbox
				v-model="permissionsModel"
				:true-value="Manager.MT5.EnPermissionsFlags.PERMISSION_EXPORT"
				color="primary"
				hide-details
				class="compact"
			/>
		</td>
		<td class="px-2">
			<v-combobox
				v-model="model.limitDays"
				:suffix="model.limitDays === 0 ? undefined:'days'"
				persistent-placeholder
				:items="[{ title: 'Default', value: 0 }]"
				:return-object="false"
				type="number"
				hide-details
				hide-spin-buttons
				variant="plain"
				min-width="100"
			/>
		</td>
		<td
			class="px-2"
		>
			<div class="d-flex">
				<v-btn
					variant="text"
					size="small"
					icon="i-mdi:trash-can"
					@click="$emit('remove', index)"
				/>
				<v-btn
					variant="text"
					class="handle"
					size="small"
					icon="i-mdi:drag"
				/>
			</div>
		</td>
	</tr>
</template>

<script lang="ts" setup>
import type { Reports } from '~/types'
import { Manager } from '~/types'

type Props = {
	index: number
	reports: Reports.MT5.Lookup.GETResponse
}

defineProps<Props>()

const model = defineModel<Manager.MT5.Report>({
	default: () => ({
		name: '',
		limitDays: 0,
		permissions: Manager.MT5.EnPermissionsFlags.PERMISSION_ALL,
	}),
})

type Emit = {
	(e: 'remove', value: Props['index']): void
}

defineEmits<Emit>()

const computedPermissions = computed({
	get: () => model.value?.permissions,
	set: (value: Manager.MT5.EnPermissionsFlags) => {
		model.value.permissions = value
	},
})

const { model: permissionsModel } = useMultiSelectEnum(computedPermissions, Manager.MT5.EnPermissionsFlags)
</script>
