<template>
	<page :title="$t('symbols.symbols')">
		<template #actions>
			<v-btn
				v-if="can('symbol.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="crud?.create(lastOpenedPath?{ path: lastOpenedPath }:undefined)"
			>
				{{ $t('symbols.create-symbol') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/symbols/mt5"
			:headers="headers"
			search-key="name"
			:default-model="{ name: '', path: '' }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
		>
			<template #toolbar.prepend>
				<v-btn
					:disabled="lastOpenedPathHistory.length < 2"
					icon="i-mdi:arrow-back"
					class="me-1"
					@click="back"
				/>
			</template>
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>
			<template #toolbar.middle-start>
				<v-chip
					v-if="lastOpenedPath"
					rounded
					closable
					size="large"
					color="primary"
					@click:close="clearPath"
				>
					{{ lastOpenedPath }}
				</v-chip>
			</template>
			<template #prepend="{ height }">
				<v-sheet
					class="fill-height pe-2 me-2 border-e "
					min-width="300"
				>
					<v-toolbar
						color="surface"
						density="compact"
						class="mb-2"
					>
						<v-text-field
							v-model="searchPathModel"
							clearable
							placeholder="Find Path"
							append-inner-icon="i-mdi:magnify"
							flat
							variant="solo-filled"
							hide-details
							density="comfortable"
							class="rounded-t-lg"
						/>
					</v-toolbar>
					<v-sheet
						class="overflow-y-scroll"
						:max-height="`calc(${height} + var(--datatable-footer-height) - 6px) `"
					>
						<symbols-path-tree
							ref="pathTreeRef"
							v-model="lastOpenedPath"
							v-model:search="debouncedSearchPathModel"
							v-model:opened="openedModel"
							@loaded="pathsData = $event"
							@deleted="pathDeleteHandler"
						/>
					</v-sheet>
				</v-sheet>
			</template>
			<template #item.icon>
				<v-icon icon="i-ph:currency-circle-dollar" />
			</template>
			<template #item.symbol="{ item }">
				<v-list-item
					class="px-0"
					lines="one"
					max-width="300"
					:title="item.symbol"
					:subtitle="item.description"
				/>
			</template>
			<template #item.execMode="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item }"
					item-key="value"
					:items="executionModeItems"
				>
					{{ $t(`Symbols.MT5.ExecutionMode.${item.title}`) }}
				</find-item-by-id>
			</template>

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('symbol.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('symbol.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</div>
			</template>

			<template #body.prepend="{}">
				<tr
					v-for="dir in currentPathChildren"
					:key="dir.value"
					class="cursor-pointer"
					@click="openPathFromTable(dir.value)"
				>
					<td>
						<v-icon
							icon="i-mdi:folder"
							color="yellow-darken-1"
						/>
					</td>
					<td :colspan="headers!.length - 1">
						{{ dir.title }} <span class="text-caption">({{ dir.totalItems }})</span>
					</td>
				</tr>
			</template>
			<template #no-data>
				<div v-if="!currentPathChildren.length">
					{{ $t('$vuetify.noDataText') }}
				</div>
			</template>
			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<symbols-crud-mt5
		ref="crud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh();pathTreeRef?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type SymbolsCrud from '@/components/Symbols/Crud/Mt5/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import SymbolsPathTree from '@/components/Symbols/PathTree/index.vue'
import { Symbols } from '~/types'

const table = ref<InstanceType<typeof Datatable> | null>(null)

const crud = ref<InstanceType<typeof SymbolsCrud> | null>(null)

const searchPathModel = ref('')

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const lastOpenedPath = ref('')

const { undo: back, history: lastOpenedPathHistory } = useRefHistory(lastOpenedPath)

const openedModel = ref<Symbols.MT5.Paths.Lookup.SingleRecord['value'][]>([])

const pathsData = ref<Symbols.MT5.Paths.Lookup.GETResponse>([])

const pathTreeRef = ref<InstanceType<typeof SymbolsPathTree> | null>(null)

const isClearingCache = ref(false)

watch(() => lastOpenedPath.value, (value) => {
	table.value?.filter({
		path: value ? `${value}\\*` : '',
	})
})

const { t } = useI18n()

const headers: Headers = [
	{
		title: '',
		value: 'icon',
	},
	{
		title: t('symbols.symbol'),
		value: 'symbol',
		// nowrap: true,
	},
	{
		title: t('symbols.path'),
		value: 'path',
	},
	{
		title: t('symbols.execution'),
		value: 'execMode',
	},
	{
		value: 'actions',
	},
]

const openPathFromTable = (path: string) => {
	lastOpenedPath.value = path
	// const openedDirs = pathTreeRef.value?.extractPathArray(path)
	// console.log('🚀 ~ openPathFromTable ~ openedDirs:', openedDirs)
	// if (openedDirs) {
	// 	openedModel.value = openedDirs
	// }
	table.value?.filter({
		path: `${path}`,
	})
}

const clearPath = () => {
	lastOpenedPath.value = ''
	openedModel.value = []
	table.value?.filter({})
}

const currentPathChildren = computed(() => {
	if (!lastOpenedPath.value) {
		return []
	}

	// find the path in all items and its children, once found return the children array

	const findInItems = (items: Symbols.MT5.Paths.Lookup.GETResponse, path: string): Symbols.MT5.Paths.Lookup.GETResponse => {
		for (const item of items) {
			if (item.value === path) {
				return item.children
			}
			if (item.children.length) {
				const children = findInItems(item.children, path)
				if (children.length) {
					return children
				}
			}
		}
		return []
	}

	const currentChildren = findInItems(pathsData.value, lastOpenedPath.value)

	return currentChildren
})

const executionModeItems = enumToItems(Symbols.MT5.ExecutionMode)

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/symbols/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
		pathTreeRef.value!.refresh()
	}).finally(() => {
		isClearingCache.value = false
	})
}

const pathDeleteHandler = (path: string) => {
	if (lastOpenedPath.value === path) {
		lastOpenedPath.value = ''
	} else {
		table.value?.refresh()
	}
}
</script>
