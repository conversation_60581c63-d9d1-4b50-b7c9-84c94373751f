<template>
	<div
		class="d-flex flex-column"
		style="min-height:calc(100dvh - var(--v-layout-top) - var(--2fa-banner-height) - 104px)"
	>
		<div>
			<v-row>
				<v-col cols="12">
					<v-toolbar
						color="surface"
						rounded="lg"
						variant="outlined"
						border
					>
						<div class="ps-4">
							<span class="text-medium-emphasis text-subtitle-2">{{ $t('dashboard.welcome-back') }}</span> <br>
							<span class="text-h5">{{ authStore.user?.name }}</span>
						</div>
						<v-divider
							vertical
							inset
							class="mx-8 hidden-sm-and-down"
						/>
						<div class="text-medium-emphasis hidden-sm-and-down">
							<div class="text-subtitle-2">
								{{ currentTime }}
							</div>
							<div class="text-subtitle-2">
								{{ currentDate }}
							</div>
						</div>
						<v-spacer />
						<template v-if="isEditMode">
							<!-- :disabled="!hasLayoutChanged" -->

							<v-btn
								color="secondary"
								@click="confirmCancelHandler"
							>
								{{ $t('dashboard.cancel') }}
							</v-btn>
							<v-btn
								color="primary"
								@click="confirmSaveHandler"
							>
								{{ $t('dashboard.save') }}
							</v-btn>
							<v-divider
								vertical
								inset
								class="ma-4"
							/>
						</template>
						<!-- <template v-else>
							<v-btn
								v-tooltip:top="`Edit Widgets`"
								class="me-4"
								color="primary"
								icon="i-material-symbols-light:table-edit-outline"
								@click="isEditMode = true"
							/>
						</template> -->
						<!-- <v-btn
							class="hidden-sm-and-down"
							prepend-icon="i-mdi:plus"
							color="primary"
							variant="text"
							size="large"
							@click="addWidget"
						>
							<span>	{{ $t('dashboard.add-widget') }}</span>
						</v-btn>
						<v-btn
							icon="i-mdi:plus"
							class="hidden-md-and-up me-2"
							@click="addWidget"
						/> -->
						<v-btn
							icon
							color="primary"
							size="small"
						>
							<v-icon icon="i-mdi:dots-vertical" />
							<v-menu
								activator="parent"
							>
								<v-list
									v-model:selected="settingsModel"
									density="compact"
									slim
								>
									<v-list-item
										title="Add Widget"
										prepend-icon="i-material-symbols-light:add"
										@click="addWidget"
									/>
									<v-list-item
										value="edit-mode"
										prepend-icon="i-material-symbols-light:table-edit-outline"
										title="Edit Widgets"
									/>
								</v-list>
							</v-menu>
						</v-btn>
					</v-toolbar>
				</v-col>
			</v-row>
		</div>
		<widget
			ref="widgetRef"
			v-model="layout"
			class="flex-grow-1 mx-n4"
			:edit-mode="isEditMode"
			@save="confirmSaveHandler"
		>
			<template #empty>
				<v-empty-state :title="$t('dashboard.you-dont-have-any-widget-yet')">
					<template #actions>
						<v-btn
							variant="text"
							prepend-icon="i-mdi:plus"
							@click="addWidget"
						>
							{{ $t('dashboard.add-widget') }}
						</v-btn>
					</template>
					<template #media>
						<v-icon icon="i-mdi:view-dashboard-outline" />
					</template>
				</v-empty-state>
			</template>
		</widget>
	</div>
</template>

<script lang="ts" setup>
import Widget, {} from '@/components/Widget/index.vue'
import type { LayoutItem } from '~/components/Widget/types'

const prefStore = usePreferencesStore()

const authStore = useAuthStore()

const now = useNow()

const dayjs = useDayjs()

const currentTime = computed(() => dayjs(now.value).format('HH:mm:ss'))

const currentDate = computed(() => dayjs(now.value).format('dddd, MMMM D, YYYY'))

const widgetRef = ref <InstanceType<typeof Widget> | null>(null)

const layout = ref<LayoutItem[]>(prefStore.getKey('dashboardWidgets') || [])

// const layoutDebounced = useDebounce(layout, 1000)

// const layout = computed({
// 	get: () => widgetRef.value?.layout || [],
// 	set: (value: LayoutItem[]) => {
// 		widgetRef.value!.layout = value
// 	},
// })

const lastLayout = ref <LayoutItem[]>([])

watch(() => lastLayout.value, (value) => {
	console.log('lastLayout', value)
})
watch(() => layout.value, (value) => {
	console.log('layout', value)
})

const settingsModel = ref<string[]>([])

const isEditMode = computed({
	get: () => settingsModel.value.includes('edit-mode'),
	set: (value) => {
		if (value) {
			settingsModel.value.push('edit-mode')
		} else {
			settingsModel.value = settingsModel.value.filter(v => v !== 'edit-mode')
		}
	},
})

// const hasLayoutChanged = computed(() => {
// 	const removeMoved = (item: LayoutItem) => {
// 		// eslint-disable-next-line @typescript-eslint/no-unused-vars
// 		const { moved, ...rest } = item
// 		return rest
// 	}
// 	const lastLayoutCloned = toRaw(useCloned(lastLayout.value).cloned.value).map(removeMoved)
// 	const layoutDebouncedCloned = toRaw(useCloned(layoutDebounced).cloned.value)?.map(removeMoved)

// 	// console.log('change', (lastLayoutCloned), (widgetRefCloned))
// 	return JSON.stringify(lastLayoutCloned) !== JSON.stringify(layoutDebouncedCloned)
// })

const addWidget = () => {
	isEditMode.value = true
	widgetRef.value?.openPanel()
}

const confirmSaveHandler = () => {
	const layoutCloned = useCloned(layout)
	lastLayout.value = toRaw(layoutCloned.cloned.value) || []
	isEditMode.value = false
	prefStore.updateKey('dashboardWidgets', layoutCloned.cloned.value || [])
}

const confirmCancelHandler = () => {
	const layoutCloned = useCloned(lastLayout.value)
	layout.value = toRaw(layoutCloned.cloned.value)
	console.log('🚀 ~ confirmCancelHandler ~ layout.value:', layout.value)
	isEditMode.value = false
}

onMounted(() => {
	const layoutCloned = useCloned(layout)
	lastLayout.value = toRaw(layoutCloned.cloned.value) || []
})
</script>
