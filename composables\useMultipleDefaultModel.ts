import pick from 'lodash/pick'
import type { Groups } from '~/types'

export const useMultipleDefaultModel = (item: Groups.MT5.Symbol, keys: (keyof Groups.MT5.Symbol)[]) => {
	const pickedObject = pick(item, keys)
	return computed({
		get: () => (Object.values(pickedObject) as boolean[]).every(value => value),
		set: (value: boolean) => {
			Object.keys(pickedObject).forEach(key =>
				// @ts-ignore
				item[key as keyof Groups.MT5.Symbol] = value,
			)
		},
	})
}
