<template>
	<div>
		<page :title="$t('actions.actions')">
			<template #actions>
				<v-btn
					v-if="can('actionTypes.view')"
					prepend-icon="i-material-symbols-light:dynamic-form-outline-rounded"
					variant="text"
					color="primary"
					:to="{ name: 'transactions-workflow-actions-action-types' }"
				>
					{{ $t('actions.action-types') }}
				</v-btn>
				<v-btn
					v-if="can('actions.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('actions.create-action') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/actions"
				:headers="headers"
				search-key="displayName"
			>
				<template #filter="{ model }">
					<v-text-field
						v-model="model.comment"
						:label="$t('actions.comment')"
						clearable
					/>
					<ApiItems
						v-slot="props"
						url="/action-types/lookup"
					>
						<v-autocomplete
							v-model="model.actionTypeId"
							chips
							v-bind="props"
							multiple
							item-title="displayName"
							item-value="id"
							:label="$t('actions.action-type')"
							clearable
						/>
					</ApiItems>
				</template>
				<template #item.onlyReceiver="{ value }">
					<!-- <truthy-icon :model-value="value" /> -->
					<div>
						<v-icon
							icon="i-mdi:input"
							start
						/>Receiver
					</div>
					<div v-if="!value">
						<v-icon
							icon="i-mdi:output"
							start
						/> Sender
					</div>
				</template>
				<template #item.actionType="{ value }">
					<info-tooltip
						open-on-hover
						:disabled="cannot('actionTypes.view')"
					>
						{{ value.displayName }}

						<template #info>
							<div>
								<b>{{ $t('actions.display-name') }}</b> {{ value.displayName }}
							</div>
							<div>
								<b>{{ $t('actions.direction') }}</b> {{ value.isIncremental ? 'In' : 'Out' }}
							</div>
						</template>

						<template #actions>
							<v-spacer />
							<v-btn
								v-if="can('actionTypes.edit')"
								size="small"
								prepend-icon="i-mdi:pencil"
								variant="text"
								@click="actionTypesCrudRef?.update(value)"
							>
								{{ $t('actions.edit') }}
							</v-btn>
						</template>
					</info-tooltip>
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('transactionWorkflow.view')"
						size="small"
						variant="text"
						icon="i-material-symbols-light:lock-person"
						:to="{ name: 'transactions-workflow-actions-actionId-manage-access', params: { actionId: item.id } }"
					/>
					<v-btn
						v-if="can('actions.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('actions.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<actions-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
		<action-types-crud
			ref="actionTypesCrudRef"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type ActionsCrud from '~/components/ActionsCrud.vue'
import type ActionTypesCrud from '~/components/ActionTypesCrud.vue'
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'

definePageMeta({
	permission: 'actions.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof ActionsCrud | null>(null)
const actionTypesCrudRef = ref<typeof ActionTypesCrud | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('actions.display-name-0'),
		value: 'displayName',
		width: 200,
		fixed: true,
	},
	{
		title: t('actions.description'),
		value: 'description',
	},
	{
		title: t('actions.only-receiver'),
		value: 'onlyReceiver',
		align: 'center',
		nowrap: true,
	},
	{
		title: t('actions.action-type'),
		value: 'actionType',
		nowrap: true,
	},
	{
		title: t('actions.comment'),
		value: 'comment',
	},
	{
		title: t('actions.date'),
		value: 'timestamp',
		nowrap: true,
	},
	{
		// title: 'Actions',
		value: 'actions',
		nowrap: true,
	},
]
</script>
