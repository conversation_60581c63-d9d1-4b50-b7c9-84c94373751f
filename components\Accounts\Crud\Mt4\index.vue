<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Accounts.MT4.Crud.account')"
		item-identity="name"
		:navigation-drawer-props="{ width: 650 }"
		:url="Accounts.MT4._Id.URL(':login')"
		:validation-callback="validate"
		:update-url="Accounts.MT4._Id.URL('')"
		v-bind="$attrs"
		@closed="closeHandler"
	>
		<template #title="{ isUpdateAction, item }">
			<div v-if="isUpdateAction">
				{{ $t('Accounts.MT4.Crud.update-account', [item.name]) }}
			</div>
			<div v-else>
				{{ $t('Accounts.MT4.Crud.create-account', [item.name]) }}
			</div>
		</template>
		<template #default="{ item, errors, isUpdateAction }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-3 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class="flex-grow-1"
						>
							<v-form
								v-for="tab in tabs"
								ref="tabForm"
								:key="tab.value"
								v-model="tab.isValid.value"
							>
								<v-tabs-window-item
									:value="tab.value"
									:eager="isValidated"
								>
									<component
										:is="tab.component"
										:update-action="isUpdateAction "
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-tabs-window-item>
							</v-form>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Personal from './Personal.vue'
import Address from './Address.vue'
// import Trades from './Trades.vue'
import Security from './Security.vue'
import Info from './Info.vue'
import type { DefaultItem } from '~/components/Crud.vue'
import Crud from '~/components/Crud.vue'

import { Accounts } from '~/types'

const { generate: generateInvestorPass } = useGeneratePassword()
const { generate: generateMasterPass } = useGeneratePassword()

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem< Accounts.MT4._Id.POSTRequest | Accounts.MT4._Id.PUTRequest> = {
	login: 0,
	group: '',
	password: generateMasterPass(),
	enable: 1,
	enableChangePassword: 1,
	enableReadOnly: 0,
	enableOtp: 1,
	enableFlags: 0,
	enableReserved: [
		0,
		0,
	],
	passwordInvestor: generateInvestorPass(),
	passwordPhone: '',
	name: '',
	country: 'Jordan',
	city: '',
	state: '',
	zipcode: '',
	address: '',
	leadSource: '',
	phone: '',
	email: '',
	comment: '',
	id: '',
	status: 'RE',
	leverage: 100,
	agentAccount: 0,
	timestamp: '1970-01-01T00:00:00',
	lastIp: 0,
	balance: 0,
	previousMonthBalance: 0,
	previousBalance: 0,
	credit: 0,
	interestRate: 0,
	taxes: 0,
	previousMonthEquity: 0,
	previousEquity: 0,
	reserved2: [
		0,
		0,
	],
	otpSecret: '',
	secureReserved: '',
	sendReports: 1,
	mqid: 0,
	userColor: **********,
	unused: '',
	apiData: '',
} satisfies DefaultItem<Accounts.MT4._Id.POSTRequest | Accounts.MT4._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const isUpdateCrud = computed(() => {
	return crudRef.value?.isUpdateAction
})

const tabs = computed<Tab[]>(() => [
	{
		value: 'personal',
		text: t('Accounts.MT4.Crud.personal'),
		component: Personal,
		isValid: ref(true),
	},
	{
		value: 'address',
		text: t('Accounts.MT4.Crud.address'),
		component: Address,
		isValid: ref(true),
	},
	// isUpdateCrud.value && {
	// 	value: 'trades',
	// 	text: t('Accounts.MT4.Crud.trades'),
	// 	component: Trades,
	// 	isValid: ref(true),
	// 	errorsCount: computed(() => tabForm.value?.[1]?.errors.length || 0),
	// },
	isUpdateCrud.value && {
		value: 'security',
		text: t('Accounts.MT4.Crud.security'),
		component: Security,
		isValid: ref(true),
	},
	{
		value: 'info',
		text: t('Accounts.MT4.Crud.info'),
		component: Info,
		isValid: ref(true),
	},
]
	.filter(tab => !!tab)
	.map((tab: Tab, index: number) => {
		tab.errorsCount = computed(() => tabForm.value?.[index]?.errors.length || 0)
		return tab
	}),
)

const tabModel = ref(tabs.value[0].value)

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error(t('Accounts.MT4.Crud.please-fix-the-form-errors')))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.value.forEach(tab => (tab.isValid.value = true))

		generatePasswordsHandler()
	})

	tabModel.value = tabs.value[0].value
}

const generatePasswordsHandler = () => {
	defaultItem.passwordInvestor = generateInvestorPass()
	defaultItem.password = generateMasterPass()
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: () => crudRef.value!.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
