<template>
	<v-container>
		<v-defaults-provider
			:defaults="{
				VCheckbox: {
					color: 'primary',
					hideDetails: true,
					class: 'compact',
					trueValue: 1,
					falseValue: 0,
				},
			}"
		>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.broker"
						:label="$t('Manager.MT4.Crud.Securities.dealer')"
						color="primary"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.trades"
						:label="$t('Manager.MT4.Crud.Securities.edit-delete-trades')"
						color="primary"
					/>
				</v-col>
			</v-row>
		</v-defaults-provider>

		<v-skeleton-loader
			:loading="status === 'pending'"
			type="table"
		>
			<v-table :class="{ 'opacity-50': !item.broker }">
				<thead>
					<tr>
						<th>{{ $t('Manager.MT4.Crud.Securities.type') }}</th>
						<th>{{ $t('Manager.MT4.Crud.Securities.use') }}</th>
						<th>{{ $t('Manager.MT4.Crud.Securities.min-lots') }}</th>
						<th>{{ $t('Manager.MT4.Crud.Securities.max-lots') }}</th>
					</tr>
				</thead>
				<tbody>
					<tr
						v-for="sec, i in securities"
						:key="i"
					>
						<td>{{ sec.title }}</td>
						<td>
							<v-switch
								v-model.number.lazy="item.securityGroups[i].enable"
								:true-value="1"
								:false-value="0"
								color="success"
								hide-details
								:disabled="!item.broker"
							/>
						</td>
						<td>
							<integer-to-decimal-field
								v-model.number="item.securityGroups[i].minimumLots"
								hide-spin-buttons
								type="number"
								density="compact"
								hide-details
								:disabled="!item.broker"
							/>
						</td>
						<td>
							<integer-to-decimal-field
								v-model.number="item.securityGroups[i].maximumLots"
								hide-spin-buttons
								type="number"
								density="compact"
								hide-details
								:disabled="!item.broker"
							/>
						</td>
					</tr>
				</tbody>
			</v-table>
		</v-skeleton-loader>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Symbols } from '~/types'
// import { Accounts } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const { data: securities, status } = useApi<Symbols.MT4.Type.Lookup.GETResponse>(Symbols.MT4.Type.Lookup.URL)
</script>
