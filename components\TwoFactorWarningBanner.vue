<template>
	<v-banner
		v-if="!authStore?.twoFactorEnabled"
		color="warning"
		icon="i-material-symbols-light:encrypted-off-outline"
		:stacked="false"
		:rounded="!isScrolled?'lg':0"
		border
		lines="two"
		sticky
		class="mb-4"
		:class="{ 'border-b': isScrolled, 'rounded-b-lg': isScrolled }"
		style="z-index: 4;"
	>
		<v-banner-text class="flex-grow-1 pe-2">
			<div class="d-flex align-center">
				<div class="flex-grow-1">
					<div class="font-weight-bold">
						{{ $t('TwoFactorWarningBanner.your-account-is-insecure') }}
					</div>
					<div class="text-medium-emphasis">
						{{ $t('TwoFactorWarningBanner.you-have-not-enabled-2-factors-authentication-plea') }}
					</div>
				</div>
				<v-spacer />
				<v-btn
					:active="false"
					color="primary"
					variant="outlined"
					:to="{ name: 'my-security' }"
					append-icon="i-mdi:chevron-right rtl-flip"
				>
					{{ $t('TwoFactorWarningBanner.enable-2fa') }}
				</v-btn>
			</div>
		</v-banner-text>
	</v-banner>
</template>

<script lang="ts" setup>
type Props = {
	isScrolled: boolean
}

defineProps<Props>()

const authStore = useAuthStore()
</script>
