<template>
	<v-container fluid>
		<fieldset>
			<legend>
				{{ $t('Accounts.MT5.Crud.general') }}
			</legend>
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.firstName"
						:label="$t('Accounts.MT5.Crud.first-name')"
						:error-messages="errors.firstName"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.middleName"
						:label="$t('Accounts.MT5.Crud.middle-name')"
						:error-messages="errors.middleName"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.lastName"
						:label="$t('Accounts.MT5.Crud.last-name')"
						:error-messages="errors.lastName"
					/>
				</v-col>
			</v-row>

			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.company"
						:label="$t('Accounts.MT5.Crud.company')"
						:error-messages="errors.company"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.phone"
						:label="$t('Accounts.MT5.Crud.phone')"
						:error-messages="errors.phone"
					/>
				</v-col>
			</v-row>

			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.eMail"
						:label="$t('Accounts.MT5.Crud.email')"
						:error-messages="errors.eMail"
						:rules="rules({ type: 'email' })"
					/>
					<v-select
						v-model="item.language"
						:label="$t('Accounts.MT5.Crud.language')"
						:items="languageItems"
						:error-messages="errors.language"
					/>
					<v-text-field
						v-model="item.id"
						:label="$t('Accounts.MT5.Crud.id-number')"
						:error-messages="errors.id"
					/>
					<v-text-field
						v-model="item.mqid"
						:label="$t('Accounts.MT5.Crud.metaquote-id')"
						:error-messages="errors.mqid"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-combobox
						v-model="item.status"
						:label="$t('Accounts.MT5.Crud.status')"
						:items="['NR', 'RE']"
						:error-messages="errors.status"
					/>
					<v-text-field
						v-model="item.leadCampaign"
						:label="$t('Accounts.MT5.Crud.lead-campaign')"
						:error-messages="errors.leadCampaign"
					/>
					<v-text-field
						v-model="item.leadSource"
						:label="$t('Accounts.MT5.Crud.lead-source')"
						:error-messages="errors.leadSource"
					/>
					<v-text-field
						v-model="registration"
						type="date"
						:label="$t('Accounts.MT5.Crud.registered')"
						:error-messages="errors.registration"
					/>
				</v-col>
			</v-row>
		</fieldset>

		<fieldset>
			<legend>
				{{ $t('Accounts.MT5.Crud.address') }}
			</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.city"
						:label="$t('Accounts.MT5.Crud.city')"
						:error-messages="errors.city"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						v-model="item.country"
						:items="countriesItems"
						:label="$t('Accounts.MT5.Crud.country')"
						:error-messages="errors.country"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.state"
						:label="$t('Accounts.MT5.Crud.state')"
						:error-messages="errors.state"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.zipCode"
						:label="$t('Accounts.MT5.Crud.zipcode')"
						:error-messages="errors.zipCode"
					/>
				</v-col>
			</v-row>
			<v-textarea
				v-model="item.address"
				:error-messages="errors.address"
				:label="$t('Accounts.MT5.Crud.address')"
				rows="1"
			/>

			<v-textarea
				v-model="item.comment"
				:error-messages="errors.comment"
				:label="$t('Accounts.MT5.Crud.comment')"
				rows="1"
			/>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts } from '~/types'

const dayjs = useDayjs()

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { items: countriesItems } = useCountries({ value: 'name' })

const languageItems = enumToItems(Accounts.MT5.Language, 'Accounts.MT5.Language')

const registration = computed({
	get() {
		// convert from timestamp to text that can be use in input type date
		return dayjs(p.item.registration * 1000).format('YYYY-MM-DD')
	},
	set(v: string) {
		// convert from text to timestamp
		emit('update:item', { registration: dayjs(v).unix() })
	},
})
</script>
