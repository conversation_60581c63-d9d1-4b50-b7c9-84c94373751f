<template>
	<RolesCrud
		ref="crudRolesRef"
		@created="newRoleCreatedHandler"
	/>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('UsersCrud.user')"
		item-identity="name"
		:navigation-drawer-props="{ width: 700 }"
		url="/users/:id"
		v-bind="$attrs"
		:create-mapper="mapperHandler"
		:update-mapper="mapperHandler"
		@closed="tab = 'info'"
		@loaded="loadedData = useCloned($event).cloned.value"
	>
		<template #default="{ item, errors, isUpdateAction }: { item: Item<Users._Id.GETResponse>, errors:ItemErrors<Users._Id.GETResponse>, isUpdateAction:boolean }">
			<div class="d-flex flex-row">
				<v-tabs
					v-model="tab"
					color="accent"
					class="flex-shrink-0 pe-3 border-e me-3"
					direction="vertical"
				>
					<v-badge
						:model-value="!!infoTabErrorsCount"
						:offset-y="10"
						:offset-x="10"
						:content="infoTabErrorsCount"
						color="error"
					>
						<v-tab
							prepend-icon="i-mdi:account-outline"
							:text="$t('UsersCrud.information')"
							value="info"
							class="w-100"
						/>
					</v-badge>
					<v-badge
						:model-value="!!permissionsTabErrorsCount"
						:offset-y="10"
						:offset-x="10"
						:content="permissionsTabErrorsCount"
						color="error"
					>
						<v-tab
							prepend-icon="i-mdi-shield-outline"
							:text="$t('UsersCrud.permissions')"
							value="permissions"
							class="w-100"
						/>
					</v-badge>
					<v-badge
						:model-value="!!serversTabErrorsCount"
						:offset-y="10"
						:offset-x="10"
						:content="serversTabErrorsCount"
						color="error"
					>
						<v-tab
							prepend-icon="i-arcticons:metatrader-5"
							:text="$t('UsersCrud.meta-servers')"
							value="servers"
							class="w-100"
						/>
					</v-badge>

					<!-- <v-badge :model-value="!!serversTabErrorsCount" :offset-y="10" :offset-x="10" :content="serversTabErrorsCount" color="error"> -->
					<v-tab
						prepend-icon="i-mdi:ip-outline"
						:text="$t('UsersCrud.whitelist-ips')"
						value="whitelistIps"
						class="w-100"
					/>
					<!-- </v-badge> -->
				</v-tabs>
				<v-sheet
					max-height="calc(100dvh - 132px)"
					class="flex-grow-1 overflow-y-auto"
				>
					<v-tabs-window
						v-model="tab"
						class="flex-grow-1"
					>
						<v-tabs-window-item
							:eager="true"
							value="info"
						>
							<v-text-field
								ref="nameField"
								v-model="item.name"
								:rules="rules({ required: true })"
								:label="$t('UsersCrud.user-name')"
								:error-messages="errors.name"
							/>
							<v-text-field
								ref="emailField"
								v-model="item.email"
								:rules="rules({ type: 'email', required: true })"
								:label="$t('UsersCrud.email')"
								:error-messages="errors.email"
							/>
							<api-items
								ref="roleApiItemsRef"
								v-slot="props"
								url="/roles/lookup"
								:error-messages="errors.roleId"
							>
								<v-select
									v-bind="props"
									ref="roleIdField"
									v-model="item.roleId"
									clearable
									:label="$t('UsersCrud.role')"
									item-title="displayName"
									item-value="id"
									:rules="rules({ required: true })"
									:menu-props="{ 'modelValue': isSelectOpen, 'onUpdate:modelValue': updateMenuHandler }"
									@update:menu="updateMenuHandler"
									@update:model-value="updateRoleHandler($event, item)"
								>
									<template
										v-if="can('roles.create')"
										#prepend-item
									>
										<v-list-item
											slim
											@click="createNewRole()"
										>
											<template #prepend>
												<v-icon>i-mdi:plus</v-icon>
											</template>
											{{ $t('UsersCrud.add-new-role') }}
										</v-list-item>

										<v-divider class="mt-2" />
									</template>
								</v-select>
							</api-items>
							<v-switch
								v-if="isUpdateAction"
								v-model="item.twoFactorEnabled"
								:error-messages="errors.twoFactorEnabled"
								:label="$t('UsersCrud.enable-2fa')"
								color="success"
								:disabled="!loadedData?.twoFactorEnabled"
							/>
						</v-tabs-window-item>

						<v-tabs-window-item
							:eager="true"
							value="permissions"
						>
							<select-permissions
								ref="permissionsField"
								v-model="item.permissions"
								:rules="[]"
								:error-messages="errors.permissions"
							/>
						</v-tabs-window-item>

						<v-tabs-window-item
							:eager="true"
							value="servers"
						>
							<v-card flat>
								<v-card-text>
									<trading-servers-select
										ref="tradingServersField"
										v-model="item.tradingServers"
										:rules="[]"
										:error-messages="errors.tradingServers"
									/>
								</v-card-text>
							</v-card>
						</v-tabs-window-item>
						<v-tabs-window-item
							:eager="true"
							value="whitelistIps"
						>
							<v-card flat>
								<!-- <v-card-text> -->
								<fieldset>
									<legend>
										{{ $t('UsersCrud.restrict-login-to-ips') }}
									</legend>
									<p class="text-medium-emphasis mb-2">
										{{ $t('UsersCrud.user-can-login-from-below') }}
									</p>
									<v-text-field
										v-model="ipTextField"
										v-mask="Mask.IP"
										:rules="rules({ type: 'pattern', pattern: REGEX.IP, message: 'Value is not a valid IP Address' })"
										:label="$t('UsersCrud.ip-address')"
										placeholder="###.###.###.###"
										@keydown.enter="addIp(ipTextField!, item)"
									>
										<template #append="{ isValid }">
											<v-btn
												variant="text"
												:disabled="!ipTextField || !isValid.value"
												prepend-icon="i-mdi:plus"
												@click="addIp(ipTextField!, item)"
											>
												{{ $t('UsersCrud.insert') }}
											</v-btn>
										</template>
									</v-text-field>

									<v-list
										v-auto-animate
										slim
									>
										<v-list-subheader>
											{{ $t('UsersCrud.whitelisted-ips') }}
										</v-list-subheader>
										<v-list-item
											v-for="ip in item.whiteIpAddress"
											:key="ip"
											:title="ip"
										>
											<template #append>
												<v-btn
													variant="text"
													size="small"
													icon="i-mdi:trash"
													@click="removeIp(ip, item)"
												/>
											</template>
										</v-list-item>
										<v-list-item
											v-if="!item.whiteIpAddress?.length"
											prepend-icon="i-mdi:information-outline"
											:subtitle="$t('UsersCrud.no-whitelisted-ips')"
										/>
									</v-list>
								</fieldset>
							<!-- </v-card-text> -->
							</v-card>
						</v-tabs-window-item>
					</v-tabs-window>
				</v-sheet>
			</div>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VInput, VSelect } from 'vuetify/components'
import type Crud from './Crud.vue'
import type { DefaultItem, Item, ItemErrors } from './Crud.vue'
import type ApiItems from './ApiItems.vue'
import type SelectPermissions from './SelectPermissions.vue'
import type TradingServerSelect from './TradingServers/Select/index.vue'
import type { Users } from '~/types/Users'
import type { Roles } from '~/types'
import { Mask } from '~/types/Mask'

const { $confirm } = useNuxtApp()

const { t } = useI18n()

const isSelectOpen = ref(false)

const ipTextField = ref<string | undefined>(undefined)

const crudRef = ref<InstanceType<typeof Crud> | null>(null)
const crudRolesRef = ref<InstanceType<typeof Crud> | null>(null)
const roleApiItemsRef = ref<InstanceType<typeof ApiItems> | null>(null)

const nameField = ref<InstanceType<typeof VInput> | null>(null)
const emailField = ref<InstanceType<typeof VInput> | null>(null)
const roleIdField = ref<InstanceType<typeof VInput> | null>(null)
const permissionsField = ref<InstanceType<typeof SelectPermissions> | null >(null)
const tradingServersField = ref<InstanceType<typeof TradingServerSelect> | null>(null)

const infoTabErrorsCount = computed(() => {
	return [nameField, emailField, roleIdField].filter(field => field?.value?.isValid === false).length
})

const permissionsTabErrorsCount = computed(() => {
	return permissionsField.value?.input?.isValid === false ? 1 : 0
})

const serversTabErrorsCount = computed(() => {
	return tradingServersField.value?.input?.isValid === false ? 1 : 0
})
const defaultItem: DefaultItem<Users._Id.POSTRequest> = {
	name: '',
	email: '',
	roleId: null,
	permissions: [],
	tradingServers: [],
	twoFactorEnabled: false,
	whiteIpAddress: [],
}

const loadedData = ref<Users._Id.GETResponse | null>(null)

const tab = ref('info')

const newRoleCreatedHandler = async (res: any) => {
	await roleApiItemsRef.value?.refresh()

	if (crudRef.value) {
		const item = crudRef.value?.getItem()
		item.roleId = res.id
		crudRef.value?.setItem(item)
	}
}

const updateMenuHandler = (value: boolean) => {
	isSelectOpen.value = value
}

const createNewRole = () => {
	isSelectOpen.value = false
	crudRolesRef.value?.create()
}

const updateRoleHandler = (roleId: number, item: Item<Users._Id.GETResponse>) => {
	if (!roleId) {
		return
	}

	if (item.permissions.length === 0) {
		copyRolePermissions(roleId, item)
		return
	}
	$confirm({
		text: t('UsersCrud.do-you-want-to-replace-item-name-permissions-with-', [item.name]),
		cancelBtn: t('UsersCrud.no'), // not working
		okBtn: t('UsersCrud.yes'), // not working
	})
		.then(() => {
			copyRolePermissions(roleId, item)
		})
		.catch(() => {})
}

const copyRolePermissions = (roleId: number, item: Item<Users._Id.GETResponse>) => {
	const role: Roles.Lookup.SingleRecord = roleApiItemsRef.value?.$bind.items.find((role) => {
		return role.id === roleId
	})
	if (role) {
		const { cloned } = useCloned(role.permissions)
		item.permissions = cloned.value
	}
}

const mapperHandler = (item: Users._Id.POSTRequest | Users._Id.GETResponse) => {
	return {
		...item,

		tradingServers: item.tradingServers.filter(v => !!v.allowAll || !!v.allowedGroups?.length),
	}
}

const addIp = (ip: string, item: Users._Id.GETResponse) => {
	if (!item.whiteIpAddress) {
		item.whiteIpAddress = []
	}
	if (ip && !item.whiteIpAddress.includes(ip)) {
		item.whiteIpAddress.unshift(ip)
	}

	ipTextField.value = undefined
}

const removeIp = (ip: string, item: Users._Id.GETResponse) => {
	item.whiteIpAddress = item.whiteIpAddress.filter(v => v !== ip)
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
