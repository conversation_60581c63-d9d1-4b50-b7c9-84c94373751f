<template>
	<v-container
		class="py-0"
		fluid
	>
		<fieldset>
			<legend>
				{{ $t('Symbols.MT4.Crud.Symbol.general') }}
			</legend>
			<v-row dense>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.symbol"
						:error-messages="errors.symbol"
						:rules="rules({ required: true, type: 'string', max: 12 })"
						:label="$t('Symbols.MT4.Crud.Symbol.symbol')"
						append-inner-icon="i-mdi:caps-lock"
						persistent-hint
						:hint="loadedSymbol && loadedSymbol !== item.symbol ? $t('Symbols.MT4.Crud.Symbol.loaded-symbol', [loadedSymbol]) : undefined"
						@click:append-inner="item.symbol = item.symbol.toUpperCase()"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<api-items
						v-slot="props"
						:error-messages="errors.source"
						:url="Symbols.MT4.Lookup.URL"
					>
						<!-- :map-items="(items: Symbols.MT4.Lookup.GETResponse) => {
							return items.map((item) => {
								(item as any).props = { subtitle: item.subtitle}
								return item
							})
						}" -->
						<v-autocomplete
							v-model="item.source"
							v-bind="props"
							:label="$t('Symbols.MT4.Crud.Symbol.source')"
							clearable
						/>
					</api-items>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-autocomplete
						v-model="item.digits"
						v-mask="'#'"
						:items="Array.from({ length: 5 }, (_, i) => i + 1)"
						:error-messages="errors.digits"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT4.Crud.Symbol.digits')"
					/>
				</v-col>

				<v-col cols="12">
					<v-text-field
						v-model="item.description"
						:error-messages="errors.description"
						:label="$t('Symbols.MT4.Crud.Symbol.description')"
					/>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<api-items
						v-slot="props"
						:url="Symbols.MT4.Type.Lookup.URL"
					>
						<v-autocomplete
							v-model="item.type"
							v-bind="props"
							:label="$t('Symbols.MT4.Crud.Symbol.type')"
						/>
					</api-items>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.exemode"
						:label="$t('Symbols.MT4.Crud.Symbol.execution')"
						:items="executionModeItems"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<api-items
						v-slot="props"
						url="/currencies/lookup"
					>
						<v-select
							v-model="item.currency"
							v-bind="props"
							:rules="rules({ required: true })"
							item-title="symbol"
							:label="$t('Symbols.MT4.Crud.Symbol.currency')"
						/>
					</api-items>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.trade"
						:label="$t('Symbols.MT4.Crud.Symbol.trade')"
						:items="tradeModeItems"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<background-color
						v-model:background="item.backgroundColor"
						:error-messages="errors.backgroundColor"
						:label="$t('Symbols.MT4.Crud.Symbol.background')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<api-items
						v-slot="props"
						url="/currencies/lookup"
					>
						<v-select
							v-bind="props"
							v-model="item.marginCurrency"
							:error-messages="errors.marginCurrency"
							item-title="symbol"
							:label="$t('Symbols.MT4.Crud.Symbol.margin-currency')"
						/>
					</api-items>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="instantMaxVolume"
						v-mask="Mask.Integer"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT4.Crud.Symbol.max-lots-for-ie')"
					/>
				</v-col>

				<v-col
					cols="12"
					md="8"
				>
					<v-select
						v-model="item.gtcPendings"
						:items="GTCModeItems"
						:label="$t('Symbols.MT4.Crud.Symbol.orders')"
					/>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.stopsLevel"
						v-mask="Mask.Integer"
						:error-messages="errors.stopsLevel"
						:label="$t('Symbols.MT4.Crud.Symbol.limit-and-stop-level')"
						suffix="pt"
						:rules="rules({ required: true })"
					/>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.freezeLevel"
						v-mask="Mask.Integer"
						:rules="rules({ required: true })"
						:error-messages="errors.freezeLevel"
						:label="$t('Symbols.MT4.Crud.Symbol.freeze-level')"
						suffix="pt"
					/>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-checkbox
						v-model="item.longOnly"
						color="primary"
						hide-details
						:label="$t('Symbols.MT4.Crud.Symbol.long-only')"
						:true-value="1"
						:false-value="0"
					/>
				</v-col>

				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.spread"
						:rules="rules({ required: true })"
						:error-messages="errors.spread"
						:label="$t('Symbols.MT4.Crud.Symbol.spread-by-default')"
						suffix="pt"
					/>
				</v-col>
				<v-col
					cols="12"
					md="8"
				>
					<div class="d-flex justify-space-between">
						<v-label>
							{{ $t('Symbols.MT5.Crud.Common.spread-balance') }}
						</v-label>
						<span class="text-caption">
							{{ $t('Symbols.MT5.Crud.Common.item-spreadbalance-spread', [item.spreadBalance - spreadBalanceBid, item.spreadBalance + spreadBalanceAsk]) }}	</span>
					</div>
					<v-slider
						v-model.number="item.spreadBalance"
						:error-messages="errors.spreadBalance"
						step="1"
						class="mx-0"
						:min="-15"
						:max="15"
						color="transparent"
						thumb-color="primary"
						track-color="primary"
						thumb-label
					/>
					<!-- <v-slider
						v-model="item.spreadBalance"
						:error-messages="errors.spreadBalance"
						step="1"
						:label="$t('Symbols.MT4.Crud.Symbol.spread-balance')"
						:min="-15"
						:max="15"
						color="transparent"
						thumb-color="primary"
						track-color="primary"
						thumb-label
					>
						<template #append>
							<v-sheet width="120" class="text-center text-body-2">
								{{ $t('Symbols.MT4.Crud.Symbol.item-spreadbalance-spread', [item.spreadBalance - spreadBalanceBid, item.spreadBalance + spreadBalanceAsk]) }}
							</v-sheet>
						</template>
					</v-slider> -->
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Mask } from '~/types/Mask'
import { Symbols } from '~/types'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const executionModeItems = enumToItems(Symbols.MT4.ExecutionMode, 'Symbols.MT4.ExecutionMode')

const tradeModeItems = enumToItems(Symbols.MT4.TradingMode, 'Symbols.MT4.TradingMode')

const GTCModeItems = enumToItems(Symbols.MT4.GTCMode, 'Symbols.MT4.GTCMode')

const spreadBalanceBid = computed(() => Math.floor((p.item.spread || 0) / 2))

const spreadBalanceAsk = computed(() => p.item.spread - spreadBalanceBid.value)

const instantMaxVolume = computed({
	get: () => p.item.instantMaxVolume / 100,
	set: (value: number) => emit('update:item', {
		instantMaxVolume: value * 100,
	}),
})
</script>
