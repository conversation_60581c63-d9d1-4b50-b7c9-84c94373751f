import { defineStore } from 'pinia'
import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'
import type { OnlineTraders } from '~/types'

export enum ConnectionStatus {
	Connected = 1,
	Disconnected = 0,
	Connecting = 2,
}

type State = {
	socket: Socket<OnlineTraders.Response, OnlineTraders.Request> | null
	data: OnlineTraders.SocketData
	serverName: string
	hasSocketEmitted: boolean
	connectionAttempts: number
	connectionStatus: ConnectionStatus
}

const log = useLog('OnlineSessions', {
	namespaceStyle: {
		backgroundColor: '#00ACC1',
	},
})
export const useOnlineSessionsStore = defineStore({
	id: 'onlineSessionsStore',
	state: (): State => ({
		socket: null,
		data: {
			channel: '',
			totalSessions: 0,
			data: [],
		},
		serverName: '',
		hasSocketEmitted: false,
		connectionAttempts: 0,
		connectionStatus: ConnectionStatus.Disconnected,
	}),
	actions: {
		connect() {
			if (this.socket) {
				return this
			} else {
				this.disconnect()
			}

			// return

			const config = useRuntimeConfig()

			const socketUrl = new URL('/online-sessions', config.public.socketUrl)

			const authStore = useAuthStore()

			this.socket = io(socketUrl.href, {
				// withCredentials: true,
				extraHeaders: {
					token: `${authStore.accessToken}`,
				},
			})

			this.socket.on('connect', () => {
				this.connectionStatus = ConnectionStatus.Connected
			})

			this.socket.on('disconnect', () => {
				this.connectionStatus = ConnectionStatus.Disconnected
				this.resetData()
				this.serverName = ''
				this.connectionAttempts = 0
			})

			this.socket.io.on('reconnect_attempt', (attempt) => {
				this.connectionAttempts = attempt
				this.connectionStatus = ConnectionStatus.Connecting
				this.hasSocketEmitted = false
			})

			this.socket.on('connect_error', (error) => {
				log('connect_error', error)
				// errorSnackbar({
				// 	text: 'Connection Error',
				// 	timeout: 2000,
				// })
			})
			useGlobalEvents().on('auth:invalidated', () => {
				this.disconnect()
			})

			return this
		},
		to(serverName: string) {
			if (!serverName) {
				log('serverName is empty')
				return
			}
			if (this.socket) {
				if (this.serverName === serverName) {
					return
				}

				if (this.serverName) {
					this.socket.emit('leave', this.serverName)
				}
				// this.socket.on('connect', () => {
				this.serverName = serverName
				this.resetData()
				this.socket!
					.emit('get', this.serverName)
					.on('update', (data) => {
						log('Data Received', data)
						this.data = data
						this.hasSocketEmitted = true
					})
				// })
			}
		},
		disconnect() {
			if (this.socket) {
				this.socket.disconnect()
				this.socket = null
			}
			return this
		},
		refresh() {
			const { serverName } = this
			this.disconnect().connect().to(serverName)
		},
		resetData() {
			this.data = {
				channel: '',
				totalSessions: 0,
				data: [],
			}
			this.hasSocketEmitted = false
		},
	},
})
