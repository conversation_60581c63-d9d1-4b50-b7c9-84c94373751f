<template>
	<v-container>
		<v-toolbar
			color="surface"
			density="compact"
		>
			<v-spacer />
			<v-btn
				prepend-icon="i-mdi:plus"
				@click="editRecord(-1)"
			>
				{{ $t('Manager.MT5.Crud.IPAccessList.add-record') }}
			</v-btn>
		</v-toolbar>
		<v-table>
			<thead>
				<tr>
					<th>
						{{ $t('Manager.MT5.Crud.IPAccessList.from') }}
					</th>
					<th>
						{{ $t('Manager.MT5.Crud.IPAccessList.to') }}
					</th>
					<th style="width: 30px" />
				</tr>
			</thead>
			<tbody>
				<tr
					v-for="record, index in item.accesses"
					:key="index"
				>
					<td>
						{{ record.from }}
					</td>
					<td>
						{{ record.to }}
					</td>
					<td>
						<div class="d-flex">
							<v-btn
								variant="text"
								size="small"
								icon="i-mdi:trash-can"
								@click="deleteRecord"
							/>
							<v-btn
								variant="text"
								size="small"
								icon="i-mdi:pencil"
								@click="editRecord(index)"
							/>
						</div>
					</td>
				</tr>
				<tr v-if="!item.accesses?.length">
					<td
						colspan="3"
						class="text-center"
					>
						{{ $t('$vuetify.noDataText') }}
					</td>
				</tr>
			</tbody>
		</v-table>
		<v-dialog
			v-model="dialogModel"
			max-width="400"
		>
			<v-confirm-edit
				v-slot="{ actions, model }"
				v-model="editItem"
				cancel-text="Reset"
				@save="save"
			>
				<v-form ref="accessFormRef">
					<v-card :title="$t('Manager.MT5.Crud.IPAccessList.access-list')">
						<template #text>
							<v-text-field
								v-model="model.value.from"
								v-mask="Mask.IP"
								autofocus
								:label="$t('Manager.MT5.Crud.IPAccessList.from')"
								placeholder="##.##.##.##"
								:rules="rules({ type: 'pattern', pattern: REGEX.IP, message: 'Value is not a valid IP Address' })"
							/>
							<v-text-field
								v-model="model.value.to"
								v-mask="Mask.IP"
								:label="$t('Manager.MT5.Crud.IPAccessList.to')"
								placeholder="##.##.##.##"
								:rules="rules({ type: 'pattern', pattern: REGEX.IP, message: 'Value is not a valid IP Address' })"
							/>
						</template>
						<template #actions>
							<v-btn @click="dialogModel = false">
								{{ $t('Manager.MT5.Crud.IPAccessList.cancel') }}
							</v-btn>
							<v-spacer />
							<component :is="actions" />
						</template>
					</v-card>
				</v-form>
			</v-confirm-edit>
		</v-dialog>
	</v-container>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Mask } from '~/types/Mask'
import type { Manager } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { t } = useI18n()

const dialogModel = ref(false)

const editItem = ref<Manager.MT5.IpAddressRange>({
	from: '',
	to: '',
})

const confirm = useNuxtApp().$confirm

const accessFormRef = ref<InstanceType<typeof VForm> | null>(null)

const currentIndex = ref(-1)

const deleteRecord = (index: number) => {
	confirm(t('Manager.MT5.Crud.IPAccessList.are-you-sure-you-want-to-delete'))
		.then(() => {
			const access = useCloned(p.item.accesses, { deep: true }).cloned.value
			access.splice(index, 1)
			emit('update:item', {
				accesses: access,
			})
		})
		.catch(() => {})
}

const editRecord = (index: number) => {
	currentIndex.value = index
	if (index === -1) {
		editItem.value = {
			from: '',
			to: '',
		}
	} else {
		editItem.value = p.item.accesses[index]
	}
	dialogModel.value = true
}

const save = async (updatedAccess: Manager.MT5.IpAddressRange) => {
	const validation = await accessFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	if (currentIndex.value === -1) {
		emit('update:item', {
			accesses: [...p.item.accesses, updatedAccess],
		})
	} else {
		const access = useCloned(p.item.accesses).cloned.value

		access[currentIndex.value] = updatedAccess

		emit('update:item', { accesses: access })
	}

	dialogModel.value = false
}
</script>
