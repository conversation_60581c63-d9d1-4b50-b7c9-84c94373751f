<template>
	<v-lazy min-height="40">
		<v-list-group
			:value="item.value"
			expand-icon="i-mdi:plus"
			collapse-icon="i-mdi:minus"
			subgroup
			style="--prepend-width: 12px"
			class="flex-grow-1"
		>
			<template #activator="{ props, isOpen }">
				<v-list-item
					:title="item.title"
					class="px-1"
					@click="selectPath(item)"
				>
					<template #prepend>
						<v-btn
							:disabled="!item.children.length"
							:ripple="false"
							variant="text"
							size="x-small"
							:icon="isOpen?'i-mdi:minus':'i-mdi:plus'"
							v-bind="props"
							:class="{ 'opacity-0': !item.children.length }"
						/>
						<v-icon :icon="item.type === 'path'?'i-mdi:folder text-yellow-darken-1':'i-ph:currency-circle-dollar'" />
					</template>
					<template #title="{ title }">
						{{ title }}
					</template>
				</v-list-item>
			</template>

			<PathTreeItem
				v-for="(child, i) in item.children"
				:key="i"
				:item="child"
				@click.stop="selectPath(child)"
				@path-select="selectPath"
			/>
		</v-list-group>
	</v-lazy>
</template>

<script lang="ts" setup>
// import PathTreeItem from './PathTreeItem.vue'
import type { Symbols } from '~/types'

type Props = {
	item: Symbols.MT5.Lookup.SingleRecord
}

type Emit = {
	(e: 'path-select', value: Symbols.MT5.Lookup.SingleRecord): void
}

defineProps<Props>()

const emit = defineEmits<Emit>()

const selectPath = (value: Symbols.MT5.Lookup.SingleRecord) => {
	emit('path-select', value)
}
</script>
