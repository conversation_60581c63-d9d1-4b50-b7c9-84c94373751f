<template>
	<v-container>
		<fieldset>
			<legend>
				{{ $t('Accounts.MT4.Crud.Security.check-password') }}
			</legend>
			<v-form ref="checkPasswordFormRef">
				<p class="mb-4 text-medium-emphasis">
					{{ $t('Accounts.MT4.Crud.Security.checking-password-allows-') }}
				</p>
				<password
					v-model="checkPasswordModel.password"
					:error-messages="checkPasswordErrors.password"
					:rules="rules({ required: true })"
					:label="$t('Accounts.MT4.Crud.Security.password')"
					autocomplete="off"
				>
					<template #append>
						<v-btn
							:loading="isCheckingPassword"
							variant="text"
							color="primary"
							density="default"
							@click="checkPassword"
						>
							{{ $t('Accounts.MT4.Crud.Security.check-password-0') }}
						</v-btn>
					</template>
				</password>
			</v-form>
		</fieldset>

		<fieldset>
			<legend>
				{{ $t('Accounts.MT4.Crud.Security.change-password') }}
			</legend>
			<v-form ref="changePasswordFormRef">
				<div class="text-medium-emphasis">
					<p class="mb-2">
						{{ $t('Accounts.MT4.Crud.Security.you-can-change-master-or-') }}
					</p>

					<p class="mb-4">
						{{ $t('Accounts.MT4.Crud.Security.you-can-also-disable-one-') }}
					</p>
				</div>
				<v-checkbox
					v-model="changePasswordModel.cleanPubkey"
					:true-value="1"
					:false-value="0"
					color="primary"
					:label="$t('Accounts.MT4.Crud.Security.unbind-account-from-one-t')"
					hide-details
					class="compact"
				/>
				<v-checkbox
					v-model="changePasswordModel.changeInvestor"
					:true-value="1"
					:false-value="0"
					color="primary"
					:label="$t('Accounts.MT4.Crud.Security.change-read-only-investor')"
					class="compact"
				/>
				<password
					v-model="changePasswordModel.password"
					v-model:strength="passwordStrength"
					:label="$t('Accounts.MT4.Crud.Security.new-password')"
					:rules="[rule({ required: true }), strengthRule]"
					show-strength
					autocomplete="off"
					:min-length="5"
					:error-messages="changePasswordErrors.password"
				>
					<template #append>
						<v-btn
							:loading="isChangingPassword"
							variant="text"
							color="primary"
							density="default"
							@click="changePassword"
						>
							{{ $t('Accounts.MT4.Crud.Security.change-password-0') }}
						</v-btn>
					</template>
				</password>
			</v-form>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const { t } = useI18n()

const { $api } = useNuxtApp()

const { successSnackbar } = useSnackbar()

const checkPasswordModel = reactive<Accounts.MT4.CheckPassword.POSTRequest>({
	login: p.item.login,
	password: '',
})

const isCheckingPassword = ref(false)

const checkPasswordFormRef = ref<InstanceType<typeof VForm>>()

const { errors: checkPasswordErrors, handler: checkPasswordHandler } = useFormHandler(checkPasswordModel)

const checkPassword = async () => {
	const validation = await checkPasswordFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	await $api(Accounts.MT4.CheckPassword.URL, {
		method: 'POST',
		body: checkPasswordModel,
	})
		.then(() => {
			successSnackbar(t('Accounts.MT4.Crud.Security.password-verified'))
		})
		.catch(checkPasswordHandler)
}

const changePasswordModel = reactive<Accounts.MT4.ChangePassword.POSTRequest>({
	login: p.item.login,
	password: '',
	changeInvestor: 0,
	cleanPubkey: 0,
})

const isChangingPassword = ref(false)

const changePasswordFormRef = ref<InstanceType<typeof VForm>>()

const { errors: changePasswordErrors, handler: changePasswordHandler } = useFormHandler(changePasswordModel)

const passwordStrength = ref(0)

const strengthRule = () => (passwordStrength.value === 100 || t('Accounts.MT4.Crud.Security.password-must-be-at-least-5-characters-long'))

const changePassword = async () => {
	const validation = await changePasswordFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	await $api(Accounts.MT4.ChangePassword.URL, {
		method: 'POST',
		body: changePasswordModel,
	})
		.then(() => {
			successSnackbar(t('Accounts.MT4.Crud.Security.password-changed'))
		})
		.catch(changePasswordHandler)
}
</script>
