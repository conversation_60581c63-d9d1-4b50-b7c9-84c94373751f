import type { Socket } from 'socket.io-client'
import { WebSocketService } from './Base'
import type { ClientToServerEvents, ServerToClientEvents } from '~/types/Common/Socket.IO'

export type UpdateHandler = (data: SocketData) => void

export interface SocketData {
	Daily: number
	Weekly: number
	Monthly: number
}
export interface Response extends ServerToClientEvents {
	update: UpdateHandler
}

export interface Request extends ClientToServerEvents {}

export class PnlWithHedging extends WebSocketService<Socket<Response, Request>> {
	constructor() {
		super('pnl-with-hedging')
	}

	onUpdate(callback: UpdateHandler) {
		this.socket?.on('update', callback)
		return this
	}
}
