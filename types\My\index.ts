export namespace My {
	export namespace Actions {
		export const URL = '/my/actions'

		export interface GETResponse {
			id: number
			comment: string
			displayName: string
			description: string
			onlyReceiver: boolean
			actionTypeId: number
			actionType: {
				id: number
				name: null
				displayName: null
				isIncremental: boolean
				operationId: number
				operation: null
				createdAt: null
				updatedAt: null
			}
			createdAt: null
			updatedAt: null
		}

	}
	export namespace ActionTypes {
		export const URL = '/my/actions'

		export interface GETResponse {
			id: number
			name: string
			displayName: string
			isIncremental: boolean
			operationId: number
			operation: {
				id: number
				name: null
				displayName: null
				type: null
				tradingPlatformId: null
				tradingPlatform: null
				createdAt: string
				updatedAt: null
			}
			createdAt: null
			updatedAt: null
		}

	}

	export namespace Preferences {
		export const URL = '/my/preferences'
		export interface SingleRecord {
			id: number
			key: string
			value: any
		}

		export type GETResponse = SingleRecord[]
	}

}
