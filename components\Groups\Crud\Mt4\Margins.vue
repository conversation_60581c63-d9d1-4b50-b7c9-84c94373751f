<template>
	<v-container>
		<v-row no-gutters>
			<v-col
				cols="12"
				md="4"
				class="pe-md-1"
			>
				<v-text-field
					v-model="item.marginCallLevel"
					:error-messages="errors.marginCallLevel"
					:label="$t('Groups.MT4.Crud.Margins.margin-call-level')"
					:rules="rules({ required: true })"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
				class="px-md-1"
			>
				<v-text-field
					v-model="item.marginStopOut"
					:error-messages="errors.marginStopOut"
					:label="$t('Groups.MT4.Crud.Margins.stop-out-level')"
					:rules="rules({ required: true })"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
				class="ps-md-1"
			>
				<v-select
					v-model="item.marginType"
					:error-messages="errors.marginType"
					:items="marginTypeItems"
				/>
			</v-col>
		</v-row>
		<v-select
			v-model="item.marginMode"
			:error-messages="errors.marginMode"
			:items="marginModeItems"
			:label="$t('Groups.MT4.Crud.Margins.free-margin')"
		/>
		<v-text-field
			v-model="item.credit"
			:error-messages="errors.credit"
			:label="$t('Groups.MT4.Crud.Margins.virtual-credit')"
			:hint="$t('Groups.MT4.Crud.Margins.applies-only-to-opening-n')"
		/>
		<v-defaults-provider
			:defaults="{
				global: {
					trueValue: 1,
					falseValue: 0,
					color: 'success',
					hideDetails: true,
				} }"
		>
			<v-switch
				v-model="item.stopoutSkipHedged"
				:label="$t('Groups.MT4.Crud.Margins.skip-fully-hedged-account')"
			/>
			<v-switch
				v-model="item.hedgeLargeLeg"
				:label="$t('Groups.MT4.Crud.Margins.calculate-hedged-margin-u')"
			/>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const marginTypeItems = enumToItems(Groups.MT4.MarginType, 'Groups.MT4.MarginType')

const marginModeItems = enumToItems(Groups.MT4.MarginMode, 'Groups.MT4.MarginMode')
</script>
