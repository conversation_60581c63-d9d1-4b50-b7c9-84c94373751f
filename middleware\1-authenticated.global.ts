import { minimatch } from 'minimatch'

const ignorePatterns = ['/auth/**']

const log = useLog('Authenticated Middleware', {
	namespaceStyle: {
		backgroundColor: '#009688',
	},
})

export default defineNuxtRouteMiddleware(async (to, from) => {
	const authStore = useAuthStore()

	// Ignore the middleware if the route is in the ignorePatterns
	if (ignorePatterns.some(pattern => minimatch(to.path, pattern))) {
		log('Ignoring', to.path)
		return
	}
	// If the from route does not have a name, then we are coming from an external source
	if (from.name === undefined) {
		if (authStore.hasToken || authStore.hasRefreshToken) {
			return await navigateTo({ name: 'auth-logging', hash: '#' + to.fullPath })
		} else {
			return await navigateTo({ name: 'auth-login', hash: '#' + to.fullPath })
		}
	}

	if (!authStore.isLoggedIn) {
		log('Not logged in', to.path)
		const router = useRouter()

		const loginPath = router.resolve({ name: 'auth-login' }).path
		// if the current route not login, redirect to login
		if (to.path !== loginPath) {
			await router.replace(loginPath)
		}
	}
})
