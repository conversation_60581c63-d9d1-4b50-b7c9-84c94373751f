import { defu } from 'defu'

export default defineNuxtPlugin(() => {
	const config = useRuntimeConfig()
	if (config.public.devTools) {
		const publicConfig = useCloned(config.public).cloned.value
		const defaults = useLocalStorage('defaults', publicConfig)
		defaults.value = publicConfig // make sure to always have the latest public config if storage exists
		const storage = useLocalStorage('devTools', config.public)
		config.public = defu(storage.value, config.public)
	}
})
