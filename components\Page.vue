<template>
	<div>
		<Title v-if="title">
			{{ title }}
		</Title>
		<v-sheet
			color="background"
			class="flex-grow-1 w-100 h-100 position-relative"
		>
			<v-card
				flat
				class="h-100"
				border
			>
				<v-container
					ref="pageTopRef"
					fluid
				>
					<v-row align="start">
						<v-col
							cols="12"
							md="auto"
						>
							<slot name="title">
								<div
									v-if="title"
									class="mb-1 text-h4"
								>
									{{ title }}
								</div>
								<div />
							</slot>
							<v-card-subtitle class="ps-0">
								<v-breadcrumbs
									class="pt-0 ps-0"
									:items="breadcrumbItems"
									density="compact"
								>
									<template #prepend>
										<nuxt-link
											:to="{ name: 'index' }"
											class="text-inherit text-decoration-none"
										>
											<v-icon
												start
												icon="i-mdi:home"
												size="small"
											/>
										</nuxt-link>
									</template>
								</v-breadcrumbs>
							</v-card-subtitle>
						</v-col>
						<v-col
							cols="12"
							md=""
							class="text-end action-slot-wrapper"
						>
							<slot name="actions" />
						</v-col>
					</v-row>
				</v-container>
				<v-card-text
					class="pt-0"
					:style="{ '--page-top-height': `${pageTopHeight}px` }"
				>
					<slot>
						<p>Page Content Goes Here</p>
					</slot>
				</v-card-text>
				<v-card-actions v-if="$slots['bottom-actions']">
					<slot name="bottom-actions" />
				</v-card-actions>
			</v-card>
		</v-sheet>
	</div>
</template>

<script lang="ts" setup>
import { VBreadcrumbs, VCard, VContainer } from 'vuetify/components'

const pageTopRef = ref<InstanceType<typeof VContainer> | null>(null)

const pageTopHeight = computed(() => {
	if (pageTopRef.value) {
		return pageTopRef.value.$el.clientHeight
	}

	return 0
})

const router = useRouter()
const { t } = useI18n()
const props = defineProps({
	title: {
		type: String,
		required: false,
		default: '',
	},
	breadcrumb: {
		type: Array as PropType<VBreadcrumbs['$props']['items']>,
		required: false,
		default: () => ([]),
	},
})

const route = useRoute()

// calculate breadcrumb based on href
const breadcrumbItems = computed<VBreadcrumbs['$props']['items']>(() => {
	if (props.breadcrumb?.length) {
		return props.breadcrumb
	}

	const path = route.matched[0].path
	const paths = path.split('/').filter((p, i) => (p && i > 0) && !p.includes(':'))

	// Capitalize the first letter of each path segment and replace the first segment with "Home"
	// const items = paths.map((p, index) => index === 0 ? 'Home' : p.charAt(0).toUpperCase() + p.slice(1))

	const formattedPaths = paths.map((path, index) => {
		const segmentPath = `/${paths.slice(0, index + 1).join('/')}`
		// Check if the generated path exists in routes
		const routeExists = checkRouteExists(segmentPath)

		return {
			title: t(`breadcrumb.${path}`),
			to: routeExists ? (segmentPath) : undefined,
		}
	})

	formattedPaths.unshift({
		title: t('breadcrumb.home'),
		to: router.resolve({ name: 'index' }).path,
	})

	return formattedPaths
})

function checkRouteExists(path: string) {
	const originalWarn = console.warn
	console.warn = () => {}
	const resolvedRoute = router.resolve(path)
	console.warn = originalWarn
	return !!resolvedRoute.matched.length
}
</script>

<style lang="scss">
.action-slot-wrapper{
	.v-btn ~ .v-btn{
		margin-inline-start: 8px;
	}
}
</style>
