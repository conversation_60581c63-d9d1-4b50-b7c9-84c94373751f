<template>
	<v-skeleton-loader
		type="text"
		:loading="isLoading"
		min-width="6px"
	>
		<div>
			<span>{{ value }}</span>
			<div
				v-if="item?.error"
				class="d-flex align-center text-error"
			>
				<span class="me-1">{{ $t('DealsMinMaxPrice.error') }}</span> <v-btn
					color="error"
					icon="i-mdi:refresh"
					size="x-small"
					variant="text"
					@click="getData"
				/>
			</div>
		</div>
	</v-skeleton-loader>
</template>

<script lang="ts" setup>
import type { Deals } from '~/types'

type Props = {
	itemKey: keyof Deals.SymbolPrices.GETResponse
	symbol: string
	from: string
	to: string
	action: string | number
	readonly?: boolean
}

const p = withDefaults(defineProps<Props>(), {
	readonly: false,
})

const dealsStore = useDealsStore()

const value = computed(() => {
	return item.value?.[p.itemKey]
})

const item = computed(() => {
	return dealsStore.get(key.value) || undefined
})

const isLoading = computed(() => dealsStore.isLoading(key.value))

const key = computed(() => dealsStore.getKey(p.symbol, p.from, p.to, p.action))

const getData = () => {
	dealsStore.getMinMax(p.symbol, p.from!, p.to!, p.action!)
}

if (p.readonly === false) {
	getData()
}
</script>
