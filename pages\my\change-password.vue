<template>
	<v-card class="py-8">
		<div class="d-flex justify-center align-center">
			<v-form v-slot="{ isValid }">
				<v-card
					flat
					max-width="600"
					class="flex-grow-1"
				>
					<v-card-title>
						{{ $t('change-password.title') }}
					</v-card-title>

					<v-card-subtitle>
						<div v-html="$t('change-password.secure-your-account-description', { br: '<br/>' })" />
					</v-card-subtitle>
					<v-card-text>
						<password
							v-model="form.password"
							label="Password"
							show-strength
							:error-messages="errors.password"
							:rules="rules({ required: true })"
						/>
						<password
							v-model="form.confirmPassword"
							label="Confirm Password"
							:error-messages="errors.confirmPassword"
							:rules="rules({ required: true, type: 'enum', enum: [form.password], message: $t('change-password.password-does-not-match') })"
						/>
					</v-card-text>

					<v-card-actions>
						<v-btn :to="{ name: 'index' }">
							{{ $t('change-password.cancel') }}
						</v-btn>
						<v-spacer />
						<v-btn
							color="primary"
							:loading="isChanging"
							:disabled="!isValid.value"
							@click="changePasswordHandler"
						>
							{{ $t('change-password.save') }}
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-form>
		</div>
	</v-card>
</template>

<script lang="ts" setup>
import type { Auth } from '~/types'

const { t } = useI18n()

const { changePassword } = useAuthStore()

const { successSnackbar } = useSnackbar()

const form = reactive<Auth.ChangePassword.POSTRequest>({
	password: '',
	confirmPassword: '',
})

const isChanging = ref(false)

const router = useRouter()

const { errors, handler } = useFormHandler(form)

const changePasswordHandler = () => {
	isChanging.value = true

	changePassword(form)
		.then(() => {
			successSnackbar(t('change-password.password-changed'))
			router.push({ name: 'index' })
		})
		.catch(handler)
		.finally(() => {
			isChanging.value = false
		})
}
</script>
