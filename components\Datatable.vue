<template>
	<v-sheet
		ref="container"
		:style="{
			'--datatable-footer-height': (dataTableProps.hideDefaultFooter? '0px' : '62px'),
		}"
	>
		<v-navigation-drawer
			v-model="navigationDrawerModel"
			temporary
			width="300"
			order="2"
			border
			elevation="0"
			:style="computedFilterStyle"
			class="rounded-ts-lg"
			:persistent="$p.persistentFilter"
		>
			<template #prepend>
				<v-card-title class="d-flex">
					{{ $t('Datatable.filter') }}	<v-spacer />
					<v-btn
						icon="i-mdi:close"
						variant="text"
						size="small"
						@click="closeFilter"
					/>
					<!-- v-if="!$p.persistentFilter" -->
				</v-card-title>
			</template>
			<v-lazy
				:model-value="lazyFilterModel"
				:transition="{ appear: false, name: '' }"
				@update:model-value="(isShown) => { lazyFilterModel = true }"
			>
				<keep-alive>
					<v-card-text v-if="navigationDrawerModel">
						<v-form
							ref="filterFormRef"
							v-slot="{ isValid }"
						>
							<slot
								name="filter"
								v-bind="{ model: filterModel, isValid }"
							/>
						</v-form>
					</v-card-text>
				</keep-alive>
			</v-lazy>
			<v-spacer />
			<template #append>
				<v-card-actions>
					<v-btn
						v-if="!$p.persistentFilter"
						@click="resetFilter"
					>
						{{ $t('Datatable.reset') }}
					</v-btn>
					<v-spacer />
					<v-btn
						color="primary"
						@click="filterHandler(filterModel)"
					>
						{{ $t('Datatable.apply') }}
					</v-btn>
				</v-card-actions>
			</template>
		</v-navigation-drawer>
		<div class="d-flex fill-height">
			<slot
				name="prepend"
				v-bind="slotsProps"
			/>

			<component
				:is="renderComponent"
				ref="table"
				v-bind="dataTableProps"
				v-model="model"
				:items-length="dataTableProps.itemsLength || 0"
				v-on="$on.datatable"
			>
				<template #top="{ }">
					<v-toolbar
						color="transparent"
						density="compact"
					>
						<slot name="toolbar.prepend" />
						<v-text-field
							v-if="!p.noSearch"
							v-model="searchModel"
							v-bind="searchFieldProps"
							@keydown.esc="searchModel = null"
						/>
						<v-badge
							v-if="hasFilter"
							color="warning"
							:model-value="hasQuery"
							dot
						>
							<v-btn
								v-bind="filterBtnProps"
								@click="showFilter"
							/>
						</v-badge>
						<slot name="toolbar.middle-start" />
						<v-spacer />
						<slot name="toolbar.middle-end" />
						<slot
							name="toolbar.column-Visibility"
							v-bind="{ items: columnVisibilityItems }"
						>
							<v-badge
								dot
								color="warning"
								offset-x="6"
								offset-y="6"
								:model-value="hasHiddenHeaders"
							>
								<v-btn
									ref="columnVisibilityBtnRef"
									v-tooltip:top="'Columns'"
									icon
									i-mdi:view-column
									:color="$p.btnsColor"
								>
									<v-menu
										activator="parent"
										close-on-back
										:close-on-content-click="false"
									>
										<v-list
											v-model:selected="hiddenHeadersStorageState"
											density="compact"
											slim
											select-strategy="classic"
											class="text-start"
										>
											<div class="d-flex flex-nowrap align-center">
												<v-list-subheader>
													{{ t('Datatable.columns') }}
												</v-list-subheader>
												<v-spacer />
												<v-btn
													size="x-small"
													:text="t('Datatable.reset-to-default')"
													variant="text"
													color="tertiary"
													:disabled="isEqual(hiddenHeadersStorageState, defaultHeadersState)"
													@click="resetColumnVisibilityToDefault"
												/>
											</div>
											<v-divider />
											<v-list-item
												v-for="listItem in columnVisibilityMenuItems"
												:key="listItem.value"
												v-bind="listItem"
											>
												<template #append>
													<v-list-item-action v-if="listItem.value">
														<v-switch
															class="ms-8"
															hide-details
															density="compact"
															color="primary"
															:model-value="!listItem.hidden"
														/>
													</v-list-item-action>
												</template>
											</v-list-item>
										</v-list>
									</v-menu>
									<v-icon>i-mdi:view-column</v-icon>
								</v-btn>
							</v-badge>
						</slot>
						<v-btn
							v-tooltip:top="'Fullscreen'"
							:icon="isFullscreen? 'i-mdi:fullscreen-exit': 'i-mdi:fullscreen'"
							:color="$p.btnsColor"
							@click="toggleFullscreen"
						/>
						<slot
							name="toolbar.refresh"
							v-bind="{ refresh: resetAndRefresh, loading: pending }"
						>
							<v-btn
								v-tooltip:top="'Refresh'"
								:color="$p.btnsColor"
								:disabled="pending"
								icon="i-mdi:refresh"
								@click="resetAndRefresh"
							/>
						</slot>
					</v-toolbar>
				</template>
				<template #item.timestamp="{ item }">
					<div>
						<div class="cursor-alias">
							<div class="text-caption">
								{{ $t('Datatable.created-since-item-createdat', [since(item.createdAt)]) }}
							</div>
							<div
								v-if="item.updatedAt"
								class="text-caption"
							>
								{{ $t('Datatable.last-updated-since-item-updatedat', [since(item.updatedAt)]) }}
							</div>
						</div>
						<v-tooltip
							activator="parent"
							location="top"
							open-delay="300"
						>
							<div class="text-caption">
								<div><b>{{ $t('Datatable.created-at') }}</b> {{ dateTime(item.createdAt) }}</div>
								<div><b>{{ $t('Datatable.updated-at') }}</b> <span v-if="item.updatedAt"> {{ dateTime(item.updatedAt)|| 'None' }}</span> <span v-else>{{ $t('Datatable.never') }}</span></div>
							</div>
						</v-tooltip>
					</div>
				</template>
				<template #item.index="{ index }">
					{{ index + (internalItemsPerPage * (internalPage - 1)) + 1 }}
				</template>
				<!-- @ts-ignore -->
				<template
					v-for="(_, name) in ($slots as VDataTableServerSlots | {})"
					#[name]="slotData"
				>
					<slot
						:name="name"
						v-bind="{ ...slotData }"
					/>
				</template>
				<template
					v-if="!dataTableProps.items?.length"
					#loading
				>
					<v-skeleton-loader :type="`table-row@${Number($p.itemsPerPage) > 0? $p.itemsPerPage : 10}`" />
				</template>
				<!-- Display loading skeleton when scrolling downwards -->
				<template #body.append="{ columns }">
					<tr v-if="$p.lazyload && hasNext && !!lazyLoadedItems.length">
						<td
							:colspan="columns.length"
							class="text-center"
						>
							<!-- <v-progress-circular v-show="pending && !!lazyLoadedItems.length" indeterminate /> -->
							<div
								v-if="wrapper"
								v-intersect.quit="{
									handler: lazyloadNext,
									options: lazyloadOptions,
								}"
							/>
							<v-btn
								variant="text"
								block
								:loading="pending"
								@click="lazyloadNext(true)"
							>
								{{ $t('Datatable.load-more') }}
							</v-btn>
						</td>
					</tr>
					<tr v-if="!hasNext && !!lazyLoadedItems.length">
						<td
							:colspan="columns.length"
							class="text-center"
						>
							<slot name="end-of-data">
								{{ $t('Datatable.end-of-data') }}
							</slot>
						</td>
					</tr>
				</template>
			</component>

			<slot
				name="append"
				v-bind="slotsProps"
			/>
		</div>
		<!-- <pre>
				{{ dataTableProps }}
			</pre> -->
	</v-sheet>
</template>

<script lang="ts" setup>
import { VDataTableServer, VDataTable, VTextField, VBtn, VList, VSheet, VForm } from 'vuetify/components'
import dot from 'dot-object'
import { useStorage, refDebounced, useFullscreen } from '@vueuse/core'
// import { defu } from 'defu'
import type { FetchError } from 'ofetch'
import { DatatableDefaultsSymbol } from '../types/Datatable'
import type { ComponentProps } from '../types/Helpers'

export type Model = Record<string, any>
export type RequestQuery = {
	[key: string]: string | number | boolean | undefined
}

// Slots
export type VDataTableServerSlots = VDataTableServer['$slots']

export type ToolbarSlots = Readonly<{
	'toolbar.column-Visibility'(props: {
		items: any[]
	}): any
	'toolbar.refresh'(props: {
		loading: boolean
		refresh: Function
	}): any
	'toolbar.prepend'(props: {}): any
	'toolbar.middle-start'(props: {}): any
	'toolbar.middle-end'(props: {}): any
}>
export type FilterSlots = Readonly<{
	filter(props: {
		model: Model | any
	}): any
}>

export type TableSlots = Readonly<{
	'append'(props: { height: string | number | undefined }): any
	'prepend'(props: { height: string | number | undefined }): any
	'end-of-data'(props: {}): any
}>
export type Slots = VDataTableServerSlots & ToolbarSlots & FilterSlots & TableSlots
// End of Slots

// Props

// export type VDatatableProps = ComponentProps<typeof VDataTableServer> & {}
export type VDatatableProps = Omit<VDataTableServer['$props'], 'modelValue' | 'itemsLength' | 'items'>
type VHeaders = Exclude<VDatatableProps['headers'], undefined>
type VHeader = VHeaders[number]

export type Headers = (VHeader & {
	/**
	 * @property {boolean} [hidden=false]
	 * @default false
	 */
	hidden?: boolean
})[] | undefined

export interface Props extends /* @vue-ignore */ VDatatableProps {
	url?: string | undefined
	apiOptions?: Parameters<typeof useApi<any>>[1] | undefined
	itemsKey?: string | undefined
	itemsLength?: number | string | undefined
	itemsPerPageReqKey?: string | undefined
	itemsPerPageResKey?: string | undefined
	totalReqKey?: string | undefined
	totalResKey?: string | undefined
	pageReqKey?: string | undefined
	pageResKey?: string | undefined
	hasNextKey?: string | undefined
	noSearch?: boolean | undefined
	searchLabel?: string | undefined
	lazyload?: boolean | IntersectionObserverInit | undefined
	btnsColor?: string | undefined
	defaultModel?: Model | undefined
	syncQuery?: boolean | undefined
	persistentFilter?: boolean | undefined
	items?: any[] | undefined
	errorHandler?: (error: FetchError) => void
	headers?: Headers
}

type Emit = {
	(e: 'update:page', value: string | number): void
	(e: 'update:itemsPerPage', value: string | number): void
	(e: 'refresh'): void
	(e: 'filter', value: Ref<Record<string, any>>): void
}
const emit = defineEmits<Emit>()
const $on = {
	datatable: {
		'update:page': (value: string | number) => {
			if ($p.url) {
				addPaginationQuery(Number(value))
				resetAndRefresh()
			} else {
				wrapper.value?.scrollTo(0, 0)
			}
			internalPage.value = Number(value)

			emit('update:page', value)
		},
		'update:itemsPerPage': (value: string | number) => {
			if ($p.url) {
				appliedQuery.value[$p.itemsPerPageReqKey] = value
				resetAndRefresh()
			} else {
				wrapper.value?.scrollTo(0, 0)
			}
			internalItemsPerPage.value = Number(value)

			emit('update:itemsPerPage', value)
		},
	},
}

const model = defineModel<any[]>()

// End of Props
const slots = useSlots()

const { t } = useI18n()
// const router = useRouter()

const { errorSnackbar } = useSnackbar()

defineOptions({
	extends: VDataTableServer,
	inheritAttrs: false,
})

defineSlots<Slots>()
const d = inject(DatatableDefaultsSymbol) ?? {}
const p = withDefaults(
	defineProps<Props>()
	, {
		hasNextKey: 'hasNext',
		noSearch: false,
		url: '',
		apiOptions: () => ({}),
		itemsLength: 0,
		itemsPerPage: 'perPage',
		searchKey: 'search',
		searchLabel: undefined,
		lazyload: false,
		itemsKey: 'items',
		itemsPerPageReqKey: 'perPage',
		itemsPerPageResKey: 'perPage',
		totalReqKey: 'total',
		totalResKey: 'total',
		pageReqKey:	'page',
		pageResKey: 'page',
		btnsColor: undefined,
		defaultModel: () => ({}),
		syncQuery: true,
		items: undefined,
		persistentFilter: false,
		errorHandler: undefined,
	},
)

const instance = getCurrentInstance()

const providedProps: Record<string, any> = {}
const calculateProvidedProps = () => {
	const provided = instance?.vnode.props || {}
	for (const key in provided) {
		const camelKey = key.replace(/-(\w)/g, (_, c) => c.toUpperCase())
		providedProps[camelKey] = provided[key]
		if (provided[key] === '') {
			providedProps[camelKey] = true
		}
	}
}

calculateProvidedProps()

const $p = reactive(Object.assign({ ...p }, d, providedProps))

watchEffect(() => {
	calculateProvidedProps()
	const pTemp = (Object.assign({ ...p }, d, providedProps))
	for (const key in pTemp) {
		// @ts-ignore
		$p[key] = pTemp[key]
	}
})

const container = ref<typeof VSheet | null>(null)

const filterFormRef = ref<InstanceType<typeof VForm> | null>(null)

const table = ref<typeof VDataTableServer | null>(null)

const computedStorageKey = computed<string>(() => {
	return `datatable.headers:${$p.url || 'default'}`
})

const defaultHeadersState = computed<string[]>(() => {
	return ($p.headers as Headers)?.filter(header => !!header.hidden)?.map(header => header.value as string) || []
})

const hiddenHeadersStorageState = useStorage<string[]>(computedStorageKey.value, defaultHeadersState.value)

const searchModel = ref<string | null>(null)

const debouncedSearchModel = refDebounced<string | null>(searchModel, 300)

const { isFullscreen, toggle: toggleFullscreen } = useFullscreen(container as unknown as Ref<HTMLElement>)

const navigationDrawerModel = ref<boolean>(false)

const filterNullishValues = (obj: Record<any, any>) => {
	return Object.fromEntries(
		Object.entries(obj).filter(([key, value]) => ![undefined, null, ''].includes(value as any) && key !== 'undefined'),
	)
}

watch(() => hiddenHeadersStorageState.value, (value) => {
	hiddenHeadersStorageState.value = value
}, { deep: true })

const hasFilter = computed(() => {
	return !!slots.filter
})

const applyFilter = (model: any) => {
	for (const key in model) {
		if ([undefined, null].includes(model[key])) {
			delete appliedQuery.value[key]
		} else if (Array.isArray(model[key])) {
			if (model[key].length) {
				appliedQuery.value[key] = model[key].join(',')
			} else {
				delete appliedQuery.value[key]
			}
		} else {
			appliedQuery.value[key] = model[key]
		}
	}

	// remove anything not available in model
	for (const key in appliedQuery.value) {
		if (!(key in model)) {
			delete appliedQuery.value[key]
		}
	}
}

const addPaginationQuery = (page: number, itemsPerPage?: number) => {
	appliedQuery.value[$p.pageReqKey] = page
	appliedQuery.value[$p.itemsPerPageReqKey] = itemsPerPage || internalItemsPerPage.value
}

const resetFilter = () => {
	const clonedDefaultModel = useCloned($p.defaultModel)
	for (const key in filterModel) {
		if (clonedDefaultModel.cloned.value[key] !== undefined) {
			filterModel[key] = clonedDefaultModel.cloned.value[key]
		} else if (Array.isArray(filterModel[key])) {
			filterModel[key] = []
		} else if (typeof filterModel[key] === 'number') {
			filterModel[key] = null
		} else {
			filterModel[key] = ''
		}
		delete appliedQuery.value[key]
	}
	filterHandler(filterModel)
}

const clonedDefaultModel = useCloned($p.defaultModel)

const filterModel = reactive<Model>(clonedDefaultModel.cloned.value)

filterModel[$p.searchKey] = debouncedSearchModel.value

const appliedQuery = ref/* <RequestQuery & Model> */(filterNullishValues({
	[$p.pageReqKey as string]: $p.page || 1,
	[$p.itemsPerPageReqKey as string]: $p.itemsPerPage || 10,
}))

applyFilter(filterModel)
addPaginationQuery($p.page ? Number($p.page) : 1, Number($p.itemsPerPage))
const { data, status, refresh, error, execute } = useApi($p.url || '/', {
	immediate: false,
	watch: false,
	lazy: true,
	...$p.apiOptions,
	query: computed(() => {
		if ($p.url) {
			return {
				...$p.apiOptions?.query,
				...filterNullishValues(appliedQuery.value),
			}
		}
		return {}
	}),

	// onRequest(_req) {
	// 	// CANCEL REQUEST
	// 	// if (!$p.url) {
	// 	// 	return new Promise((_resolve, reject) => {
	// 	// 		reject(new Error('cancel'))
	// 	// 	})
	// 	// }

	// 	// return req
	// },
})

watch(() => debouncedSearchModel.value, (_value) => {
	if ($p.url) {
		filterModel[$p.searchKey] = _value
		applyFilter(filterModel)
		addPaginationQuery(1)
		resetAndRefresh()
	}
})

const pending = computed(() => status.value === 'pending')

const wrapper = computed(() => {
	return table.value?.$el.querySelector('.v-table__wrapper')
})

const lazyloadOptions = computed(() => {
	const defaults = {
		root: wrapper.value,
		rootMargin: '0px',
		threshold: 0,
	}

	if (typeof $p.lazyload === 'object') {
		return { ...defaults, ...$p.lazyload }
	}

	return defaults
})

const resetAndRefresh = () => {
	if ($p.lazyload) {
		resetLazyLoadedItems()
	}
	wrapper.value?.scrollTo(0, 0)
	if ($p.url) {
		refresh()
	}

	emit('refresh')
}

const showFilter = () => {
	navigationDrawerModel.value = true
}

const closeFilter = () => {
	navigationDrawerModel.value = false
}

const filterHandler = async (model: any) => {
	const validation = await filterFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	if ($p.lazyload) {
		resetLazyLoadedItems()
		// scroll to top
		wrapper.value?.scrollTo(0, 0)
	}

	if (debouncedSearchModel.value) {
		filterModel[$p.searchKey] = debouncedSearchModel.value
	}

	applyFilter({ ...model })

	addPaginationQuery(1)

	// sync with address bar
	// TODO: load address bar query onMounted
	if ($p.syncQuery) {
		// router.push({ query: appliedQuery.value })
	}
	if ($p.url) {
		execute()
	}

	closeFilter()
	emit('filter', appliedQuery)
}

const exposedFilter = (model: any) => {
	// const mergedModel = defu(model, filterModel, { ...appliedQuery.value })

	if ($p.lazyload) {
		resetLazyLoadedItems()
		// scroll to top
		wrapper.value?.scrollTo(0, 0)
	}

	applyFilter(model)

	addPaginationQuery(1)

	if ($p.url) {
		execute()
	}
}
const resetLazyLoadedItems = () => {
	lazyLoadedItems.value = []
	lazyLoadedPages.value = []
}
// const resetFilter = () => {
// 	const clonedDefaultModel = useCloned($p.defaultModel)
// 	for (const key in filterModel) {
// 		if (clonedDefaultModel.cloned.value[key] !== undefined) {
// 			filterModel[key] = clonedDefaultModel.cloned.value[key]
// 		} else if (Array.isArray(filterModel[key])) {
// 			filterModel[key] = []
// 		} else if (typeof filterModel[key] === 'number') {
// 			filterModel[key] = null
// 		} else {
// 			filterModel[key] = ''
// 		}
// 		delete appliedQuery.value[key]
// 	}
// 	filterFormRef.value?.resetValidation()

// 	if ($p.url) {
// 		refresh()
// 	}

// 	closeFilter()
// 	emit('filter', appliedQuery)
// }

const computedFilterStyle = computed(() => {
	// style="left:var(----v-layout-left + 16px);top:var(--v-layout-top + 16px);"
	return navigationDrawerModel.value
		? {
				marginTop: 'calc(17px + var(--2fa-banner-height))',
				marginInlineStart: '16px',
				paddingBottom: 'calc(17px + var(--2fa-banner-height))',

			}
		: {
				marginTop: 'calc(17px + var(--2fa-banner-height))',
				paddingBottom: 'calc(17px + var(--2fa-banner-height))',

			}
})

const lazyLoadedPages = ref<number[]>([])
const lazyLoadedItems = ref<any[]>([])
const internalItems = ref<any[]>([])
const internalPage = ref<number>(1)
const internalItemsPerPage = ref<number>(Number($p.itemsPerPage))

const dataTableProps = computed(() => {
	// @ts-ignore
	const props = VDataTableServer.filterProps($p)

	props.itemsLength =	$p.items?.length ?? 0

	props.headers = $p.headers?.filter(header => !hiddenHeadersStorageState.value.includes(header.value as string)) || []

	if ($p.url) {
		if ($p.itemsKey && typeof data.value === 'object') {
			const extractedItems = dot.pick($p.itemsKey, data.value)
			if (Array.isArray(extractedItems)) {
				// eslint-disable-next-line vue/no-side-effects-in-computed-properties
				internalItems.value = extractedItems
			}
		}

		if (Array.isArray(data?.value)) {
			// eslint-disable-next-line vue/no-side-effects-in-computed-properties
			internalItems.value = data.value
		}

		if ($p.lazyload) {
			props.items = lazyLoadedItems.value
			props.hideDefaultFooter = true
		} else {
			props.items = internalItems.value
			props.page = dot.pick(String($p.pageResKey), data.value) || $p.page // causes issues with lazyload
		}

		props.loading = pending.value

		if ($p.totalResKey) {
			props.itemsLength = dot.pick($p.totalResKey, data.value) || 0
		}

		props.itemsPerPage = dot.pick(String($p.itemsPerPageResKey), data.value) || $p.itemsPerPage

		// props.pageText = `${props.page || 0} of ${Math.ceil(Number(props.itemsLength) / Number(props.itemsPerPage))}` || $p.pageText || ''
	} else {
		props.search = debouncedSearchModel.value || undefined
		props.page = internalPage.value
		props.itemsPerPage = internalItemsPerPage.value
	}

	return props
})

const headersEnabledByDefault = computed(() => ($p.headers as Headers)?.filter(header => !!header.title && !header.hidden).map(header => header.value) || [])

const hasHiddenHeaders = computed(() => {
	return hiddenHeadersStorageState.value.some(header => headersEnabledByDefault.value.includes(header))
})

const slotsProps = computed(() => {
	return {
		height: !isFullscreen.value ? dataTableProps.value.height : '100%',
	}
})

if ($p.lazyload) {
	watch(() => data.value, (newData, oldData) => {
		if (!newData) {
			return
		}
		const page = dot.pick(String($p.pageResKey), newData) || $p.page
		const oldPage = dot.pick(String($p.pageResKey), oldData) || $p.page

		const items = dot.pick($p.itemsKey as string, newData) || []

		if (page && !lazyLoadedPages.value.includes(page)) {
			if (page > oldPage) {
				lazyLoadedPages.value.push(page)
				lazyLoadedItems.value = [...lazyLoadedItems.value, ...items]
			} else {
				lazyLoadedPages.value.unshift(page)
				lazyLoadedItems.value = [...items, ...lazyLoadedItems.value]
			}
		}
	}, {
		deep: true,
		immediate: true,
	})
	// watch(() => (data.value as any)?.[$p.pageResKey], (newPage) => {
	// 	if (data.value && newPage && !lazyLoadedPages.value.includes(newPage)) {
	// 		const items = dot.pick($p.itemsKey as string, data.value) || []
	// 		lazyLoadedPages.value.push(newPage)
	// 		lazyLoadedItems.value = [...lazyLoadedItems.value, ...items]
	// 	}
	// })
}

const searchFieldProps = computed<Partial<ComponentProps<typeof VTextField>>>(() => {
	return {
		appendInnerIcon: 'i-mdi:magnify',
		clearable: true,
		hideDetails: true,
		autofocus: true,
		placeholder: $p.searchLabel || t('Datatable.search'),
		maxWidth: '400px',
		density: 'comfortable',
		class: 'me-1 rounded-t-lg flex-shrink-0',
		variant: 'solo-filled',
		flat: true,
		// rounded: 'lg',
	}
})

const filterBtnProps = computed(() => {
	const icon = 'i-mdi:filter-plus'
	if ($p.noSearch) {
		return {
			prependIcon: icon,
			text: t('Datatable.filter'),
		}
	}
	return {
		icon,
	}
})
type VisibilityItem = {
	title: string
	value: string
	hidden: boolean
	props: Record<string, any>
}

const columnVisibilityItems = computed<VisibilityItem[]>(() => {
	// Store the number of visible columns to avoid circular reference
	const visibleColumns = $p.headers?.slice()
		?.filter(header => !!header.title)

	const visibleColumnsCount = visibleColumns?.filter(header => !hiddenHeadersStorageState.value.includes(header.value as string)).length || 0

	return	visibleColumns?.map((header: any) => {
		const hidden = hiddenHeadersStorageState.value.includes(header.value as string)
		return {
			title: header.title,
			value: header.value,
			hidden,
			disabled: (visibleColumnsCount === 1) && !hidden,
			props: {
			},
		}
	}) || []
})

const columnVisibilityMenuItems = computed<any[]>(() => {
	const arr = columnVisibilityItems.value?.slice() as any[]
	if (arr) {
		// arr.unshift({
		// 	type: 'subheader',
		// 	title: t('Datatable.columns'),
		// })
		// arr.push({
		// 	type: 'divider',
		// })
		// arr.push({
		// 	title: t('Datatable.reset-to-default'),
		// 	props: {
		// 		onClick: () => {
		// 			hiddenHeadersStorageState.value = useCloned(defaultHeadersState.value).cloned.value
		// 		},
		// 		disabled: isEqual(hiddenHeadersStorageState.value, defaultHeadersState.value),
		// 	},
		// })
		return arr
	}
	return []
})

const resetColumnVisibilityToDefault = () => {
	hiddenHeadersStorageState.value = useCloned(defaultHeadersState.value).cloned.value
}

const hasQuery = computed(() => {
	const paginationQuery = [$p.pageReqKey, $p.itemsPerPageReqKey, $p.searchKey]
	return Object.keys(appliedQuery.value).filter(key => !paginationQuery.includes(key) && !!appliedQuery.value[key]).length > 0
})

const lazyloadNext = (isIntersecting: boolean) => {
	if (isIntersecting) {
		if (pending.value || !hasNext.value) {
			return
		}

		const newPageNumber = Number(appliedQuery.value[$p.pageReqKey]) + 1
		if (lazyLoadedPages.value.includes(newPageNumber)) {
			return
		}

		addPaginationQuery(newPageNumber)

		refresh()
		emit('update:page', appliedQuery.value[$p.pageReqKey] as string | number)
	}
}

const exposedRefresh = () => {
	resetAndRefresh()
}

const hasNext = computed(() => {
	if (!$p.hasNextKey) {
		console.error('[Datatable] hasNextKey is required')
		return undefined
	}
	return !!dot.pick($p.hasNextKey as string, data.value)
})

watch(() => error.value, (value) => {
	if (value) {
		if (p.errorHandler) {
			p.errorHandler(value)
		} else {
			errorSnackbar(value.message)
		}
	}
})

const renderComponent = computed(() => {
	if ($p.url) {
		return VDataTableServer
	}

	return VDataTable
})

const lazyFilterModel = ref(false)

onMounted(() => {
	if ($p.url && $p.apiOptions.immediate !== false) {
		execute()
	}
})

type Expose = {
	query: typeof appliedQuery
	pending: typeof pending
	error: typeof error
	items: typeof internalItems
	data: typeof data
	refresh: typeof exposedRefresh
	execute: typeof execute
	showFilter: typeof showFilter
	closeFilter: typeof closeFilter
	filter: typeof exposedFilter
}

defineExpose<Expose>({
	query: appliedQuery,
	pending,
	error,
	items: internalItems,
	data,
	refresh: exposedRefresh,
	execute,
	showFilter,
	closeFilter,
	filter: exposedFilter,
})
</script>

<style scoped lang="scss">
.v-sheet{
	&:fullscreen .v-table{
			height: 100%;
	}
	&:fullscreen .v-navigation-drawer{
			left:-320px !important;
			margin:0 !important;
			top:0 !important;
			height: 100% !important;
			padding-bottom: 0 !important;
		&.v-navigation-drawer--active {
			left:0 !important
		}
	}
}
.v-locale--is-rtl .v-sheet{
	&:fullscreen .v-navigation-drawer{
		right:-320px !important;
		left:unset !important;
		&.v-navigation-drawer--active {
			right:0 !important
		}
	}

}
</style>
