<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="8"
			>
				<v-text-field
					v-model.trim="item.group"
					:hint="!!loadedGroup && loadedGroup !== item.group ? $t('Groups.MT5.Crud.Common.loaded-group', [loadedGroup]) : undefined"
					:error-messages="errors.group"
					:label="$t('Groups.MT5.Crud.Common.name')"
					:rules="rules({ required: true, whitespace: true })"
				/>
			</v-col>
			<v-col>
				<api-items
					v-slot="props"
					url="/currencies/lookup"
					:error-messages="errors.currency"
				>
					<v-combobox
						v-model="item.currency"
						item-value="symbol"
						item-title="symbol"
						v-bind="props"
						:label="$t('Groups.MT5.Crud.Common.currency')"
					/>
				</api-items>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="8"
			>
				<api-items
					v-slot="props"
					:url="Network.Lookup.URL"
					:error-messages="errors.server"
				>
					<v-select
						v-model="item.server"
						v-bind="props"
						item-value="serverId"
						item-title="serverName"
						:label="$t('Groups.MT5.Crud.Common.server')"
					/>
				</api-items>
			</v-col>
			<v-col
				cols="12"
				md="4"
			>
				<v-select
					v-model.number="item.currencyDigits"
					:error-messages="errors.currencyDigits"
					:items="Array.from({ length: 8 }, (_, i) => i)"
					:label="$t('Groups.MT5.Crud.Common.digits')"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="8"
			>
				<v-select
					v-model="item.authMode"
					:error-messages="errors.authMode"
					:label="$t('Groups.MT5.Crud.Common.authentication')"
					:items="authModeItems"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
			>
				<v-text-field
					v-model.number="item.authPasswordMin"
					:error-messages="errors.authPasswordMin"
					type="number"
					:label="$t('Groups.MT5.Crud.Common.password-min-length')"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="8"
			>
				<v-select
					v-model="item.authOTPMode"
					:items="authOTPModeItems"
					label="One-time-password"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
			>
				<v-checkbox
					v-model="permissionsFlagsModel"
					color="primary"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_FORCED_OTP_USAGE"
					:label="$t('Groups.MT5.Crud.Common.force-otp-usage')"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col
				cols="12"
				md="8"
			>
				<v-select
					v-model="notificationsModel"
					:items="notificationsItems"
					multiple
					chips
					closable-chips
					:label="$t('Groups.MT5.Crud.Common.push-notification')"
					hint="Sent from the trade server"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n4">
			<v-col cols="12">
				<v-checkbox
					v-model="permissionsFlagsModel"
					class="compact"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_ENABLE_CONNECTION"
					color="primary"
					hide-details
					:label="$t('Groups.MT5.Crud.Common.enable-connections')"
				/>
				<v-checkbox
					v-model="permissionsFlagsModel"
					class="compact"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_CERT_CONFIRM"
					color="primary"
					hide-details
					:label="$t('Groups.MT5.Crud.Common.enable-certificate-confirmation')"
				/>
				<v-checkbox
					v-model="permissionsFlagsModel"
					class="compact"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_RESET_PASSWORD"
					color="primary"
					hide-details
					:label="$t('Groups.MT5.Crud.Common.change-password-at-login')"
				/>
				<v-checkbox
					v-model="permissionsFlagsModel"
					class="compact"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_RISK_WARNING"
					color="primary"
					hide-details
					:label="$t('Groups.MT5.Crud.Common.show-the-risk-warning-win')"
				/>
				<v-checkbox
					v-model="permissionsFlagsModel"
					class="compact"
					:true-value="Groups.MT5.EnPermissionsFlags.PERMISSION_REGULATION_PROTECT"
					color="primary"
					hide-details
					:label="$t('Groups.MT5.Crud.Common.enforce-country-specific')"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups, Network } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const authModeItems = enumToItems(Groups.MT5.EnAuthMode, 'Groups.MT5.EnAuthMode')

const authOTPModeItems = enumToItems(Groups.MT5.EnAuthOTPMode, 'Groups.MT5.EnAuthOTPMode')

const computedPermissionsFlags = computed({
	get: () => p.item.permissionsFlags,
	set: (value: number) => emit('update:item', { permissionsFlags: value }),
})

const { model: permissionsFlagsModel } = useMultiSelectEnum(computedPermissionsFlags, Groups.MT5.EnPermissionsFlags)

// const { model: notificationsModel } = useMultiSelectEnum(computedPermissionsFlags, Groups.MT5.EnPermissionsFlags, {
// 	exclude: [
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_FORCED_OTP_USAGE,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_CERT_CONFIRM,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_ENABLE_CONNECTION,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_NONE,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_REGULATION_PROTECT,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_RESET_PASSWORD,
// 		Groups.MT5.EnPermissionsFlags.PERMISSION_RISK_WARNING,
// 	],
// })

const notificationsEnums = [
	Groups.MT5.EnPermissionsFlags.PERMISSION_NOTIFY_BALANCES,
	Groups.MT5.EnPermissionsFlags.PERMISSION_NOTIFY_DEALS,
	Groups.MT5.EnPermissionsFlags.PERMISSION_NOTIFY_ORDERS,
]

const notificationsModel = computed({
	get: () => permissionsFlagsModel.value.filter(item => notificationsEnums.includes(item)),
	set: (value: number[]) => {
		const newValue = [...permissionsFlagsModel.value.filter(item => !notificationsEnums.includes(item)), ...value]
		permissionsFlagsModel.value = newValue
	},
})

const notificationsItems = enumToItems(Groups.MT5.EnPermissionsFlags, 'Groups.MT5.EnPermissionsFlags', item => notificationsEnums.includes(item.value) ? item	: undefined)
</script>
