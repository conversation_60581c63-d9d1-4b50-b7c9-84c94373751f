<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Account"
		item-identity="login"
		:navigation-drawer-props="{ width: 500 }"
		:url="Hedging._Id.URL(':id')"
		v-bind="$attrs"
	>
		<template #default="{ item, errors, isCreateAction }: { item: Item<Hedging._Id.GETResponse | Hedging._Id.POSTRequest | Hedging._Id.PUTRequest>, errors: ItemErrors<Hedging._Id.GETResponse>, isCreateAction: boolean}">
			<api-items
				v-slot="props"
				items
				:url="TradingServers.Lookup.URL"
				:error-messages="errors.tradingServerId"
			>
				<v-select
					v-model="item.tradingServerId"
					v-bind="props"
					:rules="rules({ required: true })"
					label="Trading Server"
					item-title="displayName"
					item-value="id"
					:disabled="!isCreateAction"
				/>
			</api-items>
			<api-items
				v-slot="props"
				url="/my/accounts"
				:api-options="{
					params: {
						serverId: computed(() => item.tradingServerId || undefined),
					},
					immediate: !!item.tradingServerId,
				}"
				:error-messages="errors.login"
			>
				<v-autocomplete
					v-model="item.login"
					:rules="rules({ required: true })"
					:disabled="!item.tradingServerId || props.loading"
					label="Login"
					v-bind="props"
					item-title="login"
					item-value="login"
				/>
			</api-items>

			<v-textarea
				v-model="item.description"
				label="Description"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type { DefaultItem, Item, ItemErrors } from '~/components/Crud.vue'
import { Hedging, TradingServers } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Hedging._Id.POSTRequest | Hedging._Id.PUTRequest> = {
	description: '',
	login: 0,
	tradingServerId: 0,
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
