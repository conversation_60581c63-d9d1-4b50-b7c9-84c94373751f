export namespace TradingServers {

	export enum IntegrationType {
		MANAGER_ACCOUNT = 1,
		REST_API = 2,
	}

	export interface TradingPlatform {
		id: number
		name: string
		createdAt: Date
	}

	export namespace _Id {

		export const URL = (_Id: number | string) => `/trading-servers/${_Id}`

		type BaseGETResponse = {
			id: number
			name: string
			displayName: string
			webLogin: number
			webPassword?: string | undefined
			tradingPlatformId: number
			tradingPlatform: TradingPlatform
			isActive: boolean
			createdAt: Date
			updatedAt: Date
		}

		type ManagerAccountResponse = BaseGETResponse & {
			integrationType: IntegrationType.MANAGER_ACCOUNT
			ipAddress: string
			port: number
		}

		type RestAPIResponse = BaseGETResponse & {
			integrationType: IntegrationType.REST_API
			apiUrl: string
		}

		export type GETResponse = ManagerAccountResponse | RestAPIResponse

		export type PUTRequest = {
			name: string
			displayName: string
			ipAddress?: string
			port?: number
			apiUrl?: string
			webLogin: number
			webPassword?: string
			tradingPlatformId: number
			isActive: boolean
			integrationType: IntegrationType
		}

		export type POSTRequest = {
			name: string
			displayName: string
			ipAddress?: string
			port?: number
			apiUrl?: string
			webLogin: number
			webPassword?: string
			tradingPlatformId: number
			isActive: boolean
			integrationType: IntegrationType
		}

	}
	export namespace Lookup {

		export const URL = '/trading-servers/lookup'

		export interface SingleRecord {
			id: number
			name: string
			displayName: string
			ipAddress: string
			webLogin: number
			port: number
			tradingPlatformId: number
			integrationType: IntegrationType
			tradingPlatform: TradingPlatform
			isActive: boolean
			createdAt: Date
			updatedAt: Date
		}

		export type GETResponse = SingleRecord[]

	}

}
