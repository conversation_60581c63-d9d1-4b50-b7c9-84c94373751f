<template>
	<v-container>
		<v-row
			no-gutters
			class="mb-3"
		>
			<v-col
				cols="12"
				md="12"
			>
				<v-checkbox
					v-model="item.realtime"
					:true-value="Symbols.MT4.RealTime.Enabled"
					:false-value="Symbols.MT4.RealTime.Disabled"
					color="primary"
					hide-details
					:label="$t('Symbols.MT4.Crud.Filtration.allow-realtime-quotes-fro')"
					class="compact"
				/>
			</v-col>
			<v-col
				cols="12"
				md="12"
			>
				<v-checkbox
					v-model="item.logging"
					:true-value="1"
					:false-value="0"
					color="primary"
					hide-details
					:label="$t('Symbols.MT4.Crud.Filtration.save-all-incoming-prices-')"
					class="compact"
				/>
			</v-col>
		</v-row>

		<v-row>
			<v-col
				cols="12"
				md="8"
			>
				<fieldset>
					<legend>
						{{ $t('Symbols.MT4.Crud.Filtration.settings') }}
					</legend>
					<v-row>
						<v-col cols="12">
							<v-text-field
								v-model.number="item.filter"
								v-mask="Mask.Integer"
								hide-spin-buttons
								:label="$t('Symbols.MT4.Crud.Filtration.filtration-level')"
								suffix="points"
								hint="Must not be less than 4 or 5 times spread by default"
								persistent-hint
								:rules="rules({ required: true })"
							/>
							<v-select
								v-model="item.filterLimit"
								:items="filterLimitItems"
								:label="$t('Symbols.MT4.Crud.Filtration.automatic-limit')"
								hint="Percentage of the current market price"
								persistent-hint
							/>
							<v-select
								v-model="item.filterCounter"
								:items="Array.from({ length: 10 }, (_, i) => i + 1)"
								:label="$t('Symbols.MT4.Crud.Filtration.filter')"
								hint="Wrong quotes coming one after another"
								persistent-hint
							/>
							<v-text-field
								v-model.number="item.quotesDelay"
								v-mask="Mask.Integer"
								hide-spin-buttons
								:label="$t('Symbols.MT4.Crud.Filtration.ignore-quotes')"
								hint="Seconds after session start"
								persistent-hint
								suffix="seconds"
								:rules="rules({ required: true })"
							/>

							<v-select
								v-model="item.filterSmoothing"
								:label="$t('Symbols.MT4.Crud.Filtration.smoothing')"
								suffix="ticks"
								:items="Array.from({ length: 11 }, (_, i) => i)"
							/>
						</v-col>
					</v-row>
				</fieldset>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
import { Symbols } from '~/types'
import { Mask } from '~/types/Mask'

withDefaults(defineProps<Props>(), defaults)

const { t } = useI18n()

const filterLimitItems = [
	{
		title: t('Symbols.MT4.Crud.Filtration.none'),
		value: 0,
	},
	{
		title: '0.1%',
		value: 0.001,
	},
	{
		title: '0.5%',
		value: 0.005,
	},
	{
		title: '1%',
		value: 0.01,
	},
	{
		title: '3%',
		value: 0.03,
	},
	{
		title: '5%',
		value: 0.05,
	},
	{
		title: '10%',
		value: 0.1,
	},
	{
		title: '15%',
		value: 0.15,
	},
	{
		title: '20%',
		value: 0.2,
	},
]
</script>
