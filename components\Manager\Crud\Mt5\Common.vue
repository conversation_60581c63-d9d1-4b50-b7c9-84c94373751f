<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="4"
			>
				<v-text-field
					v-model.number="item.login"
					:rules="rules({ required: true, type: 'number', min: 1 })"
					:label="$t('Manager.MT5.Crud.Common.login')"
					type="number"
					hide-spin-buttons
				/>
			</v-col>
			<v-col
				cols="12"
				md="8"
			>
				<v-text-field
					v-model="item.mailbox"
					:label="$t('Manager.MT5.Crud.Common.mailbox-name')"
				/>
			</v-col>
		</v-row>

		<api-items
			v-slot="props"
			:url="Groups.MT5.Paths.Lookup.URL"
			:map-items="groupPathMapper"
		>
			<v-tree-select
				v-bind="props"
				v-model="groupsModel"
				:label="$t('Manager.MT5.Crud.Common.groups')"
				chips
				closable-chips
				:tree-props="{
					selectStrategy: 'independent',
				}"
			/>
		</api-items>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { t } = useI18n()

const groupsModel = computed({
	get() {
		return p.item.groups
	},
	set(v: string[]) {
		emit('update:item', {
			groups: v,
		})
	},
})

const groupPathMapperHelper = (item: Groups.MT5.Paths.Lookup.SingleRecord) => {
	const children: any = item.children?.map(groupPathMapperHelper)

	return {
		...item,
		value: item.type === 'Path' ? item.value + '\\*' : item.value,
		children: children?.length ? children : undefined,
		props: {
			class: {
				'ms-7': !children?.length,
			},
		},
	}
}

const groupPathMapper = (items: Groups.MT5.Paths.Lookup.SingleRecord[]) => {
	const mappedItems = items.map(groupPathMapperHelper)

	mappedItems.unshift({
		value: '*',
		title: t('Manager.MT5.Crud.Common.all-groups'),
		props: {
			class: {
				'ms-7': true,
			},
		},
	} as ReturnType<typeof groupPathMapperHelper>)

	return mappedItems
}
</script>
