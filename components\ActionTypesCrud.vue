<template>
	<operation-crud
		ref="operationCrudRef"
		:navigation-drawer-props="{ width: 400 }"
		@created="operationCreatedHandler"
	/>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Action Type"
		item-identity="displayName"
		:navigation-drawer-props="{ width: 500 }"
		url="/action-types/:id"
		v-bind="$attrs"
		@loaded="tradingPlatformId = $event?.operation?.tradingPlatformId"
	>
		<template #default="{ item, errors }: { item: Item<ActionTypes._Id.GETResponse>, errors: ItemErrors<ActionTypes._Id.GETResponse>}">
			<!-- <v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				:rules="rules({ required: true })"
				label="Name"
			/> -->

			<api-items
				v-slot="props"
				url="/trading-platforms/lookup"
			>
				<v-select
					v-model="tradingPlatformId"
					:rules="rules({ required: true })"
					:label="$t('OperationCrud.trading-platform')"
					v-bind="props"
					item-title="name"
					item-value="id"
				/>
			</api-items>
			<api-items
				ref="actionTypesApiItemsRef"
				v-slot="props"
				url="/operations/lookup"
				:api-options="{
					query,
					immediate: false,
				}"
				:error-messages="errors.operationId"
			>
				<v-select
					v-model="item.operationId"
					:rules="rules({ required: true })"
					label="Operation"
					v-bind="props"
					item-title="displayName"
					item-value="id"
					:menu-props="{ modelValue: isSelectOpen }"
					:disabled="!tradingPlatformId"
					@update:menu="updateMenuHandler"
				>
					<template
						v-if="can('operations.create')"
						#prepend-item
					>
						<v-list-item
							slim
							@click="createNewOperation()"
						>
							<template #prepend>
								<v-icon>i-mdi:plus</v-icon>
							</template>
							Add New Operation
						</v-list-item>

						<v-divider class="mt-2" />
					</template>
				</v-select>
			</api-items>
			<v-text-field
				v-model="item.displayName"
				:error-messages="errors.displayName"
				:rules="rules({ required: true })"
				label="Display Name"
			/>

			<div class="ps-1">
				<v-label class="d-block mb-1">
					Direction
				</v-label>

				<v-btn-toggle
					v-model="item.isIncremental"
					density="compact"
					divided
					mandatory
					border
					label="Direction"
				>
					<v-btn
						color="success"
						:value="true"
						prepend-icon="i-mdi:arrow-bottom"
					>
						In
					</v-btn>
					<v-btn
						color="error"
						:value="false"
						prepend-icon="i-mdi:arrow-top"
					>
						Out
					</v-btn>
				</v-btn-toggle>
			</div>

			<!-- <v-switch v-model="item.isIncremental" :error-messages="errors.isIncremental" color="success" label="Incremental?" /> -->
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from './Crud.vue'
import type { CrudEventData, DefaultItem, Item, ItemErrors } from './Crud.vue'
import type ApiItems from './ApiItems.vue'
import type { ActionTypes } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<ActionTypes._Id.POSTRequest | ActionTypes._Id.POSTRequest> = {
	// name: '',
	displayName: '',
	isIncremental: false,
	operationId: null,
}

const tradingPlatformId = ref<string | null>(null)

const query = computed(() => {
	return {
		tradingPlatformId: tradingPlatformId,
	}
})

const operationsApiItemsRef = ref<InstanceType<typeof ApiItems> | null>(null)

const operationCrudRef = ref<InstanceType<typeof Crud> | null>(null)

const isSelectOpen = ref<boolean>(false)

const operationCreatedHandler = async (e: CrudEventData) => {
	await operationsApiItemsRef.value?.refresh()

	if (crudRef.value) {
		const item = crudRef.value?.getItem()
		item.operationId = e.response.id
		crudRef.value?.setItem(item)
	}
}

const createNewOperation = () => {
	isSelectOpen.value = false
	operationCrudRef.value?.create()
}

const updateMenuHandler = (value: boolean) => {
	isSelectOpen.value = value
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
