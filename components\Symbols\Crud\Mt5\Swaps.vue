<template>
	<v-container>
		<v-switch
			v-model="isSwapEnabled"
			color="success"
			:label="$t('Symbols.MT5.Crud.Swaps.enable-swaps')"
			@update:model-value="!$event && (item.swapMode = Symbols.MT5.SwapMode.SWAP_DISABLED)"
		/>

		<v-defaults-provider
			:defaults="{
				global: {
					disabled: !isSwapEnabled,
				},
			}"
		>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<fieldset>
						<legend>{{ $t('Symbols.MT5.Crud.Swaps.settings') }}</legend>
						<v-select
							v-model="item.swapMode"
							:error-messages="errors.swapMode"
							:items="swapModeItems"
							:rules="rules({ required: true })"
							:label="$t('Symbols.MT5.Crud.Swaps.type')"
						/>
						<v-text-field
							v-model.number="item.swapLong"
							:error-messages="errors.swapLong"
							:rules="rules({ required: true })"
							type="number"
							hide-spin-buttons
							:label="$t('Symbols.MT5.Crud.Swaps.long-positions')"
						/>
						<v-text-field
							v-model.number="item.swapShort"
							:error-messages="errors.swapShort"
							:rules="rules({ required: true })"
							type="number"
							hide-spin-buttons
							:label="$t('Symbols.MT5.Crud.Swaps.short-positions')"
						/>

						<v-combobox
							v-model.number="item.swapYearDay"
							:error-messages="errors.swapYearDay"
							:rules="rules({ required: isSwapEnabled, type: 'number', min: 182, max: 366 })"
							:label="$t('Symbols.MT5.Crud.Swaps.days-in-year')"
						/>
						<v-checkbox
							v-model="item.swapFlags"
							color="primary"
							:true-value="Symbols.MT5.SwapFlags.SWAP_FLAGS_CONSIDER_HOLIDAYS"
							:false-value="Symbols.MT5.SwapFlags.SWAP_FLAGS_NONE"
							:label="$t('Symbols.MT5.Crud.Swaps.automatically-consider-ho')"
						/>
					</fieldset>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<fieldset>
						<legend>{{ $t('Symbols.MT5.Crud.Swaps.swap-multipliers') }}</legend>
						<v-btn
							variant="text"
							density="default"
							block
							append-icon="i-mdi:menu-down"
						>
							{{ $t('Symbols.MT5.Crud.Swaps.set-standard-settings') }}	<v-menu
								v-slot="{ isActive: isMenuActive }"
								activator="parent"
							>
								<v-list>
									<v-list-item
										:title="$t('Symbols.MT5.Crud.Swaps.forex')"
										@click="setAsForex"
									/>
									<v-list-item
										:title="$t('Symbols.MT5.Crud.Swaps.all-week')"
										@click="setAllWeek"
									/>
									<v-list-item
										:title="$t('Symbols.MT5.Crud.Swaps.from-symbol')"
										link
									>
										<v-dialog
											v-slot="{ isActive }"
											activator="parent"
											max-width="400"
										>
											<v-card :title="$t('Symbols.MT5.Crud.Swaps.choose-symbol')">
												<v-card-text>
													<symbols-crud-symbols-tree
														v-model="selectedSymbol"
														:label="$t('Symbols.MT5.Crud.Swaps.symbol')"
														@update:model-value="fetchSymbol"
													/>
												</v-card-text>

												<v-card-actions>
													<v-btn
														variant="text"
														@click="isActive.value = false;isMenuActive.value = false"
													>
														{{ $t('Symbols.MT5.Crud.Swaps.cancel') }}
													</v-btn>
													<v-btn
														variant="text"
														:loading="isFetching"
														:disabled="isFetching"
														@click="saveFetchedSymbol();isActive.value = false;isMenuActive.value = false "
													>
														{{ $t('Symbols.MT5.Crud.Swaps.save') }}
													</v-btn>
												</v-card-actions>
											</v-card>
										</v-dialog>
									</v-list-item>
								</v-list>
							</v-menu>
						</v-btn>

						<v-table>
							<thead :class="{ 'text-disabled': !isSwapEnabled }">
								<tr>
									<th>
										{{ $t('Symbols.MT5.Crud.Swaps.day') }}
									</th>
									<th>
										{{ $t('Symbols.MT5.Crud.Swaps.multiplier') }}
									</th>
								</tr>
							</thead>
							<tbody :class="{ 'text-disabled': !isSwapEnabled }">
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.sunday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateSunday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.monday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateMonday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.tuesday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateTuesday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.wednesday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateWednesday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.thursday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateThursday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.friday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateFriday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
								<tr>
									<td>{{ $t('Symbols.MT5.Crud.Swaps.saturday') }}</td>
									<td>
										<v-text-field
											v-model.number="item.swapRateSaturday"
											type="number"
											hide-spin-buttons
											single-line
											density="compact"
											hide-details
											flat
											variant="solo-filled"
											class="my-1"
										/>
									</td>
								</tr>
							</tbody>
						</v-table>
					</fieldset>
				</v-col>
			</v-row>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import type { Props, Emit } from './Shared'
import { defaults } from './Shared'
import { Symbols } from '~/types'

type DaysSwapRate = {
	swapRateSunday: number
	swapRateMonday: number
	swapRateTuesday: number
	swapRateWednesday: number
	swapRateThursday: number
	swapRateFriday: number
	swapRateSaturday: number
}

const { $api } = useNuxtApp()

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const swapModeItems = enumToItems(Symbols.MT5.SwapMode, 'Symbols.MT5.SwapMode',
	item => item.value === Symbols.MT5.SwapMode.SWAP_DISABLED ? undefined : item,
)

const selectedSymbol = ref<string | undefined>()

const isFetching = ref(false)

const fetchedSymbol = ref<Symbols.MT5._Id.GETResponse | null>(null)

const isSwapEnabled = computed({
	get() {
		return p.item.swapMode !== Symbols.MT5.SwapMode.SWAP_DISABLED
	},
	set(value: boolean) {
		emit('update:item', {
			swapMode: value ? Symbols.MT5.SwapMode.SWAP_BY_POINTS : Symbols.MT5.SwapMode.SWAP_DISABLED,
		})
	},
})

const setSwapRate = (days: DaysSwapRate) => {
	emit('update:item', days)
}

const setAsForex = () => {
	setSwapRate({
		swapRateSunday: 0,
		swapRateMonday: 1,
		swapRateTuesday: 1,
		swapRateWednesday: 3,
		swapRateThursday: 1,
		swapRateFriday: 1,
		swapRateSaturday: 0,
	})
}

const setAllWeek = () => {
	setSwapRate({
		swapRateSunday: 1,
		swapRateMonday: 1,
		swapRateTuesday: 1,
		swapRateWednesday: 1,
		swapRateThursday: 1,
		swapRateFriday: 1,
		swapRateSaturday: 1,
	})
}

const fetchSymbol = (value: string) => {
	selectedSymbol.value = value
	isFetching.value = true
	$api<Symbols.MT5._Id.GETResponse>(Symbols.MT5._Id.URL(value))
		.then((symbol) => {
			fetchedSymbol.value = symbol
		})
		.finally(() => {
			isFetching.value = false
		})
}

const saveFetchedSymbol = () => {
	if (fetchedSymbol.value) {
		setSwapRate(
			{
				swapRateSunday: fetchedSymbol.value.swapRateSunday,
				swapRateMonday: fetchedSymbol.value.swapRateMonday,
				swapRateTuesday: fetchedSymbol.value.swapRateTuesday,
				swapRateWednesday: fetchedSymbol.value.swapRateWednesday,
				swapRateThursday: fetchedSymbol.value.swapRateThursday,
				swapRateFriday: fetchedSymbol.value.swapRateFriday,
				swapRateSaturday: fetchedSymbol.value.swapRateSaturday,
			},
		)
	}
}
</script>
