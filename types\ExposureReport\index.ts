export namespace ExposureReport {
	export interface Record {
		serverId: number
		platform: number
		category: Category
		group: string
		symbol: string
		totalVolumeBuy: number
		totalVolumeSell: number
		totalExposureLots: number
		totalExposureValue: number
		numberOfBuys: number
		numberOfSells: number
		buyVWAP: number
		sellVWAP: number
		currentPrice: number
		currentUnrealizedProfit: number
		totalClosedProfitForDay: number
	}

	export enum Category {
		Cryptocurrencies = 'Cryptocurrencies',
		ETFS = 'ETF\'s',
		Energies = 'Energies',
		FranceShares = 'France Shares',
		Fx = 'FX',
		GermanyShares = 'Germany Shares',
		Indices = 'Indices',
		Metals = 'Metals',
		UKShares = 'UK Shares',
		USShares = 'US Shares',
		Unknown = 'Unknown',
	}

	export const URL = `/exposure-report`

	export type GETResponse = {
		[key in Category]: Record
	}

	// export namespace _Id {
	// 	export const URL = (_Id: number | string) => `/currencies/${_Id}`

	// 	export interface GETResponse {
	// 		id: number
	// 		name: string
	// 		symbol: string
	// 		createdAt: string
	// 		updatedAt: null
	// 	}

	// 	export interface PUTRequest {
	// 		name: string
	// 		symbol: string
	// 	}

	// 	export interface POSTRequest {
	// 		name: string
	// 		symbol: string
	// 	}

	// }

}
