<template>
	<div class="bg-aurora animate">
		<v-sheet
			class="pa-md-12 pa-6 h-screen d-flex align-center"
			:class="{ 'v-locale--is-rtl': isRtl }"
			color="transparent"
		>
			<slot />
			<div class="copyrights">
				<div class="text-caption text-center text-medium-emphasis">
					<i18n-t
						keypath="auth.copyrights"
						tag="span"
					>
						<template #symbol>
							<sup>©</sup>
						</template>
						<template #version>
							{{ app.version }}
						</template>
					</i18n-t>
				</div>
			</div>
		</v-sheet>
	</div>
</template>

<script setup lang="ts">
const { isRtl } = useLocale()

const { app } = useAppConfig()

definePageMeta({
	viewTransition: false,
})
</script>

<style lang="scss">
.engraved-text {
	text-shadow: 0px 1px 0px rgba(255,255,255,.3), 0px -1px 0px rgba(0,0,0,.7);
	/* font-size: 150px !important; */
	user-select: none;
}

.mt-img{
	background-size: contain;
	background-repeat: no-repeat;
	background-position: end;
	opacity: 0.05;
	/* filter: grayscale(100%); */
}

.copyrights{
	position: fixed;
	bottom:4px;
	right:4px;
	--v-theme-on-background: 255,255,255;
	@at-root .v-locale--is-rtl & {
		left:4px;
		right:unset;
	}
}
</style>
