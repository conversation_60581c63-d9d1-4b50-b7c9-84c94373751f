<template>
	<v-btn v-bind="btnBind" />
</template>

<script lang="ts" setup>
const i18n = useI18n()

const prefStore = usePreferencesStore()

const switchLocaleHandler = (locale: string) => {
	i18n.setLocale(locale)
	// isRtl.value = i18n.localeProperties.value.dir === 'rtl'
	prefStore.updateKey('locale', locale)
}

const otherLocale = computed(() => {
	return i18n.availableLocales.find(locale => locale !== i18n.locale.value)
})

const btnBind = computed(() => {
	return {
		onClick: () => switchLocaleHandler(otherLocale.value as string),
		text: otherLocale.value?.toUpperCase(),
		icon: true,
		active: false,
	}
})
</script>
