<template>
	<v-text-field
		:model-value="model"
		placeholder="Select Symbol"
		readonly
		prepend-inner-icon="i-ph:currency-circle-dollar"
		class="curser-pointer"
		:append-inner-icon="menuModel ? 'i-mdi:menu-up' : 'i-mdi:menu-down'"
		:loading="status === 'pending'"
	>
		<v-menu
			v-model="menuModel"
			activator="parent"
			max-height="500dvh"
			:close-on-content-click="false"
			max-width="100%"
			@update:model-value="menuHandler"
		>
			<v-list
				v-model:opened="openedModel"
				slim
				density="compact"
				class="position-relative opacity-100"
				width="100%"
				bg-color="surface"
			>
				<!-- <template #prepend> -->
				<v-sheet
					class="position-sticky px-1"
					color="surface"
					style="z-index: 1;top: -8px;"
				>
					<v-text-field
						v-model="searchPathModel"
						autofocus
						clearable
						placeholder="Find Path"
						hide-details
						flat
						variant="solo"
						density="comfortable"
					/>
					<v-divider class="mx-2" />
				</v-sheet>
				<!-- <v-list > -->
				<SymbolsTreeItem
					v-for="item in computedData"
					:key="item.value"
					:item="item"
					@symbol-select="symbolSelectHandler"
				/>
				<!-- </v-list> -->
				<!-- </template> -->
				<!-- <v-treeview
						v-model:opened="openedModel"
						v-model:selected="selectedModel"
						:activatable="false"
						item.children="children"
						item.value="value"
						item-type="s"
						active-class="bg-background"
						active-strategy="single-independent"
						open-strategy="single"
						expand-icon="i-mdi:folder text-yellow-darken-1"
						collapse-icon="i-mdi:folder-open text-yellow-darken-1"
						slim
						:search="debouncedSearchPathModel"
						:items="data || []"
						density="compact"
						width="100%"
					>
						<template #title="{item,title}">
							<div>
								{{ title }} <span class="text-caption">({{ item.children.length }})</span>
							</div>
						</template>

						<template #append="{item}">
							<v-btn
								v-if="item.type === 'symbol'"
								v-tooltip="'Select'"
								icon="i-mdi:chevron-right"
								variant="text"
								size="x-small"
								@click.stop="selectPath(item.value)"
							/>
						</template>
					</v-treeview> -->
				<!-- <v-dialog v-model="addDirModel" max-width="400" attach absolute @update:model-value="cancelAddDir">
						<v-form v-slot="{ isValid }" @submit.prevent="saveDir">
							<v-card title="Add Directory">
								<template #text>
									<div class="mb-2" />
									<v-text-field
										v-model.trim="dirNameModel"
										autofocus
										placeholder="Directory Name"
										density="comfortable"
										persistent-placeholder
										single-line
										:rules="rules({ required: true, whitespace: true })"
									/>
								</template>
								<template #actions="">
									<v-spacer />
									<v-btn @click="cancelAddDir(false)">
										Cancel
									</v-btn>
									<v-btn :disabled="!isValid.value" @click="saveDir()">
										Save
									</v-btn>
								</template>
							</v-card>
						</v-form>
					</v-dialog> -->
			</v-list>
		</v-menu>
	</v-text-field>
</template>

<script lang="ts" setup>
import SymbolsTreeItem from './SymbolsTreeItem.vue'
import { Symbols } from '~/types'

const model = defineModel('modelValue', {
	type: String,
	default: '',
})

const searchPathModel = ref('')

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

// const paths = ref<Symbols.MT5.Lookup.GETResponse>([])

const openedModel = ref<Symbols.MT5.Lookup.SingleRecord['value'][]>([])

// const selectedModel = ref([])

const menuModel = ref(false)

// const currentAddDirItem = ref<Path | null>(null)

// const addDirModel = ref(false)

// const dirNameModel = ref('')

const { data, status } = useApi<Symbols.MT5.Lookup.GETResponse>(Symbols.MT5.Lookup.URL, {
	key: 'crudSymbolsTree',
})

const computedData = computed(() => {
	if (!data.value) {
		return []
	}

	if (!debouncedSearchPathModel.value) {
		return data.value
	}
	// filter the data based on the debouncedSearchPathModel model, find the item.title recursively in all children and keep the parent folder
	const filterData = (data: Symbols.MT5.Lookup.GETResponse, search: string) => {
		return data.filter((item) => {
			if (item.children.length) {
				const children = filterData(item.children, search)
				if (children.length) {
					item.children = children
					return true
				}
			}
			return item.title.toLowerCase().includes(search.toLowerCase())
		})
	}
	return filterData(data.value, debouncedSearchPathModel.value)
})

const symbolSelectHandler = (value: string) => {
	// extract the last part of the path
	const parts = value.split('\\')
	model.value = parts[parts.length - 1]
	menuModel.value = false
}

const menuHandler = (value: boolean) => {
	if (value === false) {
		searchPathModel.value = ''
		openedModel.value = []
	}
}
</script>
