export namespace Hedging {

	export const URL = `/hedging`

	export namespace _Id {

		export const URL = (_Id: number | string) => `/hedging/${_Id}`

		export interface Record {
			id: number
			login: number
			description: string
			tradingServerId: number
			createdAt: string
			updatedAt: string
		}

		export type GETResponse = Record

		export type PUTRequest = Omit<Record, 'id' | 'createdAt' | 'updatedAt'>

		export type POSTRequest = Omit<Record, 'id' | 'createdAt' | 'updatedAt'>

	}

}
