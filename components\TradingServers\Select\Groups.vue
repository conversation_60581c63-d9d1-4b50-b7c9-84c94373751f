<template>
	<div>
		<trading-servers-select-search
			v-model="model"
			:item="p.item"
		/>
		<v-sheet
			v-if="model.length"
			v-auto-animate
			class="d-flex flex-wrap mt-3"
			style="gap:4px"
		>
			<v-chip
				v-for="group in model"
				:key="group"
				:text="group"
				closable
				@click.prevent
				@click:close="removeItem(group)"
			/>
		</v-sheet>

		<v-empty-state
			v-else
			:title="$t('TradingServerSelectGroups.there-is-no-any-group-yet')"
			text="Please select a group"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { TradingServers } from '~/types'

type Props = {
	disabled?: boolean
	modelValue: string[]
	item: TradingServers.Lookup.SingleRecord
}

const p = defineProps<Props>()

const emit = defineEmits(['update:modelValue'])

// const model = defineModel<string[]>({ type: Array, default: () => [] })
const model = computed({
	get: () => p.modelValue,
	set: (value: string[]) => emit('update:modelValue', value),
})

const removeItem = (item: string) => {
	model.value = model.value.filter(i => i !== item)
}
</script>
