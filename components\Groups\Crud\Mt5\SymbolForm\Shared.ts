import type { Item, ItemErrors } from '~/components/Crud.vue'
import { Symbols, type Groups } from '~/types'
import { EnPermissionsFlags } from '~/types/Groups/MT5/Symbol'

export type Props = {
	item: Item<Groups.MT5.Symbol>
	errors: ItemErrors<Groups.MT5.Symbol>
}

export const defaults = {

}

export type Emit = {
	(e: 'update:item', v: Partial<Props['item']>): void
}

export const defaultSymbol: Groups.MT5.Symbol = {
	path: '*',
	spreadDiff: 0,
	spreadDiffDefault: true,
	spreadDiffBalance: 0,
	spreadDiffBalanceDefault: true,
	stopsLevel: 30,
	stopsLevelDefault: true,
	freezeLevel: 0,
	freezeLevelDefault: true,
	swap3Day: 2147483647,
	swap3DayDefault: true,
	swapYearDaysDefault: true,
	volumeMin: 100,
	volumeMinDefault: true,
	volumeMax: 1000000,
	volumeMaxDefault: true,
	volumeStep: 100,
	volumeStepDefault: true,
	volumeLimit: 0,
	volumeLimitDefault: true,
	ieVolumeMax: 0,
	ieVolumeMaxDefault: true,
	volumeMinExt: 1000000,
	volumeMinExtDefault: true,
	volumeMaxExt: 10000000000,
	volumeMaxExtDefault: true,
	volumeStepExt: 1000000,
	volumeStepExtDefault: true,
	volumeLimitExt: 0,
	volumeLimitExtDefault: true,
	ieVolumeMaxExt: 1000000,
	ieVolumeMaxExtDefault: true,
	marginInitial: 0,
	marginInitialDefault: true,
	marginMaintenance: 0,
	marginMaintenanceDefault: true,
	marginLong: 0,
	marginLongDefault: false,
	marginShort: 0,
	marginShortDefault: false,
	marginLimit: 0,
	marginLimitDefault: false,
	marginStop: 0,
	marginStopDefault: false,
	marginStopLimit: 0,
	marginStopLimitDefault: false,
	swapLong: 1.7976931348623157e+308,
	swapLongDefault: true,
	swapShort: 1.7976931348623157e+308,
	swapShortDefault: true,
	marginRateInitial: 1.7976931348623157e+308,
	marginRateInitialDefault: true,
	marginRateMaintenance: 1.7976931348623157e+308,
	marginRateMaintenanceDefault: true,
	marginRateLiquidity: 0,
	marginRateLiquidityDefault: true,
	marginHedged: 100000,
	marginHedgedDefault: true,
	marginRateCurrency: 0,
	marginRateCurrencyDefault: true,
	swapRateSunday: 1.7976931348623157e+308,
	swapRateSundayDefault: true,
	swapRateMonday: 1.7976931348623157e+308,
	swapRateMondayDefault: true,
	swapRateTuesday: 1.7976931348623157e+308,
	swapRateTuesdayDefault: true,
	swapRateWednesday: 1.7976931348623157e+308,
	swapRateWednesdayDefault: true,
	swapRateThursday: 1.7976931348623157e+308,
	swapRateThursdayDefault: true,
	swapRateFriday: 1.7976931348623157e+308,
	swapRateFridayDefault: true,
	swapRateSaturday: 1.7976931348623157e+308,
	swapRateSaturdayDefault: true,
	reTimeout: 7,
	reTimeoutDefault: true,
	reFlags: 0,
	reFlagsDefault: false,
	ieCheckMode: Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_NONE,
	ieCheckModeDefault: true,
	ieTimeout: 30,
	ieTimeoutDefault: true,
	ieSlipProfit: 30,
	ieSlipProfitDefault: true,
	ieSlipLosing: 30,
	ieSlipLosingDefault: true,
	bookDepthLimit: 0,
	ieFlags: Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_NONE,
	ieFlagsDefault: true,
	swapYearDays: 4294967295,
	swapFlags: 4294967295,
	swapFlagsDefault: true,
	marginMaintenanceBuy: 0,
	marginMaintenanceSell: 0,
	marginMaintenanceBuyLimit: 0,
	marginMaintenanceSellLimit: 0,
	marginMaintenanceBuyStop: 0,
	marginMaintenanceSellStop: 0,
	marginMaintenanceBuyStopLimit: 0,
	marginMaintenanceSellStopLimit: 0,
	marginInitialBuy: 1,
	marginInitialSell: 1,
	marginInitialBuyLimit: 0,
	marginInitialSellLimit: 0,
	marginInitialBuyStop: 0,
	marginInitialSellStop: 0,
	marginInitialBuyStopLimit: 0,
	marginInitialSellStopLimit: 0,
	tradeMode: Symbols.MT5.TradeMode.TRADE_FULL,
	tradeModeDefault: true,
	execMode: Symbols.MT5.ExecutionMode.EXECUTION_INSTANT,
	execModeDefault: true,
	fillFlags: Symbols.MT5.FillingFlags.FILL_FLAGS_NONE,
	fillFlagsDefault: true,
	expirFlags: Symbols.MT5.ExpirationFlags.TIME_FLAGS_ALL,
	expirFlagsDefault: true,
	marginFlags: 0,
	marginFlagsDefault: true,
	swapMode: -1,
	swapModeDefault: true,
	orderFlags: -1,
	orderFlagsDefault: true,
	permissionsFlags: EnPermissionsFlags.PERMISSION_NONE,
} satisfies Groups.MT5.Symbol
