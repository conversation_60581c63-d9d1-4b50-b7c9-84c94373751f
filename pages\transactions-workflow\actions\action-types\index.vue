<template>
	<div>
		<page :title="$t('action-types.action-types')">
			<template #actions>
				<v-btn
					v-if="can('operations.view')"
					variant="text"
					color="primary"
					prepend-icon="i-material-symbols-light:action-key"
					:to="{ name: 'transactions-workflow-actions-action-types-operations' }"
				>
					{{ $t('action-types.operations') }}
				</v-btn>
				<v-btn
					v-if="can('actionTypes.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('action-types.create-action-type') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/action-types"
				:headers="headers"
				search-key="name"
			>
				<template #item.isIncremental="{ value }">
					<!-- <truthy-icon :model-value="value" /> -->
					<div v-if="value">
						<v-icon
							icon="i-mdi:arrow-bottom"
							color="success"
						/> {{ $t('action-types.in') }}
					</div>
					<div v-else>
						<v-icon
							color="error"
							icon="i-mdi:arrow-top"
						/> {{ $t('action-types.out') }}
					</div>
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('actionTypes.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('actionTypes.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<action-types-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type ActionTypesCrud from '~/components/ActionTypesCrud.vue'

definePageMeta({
	permission: 'actionTypes.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof ActionTypesCrud | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('action-types.display-name'),
		value: 'displayName',
	},
	{
		title: t('action-types.operation'),
		value: 'operation.displayName',
	},
	{
		title: t('action-types.direction'),
		value: 'isIncremental',
	},
	{
		value: 'actions',
	},
]
</script>
