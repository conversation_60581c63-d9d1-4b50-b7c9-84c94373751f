<template>
	<v-text-field
		v-model.number="model"
		hide-spin-buttons
		type="number"
	/>
</template>

<script lang="ts" setup>
import { VTextField } from 'vuetify/components'

type Props = {
	modelValue: number
	ext: number
}

type Emit = {
	(key: 'update:modelValue', value: number): void
	(key: 'update:ext', value: number): void
}

const emit = defineEmits<Emit>()

const p = defineProps<Props>()

/* example:
model = 2220
ext = 22200000

result = 0.222
*/

const model = computed({
	get: () => {
		return p.modelValue / (p.ext / p.modelValue || 1)
	},
	set: (v) => {
		if (!v) {
			emit('update:modelValue', 0)
			emit('update:ext', 0)
			return
		}
		const decimalPoints = getDecimalPointsCount(v)
		const factor = Math.pow(10, decimalPoints + 1)

		const modelValue = Math.round(v * factor)

		const ext = modelValue * factor

		emit('update:modelValue', modelValue)
		emit('update:ext', ext)
	},
})

function getDecimalPointsCount(number: number) {
	const str = number.toString()
	const decimalIndex = str.indexOf('.')

	if (decimalIndex === -1) {
		return 0 // No decimal point found, so there are 0 decimal places.
	}

	return str.length - decimalIndex - 1
}
</script>
