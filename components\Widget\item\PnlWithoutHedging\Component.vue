<template>
	<v-card
		height="100%"
		flat
		border
	>
		<v-card-title class="d-flex align-center">
			<div class="text-subtitle-1 text-wrap line-truncate-2 me-3">
				Daily, Weekly, and Monthly PnL without Hedging
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text>
			<v-list-item
				title="Daily"
				class="mb-2 bg-background"
			>
				<template #append>
					<span class="text-success">+{{ money(data.Daily) }}</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Weekly"
				class="mb-2 bg-background"
			>
				<template #append>
					<span class="text-success">+{{ money(data.Weekly) }}</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Monthly"
				class="mb-2 bg-background"
			>
				<template #append>
					<span class="text-success">+{{ money(data.Monthly) }}</span>
				</template>
			</v-list-item>
		</v-card-text>
		<!-- <template #append>
			<v-icon
				size="48"
				:color="`rgba(var(--v-theme-${iconColor}),0.3)`"
				icon="i-mdi:account-online-outline"
			/>
		</template> -->
		<!-- <template #actions>
			<v-spacer />
			<nuxt-link-locale class="text-caption text-decoration-none text-medium-emphasis" :to="{name: 'reports-online-traders'}">
				View Sessions
			</nuxt-link-locale>
		</template> -->
		<!-- <v-sparkline
			:model-value="data.history"
			color="rgb(var(--v-theme-success))"
			:gradient="['rgb(var(--v-theme-success))','rgb(var(--v-theme-surface))']"
			fill
			padding="0"
			stroke-linecap="round"
			smooth
		/> -->
	</v-card>
</template>

<script lang="ts" setup>
import type { WidgetItemProps } from '../../types'
import { PnlWithoutHedging } from '~/services/WebSocket/PnlWithoutHedging'
import type { SocketData } from '~/services/WebSocket/PnlWithoutHedging'

type Props = WidgetItemProps & {}

const p = defineProps<Props>()

const pnlWithoutHedgingInstance = new PnlWithoutHedging()

const previewData = ref<SocketData>({
	Daily: 1234.21,
	Weekly: 3216.25,
	Monthly: 12588.78,
})

const realData = ref<SocketData>({} as SocketData)

const data = computed<SocketData>(() => {
	if (p.isPreview) {
		return previewData.value
	}
	return realData.value!
})

watch(() => p.isPreview, (isPreview) => {
	if (!isPreview) {
		pnlWithoutHedgingInstance.connect().onUpdate((data) => {
			realData.value = data
		})
	}
}, { immediate: true, once: true })

// const tradingServerName = computed(() => myPref.keys.tradingServerName)

// watch(() => tradingServerName.value, (value) => {
// 	if (!p.isPreview && value) {
// 		onlineSessionsStore.connect().to(value)
// 	}
// }, {
// 	immediate: true,
// })
</script>
