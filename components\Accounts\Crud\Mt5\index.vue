<template>
	<accounts-crud-mt5-create
		ref="createCrudRef"
		@created="emit('created', $event)"
		@updated="emit('updated', $event)"
		@deleted="emit('deleted', $event)"
	/>
	<accounts-crud-mt5-update
		ref="updateCrudRef"
		@created="emit('created', $event)"
		@updated="emit('updated', $event)"
		@deleted="emit('deleted', $event)"
	/>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type AccountsCrudMt5Create from '~/components/Accounts/Crud/Mt5/Create/index.vue'

defineOptions({
	inheritAttrs: false,
})

const createCrudRef = ref<InstanceType<typeof Crud>>()
const updateCrudRef = ref<InstanceType<typeof Crud>>()

const emit = defineEmits(['created', 'updated', 'deleted'])

type Expose = {
	create: (...args: Parameters<InstanceType<typeof Crud>['create']>) => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: (...args) => createCrudRef.value!.create(...args),
	update: (item: any) => updateCrudRef.value?.update(item),
	delete: (item: any) => updateCrudRef.value?.delete(item),
})
</script>
