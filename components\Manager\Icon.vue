<template>
	<v-icon v-bind="iconProps" />
</template>

<script lang="ts" setup>
import { VIcon } from 'vuetify/components'
import { Manager } from '~/types'

type MT5Manager = Pick<Manager.MT5._Id.GETResponse, 'rights'>

type MT4Manager = Pick<Manager.MT4._Id.GETResponse, 'admin'>

type Props = {
	manager: MT5Manager
	platform: 'MT5'
} | {
	manager: MT4Manager
	platform: 'MT4'
}

defineOptions({
	components: VIcon,
})
const p = defineProps<Props>()

const iconProps = computed<VIcon['$props']>(() => {
	let color

	if (p.platform === 'MT5') {
		if (p.manager.rights.includes(Manager.MT5.EnManagerRights.RIGHT_ADMIN)) {
			color = 'account'
		} else {
			color = 'info'
		}
	} else {
		if (p.manager.admin) {
			color = 'account'
		} else {
			color = 'info'
		}
	}

	return {
		icon: 'i-mdi:account-tie',
		color,

	}
})
</script>
