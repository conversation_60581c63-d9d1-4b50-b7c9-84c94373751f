const config = {
	baseURL: 'http://10.10.50.241:261/api/auth',
	globalAppMiddleware: true,
	isEnabled: true,
	session: {
		enableRefreshOnWindowFocus: true,
		enableRefreshPeriodically: 1000 * 60 * 10, // 10 minutes
	},
	provider: {
		type: 'refresh',
		endpoints: {
			signIn: { path: '/login', method: 'post' },
			signOut: { path: '/logout', method: 'post' },
			refresh: { path: '/refresh-token', method: 'post' },
			getSession: { path: '/me', method: 'get' },
		},
		token: {
			signInResponseTokenPointer: '/accessToken',
			cookieName: 'auth.accessToken',
			sameSiteAttribute: 'lax',
			headerName: 'Authorization',
			type: 'Bearer',
			maxAgeInSeconds: 1000 * 60 * 60 * 24,
		},
		refreshToken: {
			signInResponseRefreshTokenPointer: '/refreshToken',
			refreshRequestTokenPointer: '/refreshToken',
			cookieName: 'auth.refreshToken',
			maxAgeInSeconds: 1000 * 60 * 60 * 24,
		},
		refreshOnlyToken: true,
		pages: {
			login: '/en/auth/login',
		},
		sessionDataType: {
			id: 'number',
			name: 'string',
			email: 'string',
			createAt: 'string',
			updateAt: 'null | string',
			role: 'array',
		},
	},

}

export default config
