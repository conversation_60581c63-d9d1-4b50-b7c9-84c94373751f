<template>
	<v-container>
		<v-data-table
			:headers="headers"
			:items="items"
			hide-default-footer
			:items-per-page="-1"
		>
			<template #top>
				<div class="text-end">
					<v-btn
						variant="text"
						prepend-icon="	i-mdi:plus"
						@click="setForSymbolEdit(-1)"
					>
						{{ $t('Groups.MT4.Crud.Symbols.add-symbol') }}
					</v-btn>
				</div>
			</template>
			<template #item.marginDivider="{ value }">
				<div v-if="value === 100/DBL_MAX">
					{{ $t('Groups.MT4.Crud.Symbols.default') }}
				</div>
				<div v-else>
					{{ 100 / value }}%
				</div>
			</template>
			<template #item.actions="{ index }">
				<v-btn
					icon="i-mdi:trash-can"
					size="small"
					variant="text"
					@click="deleteSymbol(index)"
				/>
				<v-btn
					icon="i-mdi:pencil"
					size="small"
					variant="text"
					@click="setForSymbolEdit(index)"
				/>
			</template>
		</v-data-table>
		<v-dialog
			v-model="editModel"
			max-width="400"
		>
			<v-confirm-edit
				v-slot="{ actions, model }"
				ref="confirmEditRef"
				v-model="currentSymbol"
				cancel-text="Reset"
				@save="saveSymbol"
			>
				<v-form ref="editFormRef">
					<v-card :title="editDialogTitle">
						<template #text>
							<api-items
								v-slot="props"
								:url="Symbols.MT4.Lookup.URL"
								:map-items="(items: Symbols.MT4.Lookup.GETResponse) => {
									return items.map((item) => {
										(item as any).props = { subtitle: item.subtitle }
										return item
									})
								}"
							>
								<v-autocomplete
									v-model="model.value.symbol"
									:label="$t('Groups.MT4.Crud.Symbols.symbol')"
									v-bind="props"
									:rules="rules({ required: true })"
									@update:model-value="symbolSelectHandler($event, model)"
								/>
							</api-items>
							<v-row no-gutters>
								<v-col class="pe-1">
									<v-text-field
										v-model.number="model.value.swapLong"
										:loading="isFetchingSymbol"
										type="number"
										hide-spin-buttons
										:label="$t('Groups.MT4.Crud.Symbols.long-position-swap')"
										:rules="rules({ required: true })"
									/>
									<v-combobox
										v-model.number="computed({
											get: () => 100 / model.value.marginDivider,
											set: (value) => model.value.marginDivider = 100 / value,
										}).value"
										:return-object="false"
										hide-spin-buttons
										:label="$t('Groups.MT4.Crud.Symbols.margin-percentage')"
										type="number"
										suffix="%"
										:items="[{ title: 'default', value: DBL_MAX }]"
										:rules="rules({ required: true })"
									/>
								</v-col>
								<v-col class="ps-1">
									<v-text-field
										v-model.number="model.value.swapShort"
										:loading="isFetchingSymbol"
										item-title="title"
										item-value="value"
										type="number"
										hide-spin-buttons
										:label="$t('Groups.MT4.Crud.Symbols.short-position-swap')"
										:rules="rules({ required: true })"
									/>
								</v-col>
							</v-row>
						</template>
						<template #actions>
							<v-btn @click="editModel = false">
								{{ $t('Groups.MT4.Crud.Symbols.cancel') }}
							</v-btn>
							<v-spacer />
							<component :is="actions" />
						</template>
					</v-card>
				</v-form>
			</v-confirm-edit>
		</v-dialog>
	</v-container>
</template>

<script lang="ts" setup>
import type { VConfirmEdit, VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Symbols } from '~/types'
import type { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const confirmEditRef = ref<InstanceType<typeof VConfirmEdit>>()

const editModel = ref(false)

const editFormRef = ref <InstanceType<typeof VForm>>()

const DBL_MAX = 100 / Number.MAX_VALUE

const isFetchingSymbol = ref(false)

const { t } = useI18n()

const headers = [
	{
		title: t('symbols.symbol'),
		value: 'symbol',
	},
	{
		title: t('Groups.MT4.Crud.Symbols.long'),
		value: 'swapLong',
	},
	{
		title: t('Groups.MT4.Crud.Symbols.short'),
		value: 'swapShort',
	},
	{
		title: t('Groups.MT4.Crud.Symbols.percentage'),
		value: 'marginDivider',
	},
	{
		title: '',
		value: 'actions',
	},
]

const confirm = useNuxtApp().$confirm

const currentSymbolIndex = ref<number>()

const defaultSymbol: Groups.MT4.GroupMargin = {
	symbol: '',
	swapLong: 0,
	swapShort: 0,
	marginDivider: 1,
	reserved: [0, 0, 0, 0, 0, 0, 0],
}
const currentSymbol = ref<Groups.MT4.GroupMargin>(defaultSymbol)

const items = computed<typeof p.item.specialSecuritiesSettings>({
	get() {
		return getRealSymbols(p.item.specialSecuritiesSettings)
	},
	set(value) {
		emit('update:item', {
			specialSecuritiesSettings: value,
		})
	},
})

const editDialogTitle = computed(() => {
	if (currentSymbolIndex.value === -1) {
		return t('Groups.MT4.Crud.Symbols.add-symbol-0')
	}
	return t('Groups.MT4.Crud.Symbols.edit-currentsymbol-value-', [currentSymbol.value?.symbol])
})

const deleteSymbol = (index: number) => {
	confirm({
		title: t('Groups.MT4.Crud.Symbols.delete-symbol'),
		text: t('Groups.MT4.Crud.Symbols.are-you-sure-you-want-to-'),
	}).then(() => {
		resetSymbol(index)
	}).catch()
}

const setForSymbolEdit = (index: number) => {
	currentSymbolIndex.value = index
	if (index === -1) {
		currentSymbol.value = { ...defaultSymbol }
	} else {
		currentSymbol.value = toRaw(useCloned(items.value[index]).cloned.value) as Groups.MT4.GroupMargin
	}
	editModel.value = true
}

const saveSymbol = async () => {
	const validation = await editFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	const index = items.value.findIndex(item => (item as Groups.MT4.GroupMargin).symbol === currentSymbol.value?.symbol)
	const clonedItems = useCloned(items.value).cloned.value
	if (index === -1) {
		clonedItems.push(currentSymbol.value!)
	} else {
		clonedItems[index] = currentSymbol.value!
	}
	items.value = fillEmpty(clonedItems)
	editModel.value = false
}

const resetSymbol = (index: number) => {
	const clonedItems = useCloned(p.item.specialSecuritiesSettings, { deep: true }).cloned.value
	clonedItems[index] = {}
	const filteredItems = getRealSymbols(clonedItems)
	items.value = fillEmpty(filteredItems)
}

const fillEmpty = (items: any[]) => {
	return items
	// const empty = {}
	// const result = []
	// for (let i = 0; i < MAX_SYMBOLS; i++) {
	// 	result.push(items[i] || empty)
	// }
	// return result
}

const getRealSymbols = (items: any[]) => {
	return items.filter(item => !!(item as Groups.MT4.GroupMargin).symbol)
}

const symbolSelectHandler = (symbol: Symbols.MT4.Lookup.SingleRecord['value'], model: Ref<Groups.MT4.GroupMargin>) => {
	const { $api } = useNuxtApp()

	isFetchingSymbol.value = true

	$api<Symbols.MT4._Id.GETResponse>(Symbols.MT4._Id.URL(symbol)).then((data) => {
		model.value.swapLong = data.swapLong
		model.value.swapShort = data.swapShort
		model.value.marginDivider = data.marginDivider
	}).finally(() => {
		isFetchingSymbol.value = false
	})
}
</script>
