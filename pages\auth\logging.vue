<template>
	<Title>{{ $t('logging.logging') }}</Title>
	<v-window
		:id="redirectPath"
		v-model="windowModel"
	>
		<v-window-item value="loadData">
			<v-banner v-if="error">
				{{ error }}
			</v-banner>
			<v-sheet
				v-else
				class="text-center"
			>
				<v-progress-linear
					indeterminate
					color="accent"
				/>
				<div class="py-3 text-subtitle-2">
					{{ info }}
				</div>
			</v-sheet>
		</v-window-item>

		<v-window-item value="chooseServer">
			<p>
				{{ $t('logging.please-choose-a-trading-server-to-continue') }}
			</p>
			<v-form
				v-model="isServerValid"
				@submit.prevent="serverChosenHandler"
			>
				<api-items
					v-slot="props"
					url="/my/trading-servers"
				>
					<v-select
						v-bind="props"
						v-model="tradingServerName"
						density="comfortable"
						prepend-icon="i-arcticons:metatrader-5"
						:placeholder="$t('logging.select-server')"
						flat
						color="primary"
						:rules="rules({ required: true })"
						variant="solo"
						item-title="displayName"
						item-value="name"
						hide-details
					>
						<!-- <template #selection="{ item }">
							<v-list-item
								lines="two"
								v-bind="props"
							>
								<v-list-item-title>
									{{ item.title }}
								</v-list-item-title>
								<v-list-item-subtitle>
									{{ item.raw.ipAddress }}:{{ item.raw.port }}
								</v-list-item-subtitle>
							</v-list-item>
						</template> -->
						<template #item="{ props: selectProps, item }">
							<v-list-item
								lines="two"
								v-bind="selectProps"
							>
								<v-list-item-subtitle>
									{{ item.raw.ipAddress }}:{{ item.raw.port }}
								</v-list-item-subtitle>
							</v-list-item>
						</template>
					</v-select>
				</api-items>

				<v-btn
					block
					color="primary"
					:disabled="!isServerValid"
					type="submit"
					class="my-4"
					size="large"
				>
					Continue
				</v-btn>
			</v-form>
		</v-window-item>
	</v-window>
</template>

<script lang="ts" setup>
import type { LoginHistoryItem } from '../auth.vue'
import { VerifyRejection } from '~/stores/auth'

definePageMeta({
	viewTransition: false,
})

const { t } = useI18n()

const info = ref('')

const windowModel = ref<'loadData' | 'chooseServer'>('loadData')

const isServerValid = ref(false)

const error = ref<false | string>(false)

const authStore = useAuthStore()

const prefStore = usePreferencesStore()

const router = useRouter()

const route = useRoute()

const { errorSnackbar } = useSnackbar()

const tradingServerName = ref<string | null>(null)

const redirectPath = computed<undefined | string>(() => {
	if (route.hash && route.hash !== '#/') {
		return (route.hash.substring(1))
	}
	return undefined
})

const hash = computed<undefined | string>(() => {
	if (route.hash && route.hash !== '#/') {
		return (route.hash /* .substring(1) */)
	}
	return undefined
})

const init = async () => {
	const currentTime = new Date().getTime()

	info.value = t('logging.getting-profile-data')

	try {
		await authStore.verifyUserCredentials()
	} catch (e: unknown) {
		const error = e as VerifyRejection
		if (error) {
			switch (error) {
				case VerifyRejection.TWO_FA_Required:{
					// const path = { name: 'auth-2fa', hash: hash.value }
					await router.push({ name: 'auth-2fa', hash: hash.value })
					return // Stop execution after redirection
				}
				case VerifyRejection.INVALID_Tokens:
					await router.push({ name: 'auth-login', hash: hash.value })
					errorSnackbar({
						title: t('logging.please-try-to-login-again'),
						text: t('logging.an-error-occurred-while-getting-user-credentials-p'),
					})
					return // Stop execution after redirection
			}
		}
	}

	info.value = t('logging.getting-preferences-data')
	try {
		await prefStore.load()
	} catch (e) {
		console.error(e)
		error.value = e?.toString?.() || t('logging.an-error-occurred-while-getting-user-preferences')
	}

	const timeTaken = new Date().getTime() - currentTime
	if (timeTaken < 500) {
		await new Promise(resolve => setTimeout(resolve, 500 - timeTaken))
	}
	// save to login history
	const loginHistory = useLocalStorage<LoginHistoryItem[]>('login-history', [], {})

	const loginHistoryItem = loginHistory.value.find(item => item.email === authStore.user?.email)

	if (loginHistoryItem) {
		loginHistoryItem.lastLogin = new Date().toISOString()
		loginHistoryItem.name = authStore.user!.name
	} else if (authStore.user!.email) {
		loginHistory.value.push({
			email: authStore.user!.email,
			name: authStore.user!.name,
			lastLogin: new Date().toISOString(),
		})
	}

	loginHistory.value.sort((a, b) => {
		return new Date(b.lastLogin).getTime() - new Date(a.lastLogin).getTime()
	})

	const tradingServerNames = authStore.user?.tradingServerList?.map(server => server.name) || []
	const prefServerName = prefStore.getKey('tradingServerName')

	// check if serverTradingId is set
	if (!prefServerName || (prefServerName && !tradingServerNames.includes(prefServerName))) {
		windowModel.value = 'chooseServer'
	} else {
		await navigateTo(redirectPath.value ?? { name: 'index' }, { replace: true })
	}
}

const serverChosenHandler = async () => {
	await prefStore.updateKey('tradingServerName', tradingServerName.value)
	router.replace(redirectPath.value ?? { name: 'index' })
}

const routeBaseName = useRouteBaseName()

const currentRouteBaseName = computed(() => {
	return routeBaseName(route)
})
watch(() => currentRouteBaseName.value, (val) => {
	if (val === 'auth-logging') {
		init()
	}
}, { immediate: true })
</script>
