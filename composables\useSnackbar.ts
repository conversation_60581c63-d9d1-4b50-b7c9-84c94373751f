import type { SnackbarService } from 'vue3-snackbar'
import { useSnackbar as useVue3Snackbar } from 'vue3-snackbar'

export function useSnackbar() {
	const $snackbar = useVue3Snackbar()

	type SnackbarMessage = Parameters<SnackbarService['add']>[0]

	const successSnackbar = (item: string | SnackbarMessage) => {
		if (typeof item === 'string') {
			item = { text: item }
		}
		$snackbar.add({ duration: 3000, dismissible: true, ...item, type: 'success' })
	}

	const errorSnackbar = (item: string | SnackbarMessage) => {
		if (typeof item === 'string') {
			item = { text: item }
		}
		$snackbar.add({ duration: 7000, dismissible: true, ...item, type: 'error' })
	}

	const infoSnackbar = (item: string | SnackbarMessage) => {
		if (typeof item === 'string') {
			item = { text: item }
		}
		$snackbar.add({ duration: 3000, dismissible: true, ...item, type: 'info' })
	}

	const warningSnackbar = (item: string | SnackbarMessage) => {
		if (typeof item === 'string') {
			item = { text: item }
		}
		$snackbar.add({ duration: 5000, dismissible: true, ...item, type: 'warning' })
	}

	return {
		snackbar: $snackbar,
		successSnackbar,
		errorSnackbar,
		infoSnackbar,
		warningSnackbar,
	}
}
