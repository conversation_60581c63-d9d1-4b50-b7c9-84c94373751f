import type { Permissions } from '../Permissions'

export namespace Auth {

	export namespace Login {
		export const URL = '/auth/login'

		export interface POSTRequest {
			email: string
			password: string
		}

		export interface POSTResponse {
			refreshToken: string
			accessToken: string
			refreshTokenExpiry: string
			accessTokenExpiry: string
			twoFactorEnabled: boolean
		}

	}
	export namespace ForgotPassword {
		export const URL = '/auth/forgot-password'

		export interface POSTRequest {
			email: string
		}

		export interface POSTResponse {

		}

	}

	export namespace ResetPassword {
		export const URL = '/auth/reset-password'

		export interface POSTRequest {
			email: string
			password: string
			confirmPassword: string
			resetToken: string
		}

		export interface POSTResponse {

		}

	}
	export namespace LoginMode {
		export const URL = '/auth/login-mode'

		export enum Modes {
			Database = 1,
			AzureAD = 2,
		}

		export interface GETResponse {
			loginMode: Modes
		}

	}
	export namespace RefreshToken {

		export const URL = '/auth/refresh-token'

		export interface POSTResponse {
			refreshToken: string
			accessToken: string
			refreshTokenExpiry: string
			accessTokenExpiry: string
			twoFactorEnabled: boolean
		}
	}
	export namespace Me {

		export const URL = '/auth/me'
		export interface PermissionList {
			id: number
			name: string
			createdAt: Date
		}

		export interface Role {
			id: number
			name: string
		}

		export interface TradingPlatform {
			id: number
			name: 'MT4' | 'MT5'
			createdAt: string
			updatedAt: string
		}

		export interface TradingServerList {
			id: number
			name: string
			displayName: string
			webLogin: number
			port: number
			tradingPlatformId: number
			tradingPlatform: TradingPlatform
			integrationType: number
			isActive: boolean
			createdAt: string
		}
		export interface GETResponse {
			id: number
			name: string
			email: string
			twoFactorEnabled: boolean
			twoFactorSecret: string
			createdAt: Date
			roleId: number
			role: Role
			// permissions: any[];
			permissionNames: Permissions.Names[]
			tradingServers: any[]
			// permissionList: PermissionList[];
			tradingServerList: TradingServerList[]
		}

	}
	export namespace TwoFactor {

		export namespace App {
			export namespace Generate {
				export const URL = '/2FA/app/generate'
				export interface GETResponse {
					code: string
					qrCodeImage: string
				}
			}
			export namespace Verify {
				export const URL = '/2FA/app/verify'
				export interface POSTRequest {
					code: string
				}
			}
		}
	}
	export namespace Logout {
		export const URL = '/auth/logout'
	}
	export namespace CheckTokensValidity {
		export const URL = '/auth/check-valid-tokens'
		export interface POSTRequest {
			accessTokenValid: boolean
			refreshTokenValid: boolean
		}
	}

	export namespace ChangePassword {
		export const URL = '/auth/change-password'
		export interface POSTRequest {
			password: string
			confirmPassword: string
		}

		export interface POSTResponse {
		}

	}

}
