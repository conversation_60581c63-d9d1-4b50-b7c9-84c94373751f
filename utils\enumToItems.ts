// eslint-disable-next-line @typescript-eslint/no-unused-vars
type EnumItem<T> = {
	title: string | number | symbol
	value: number
}

export default function<T extends object> (
	someEnum: T,
	i18nKey?: string,
	callback: null | ((item: EnumItem<T>) => EnumItem<T> | undefined) = null,
): Array<EnumItem<T>> {
	const { $i18n } = useNuxtApp()
	const keys = Object.keys(someEnum).filter(key => Number.isNaN(Number(key))) as Array<keyof typeof someEnum>

	return keys.map((key) => {
	// Calculate the bitwise value
		const valueParts = key.toString().split('_AND_')
		const valueSum = valueParts.reduce((sum, part) => {
			const enumValue = (someEnum as any)[part]
			if (typeof enumValue === 'number') {
				return sum | enumValue
			}
			return sum
		}, 0)

		let item: EnumItem<T> | undefined = {
			title: i18nKey ? $i18n.t(`${i18nKey}.${String(key)}`) : key,
			value: valueSum,
		}

		if (callback) {
			item = callback(item)
		}

		return item
	}).filter(item => !!item)
}
