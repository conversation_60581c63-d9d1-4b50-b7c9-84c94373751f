<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.login"
					type="number"
					hide-spin-buttons
					:label="$t('Manager.MT4.Crud.Common.login')"
					:rules="rules({ required: true, type: 'number', min: 1 })"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model="item.infoDepth"
					type="number"
					hide-spin-buttons
					:return-object="false"
					:label="$t('Manager.MT4.Crud.Common.data-available-for')"
					:items="infoDepthItems"
					suffix="days"
				/>
			</v-col>
		</v-row>
		<v-combobox
			v-model="groupsModel"
			:return-object="false"
			chips
			:delimiters="[',', ' ']"
			multiple
			closable-chips
			:label="$t('Manager.MT4.Crud.Common.groups')"
		/>
		<v-row>
			<v-col
				cols="12"
				md="auto"
			>
				<v-checkbox
					v-model="item.ipFilter"
					color="primary"
					:true-value="1"
					:false-value="0"
					:label="$t('Manager.MT4.Crud.Common.ip-filter')"
				/>
			</v-col>
			<v-col
				cols="12"
				md
			>
				<v-text-field
					v-model="ipFromModel"
					v-mask="Mask.IP"
					:label="$t('Manager.MT4.Crud.Common.from')"
					:disabled="!item.ipFilter"
					@blur="ipFromModelHandler"
				/>
			</v-col>
			<v-col
				cols="12"
				md
			>
				<v-text-field
					v-model="ipToModel"
					v-mask="Mask.IP"
					:label="$t('Manager.MT4.Crud.Common.to')"
					:disabled="!item.ipFilter"
					@blur="ipToModelHandler"
				/>
			</v-col>
		</v-row>
		<v-text-field
			v-model="item.mailbox"
			:label="$t('Manager.MT4.Crud.Common.mailbox-name')"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import type { VCheckbox } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Mask } from '~/types/Mask'
// import { Accounts } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { t } = useI18n()

const infoDepthItems = [
	{
		title: t('Manager.MT4.Crud.Common.unlimited'),
		value: 0,
	},
	7, 14, 30, 61, 180,
]

const groupsModel = computed<string[]>({
	get: () => p.item.groups.split(',').filter(Boolean),
	set: (value) => {
		emit('update:item', {
			groups: value.join(','),
		})
	},
})

const numberToIP = (value: number) => {
	const octets = []
	for (let i = 0; i < 4; i++) {
		octets.unshift(value & 0xFF)
		value >>= 8
	}
	return octets.join('.')
}

const ipToNumber = (value: string) => {
	const octets = value.split('.').map(Number)
	let result = 0
	for (let i = 0; i < 4; i++) {
		result <<= 8
		result |= octets[i]
	}
	return result
}
const ipFromModel = ref(numberToIP(p.item.ipFrom))

const ipFromModelHandler = () => {
	emit('update:item', {
		ipFrom: ipToNumber(ipFromModel.value),
	})
}

const ipToModel = ref(numberToIP(p.item.ipTo))

const ipToModelHandler = () => {
	emit('update:item', {
		ipTo: ipToNumber(ipToModel.value),
	})
}
</script>
