<template>
	<v-container>
		<v-defaults-provider
			:defaults="{
				VCheckbox: {
					color: 'primary',
					hideDetails: true,
					class: 'compact',
					trueValue: 1,
					falseValue: 0,
				},
			}"
		>
			<v-row dense>
				<v-col
					cols="12"
					md="5"
				>
					<api-items
						v-slot="props"
						ref="roleLookupRef"
						:url="Manager.MT4.Roles.Lookup.URL"
					>
						<v-text-field
							v-model="selectedRoleModel"
							item-title="name"
							item-value="permissions"
							:class="{ 'v-select--active-menu': !!menuModel }"
							:label="$t('Manager.MT4.Crud.Permissions.roles')"
							:append-inner-icon="`v-select__menu-icon ${$vuetify.icons.aliases?.dropdown}`"
							v-bind="omit(props, ['items'])"
						>
							<v-menu
								v-model="menuModel"
								activator="parent"
							>
								<v-list :items="props.items">
									<v-list-item
										v-for="(item, i) in props.items"
										:key="i"
										:title="item.name"
										:active="item.name === selectedRoleModel"
										@click="selectRole(item)"
									/>
								</v-list>
							</v-menu>
						</v-text-field>
					</api-items>
				</v-col>
				<v-col>
					<v-btn
						variant="text"
						height="48"
						prepend-icon="i-mdi:content-save"
						:disabled="!selectedRoleModel"
						:loading="isSaving"
						@click="saveRightsAsRole"
					>
						{{ $t('Manager.MT4.Crud.Permissions.save') }}
					</v-btn>
					<v-btn
						variant="text"
						height="48"
						prepend-icon="i-mdi:delete"
						:disabled="!canDeleteRole"
						:loading="isDeleting"
						@click="deleteRole"
					>
						{{ $t('Manager.MT4.Crud.Permissions.delete') }}
					</v-btn>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.manager"
						:label="$t('Manager.MT4.Crud.Permissions.manager')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.seeTrades"
						:label="$t('Manager.MT4.Crud.Permissions.supervise-trades')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.admin"
						:label="$t('Manager.MT4.Crud.Permissions.administrator')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.money"
						:label="$t('Manager.MT4.Crud.Permissions.accountant')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.reports"
						:label="$t('Manager.MT4.Crud.Permissions.reports')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.riskman"
						:label="$t('Manager.MT4.Crud.Permissions.risk-manager')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.email"
						:label="$t('Manager.MT4.Crud.Permissions.internal-mail-system')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.logs"
						:label="$t('Manager.MT4.Crud.Permissions.journals')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.news"
						:label="$t('Manager.MT4.Crud.Permissions.send-news')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.marketWatch"
						:label="$t('Manager.MT4.Crud.Permissions.market-watch')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.online"
						:label="$t('Manager.MT4.Crud.Permissions.connections-show-online-c')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.userDetails"
						:label="$t('Manager.MT4.Crud.Permissions.personal-details')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.plugins"
						:label="$t('Manager.MT4.Crud.Permissions.configure-server-plugins')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.serverReports"
						:label="$t('Manager.MT4.Crud.Permissions.automatic-server-reports')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.techSupport"
						:label="$t('Manager.MT4.Crud.Permissions.access-to-technical-support')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.market"
						:label="$t('Manager.MT4.Crud.Permissions.access-to-app-market')"
					/>
				</v-col>
			</v-row>
			<v-row class="mt-n5">
				<v-col
					cols="12"
					md="6"
				>
					<v-checkbox
						v-model="item.notifications"
						:label="$t('Manager.MT4.Crud.Permissions.push-notification')"
					/>
				</v-col>
			</v-row>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import type { VCheckbox } from 'vuetify/components'
import omit from 'lodash/omit'
import pick from 'lodash/pick'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Manager } from '~/types'
import type ApiItems from '~/components/ApiItems.vue'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const selectedRoleModel = ref<Manager.MT4.Roles.Role['name']>()

const roleLookupRef = ref<InstanceType<typeof ApiItems>>()

const { $api } = useNuxtApp()

const isSaving = ref(false)

const isDeleting = ref(false)

const menuModel = ref(false)

const selectRole = (role: Manager.MT4.Roles.Role) => {
	selectedRoleModel.value = role.name
	setRights(role.permissions)
}

const setRights = (rights: Manager.MT4.Roles.Role['permissions']) => {
	emit('update:item', {
		...rights,
	})
}

const saveRightsAsRole = () => {
	isSaving.value = true
	$api(Manager.MT4.Roles._Name.URL, {
		method: 'POST',
		body: {
			name: selectedRoleModel.value,
			permissions: pick(p.item, ['logs', 'news', 'admin', 'email', 'money', 'market', 'online', 'trades', 'manager', 'plugins', 'reports', 'riskman', 'seeTrades', 'marketWatch', 'techSupport', 'userDetails', 'notifications', 'serverReports']),
		},
	})
		.then(() => {
			roleLookupRef.value?.refresh()
		})
		.finally(() => {
			isSaving.value = false
		})
}

const canDeleteRole = computed(() => {
	return !!selectedRoleModel.value && roleLookupRef.value?.$bind.items.some(item => item.name === selectedRoleModel.value)
})

const deleteRole = () => {
	isDeleting.value = true
	$api(Manager.MT4.Roles._Name.DeleteURL(selectedRoleModel.value as string), {
		method: 'DELETE',
	})
		.then(() => {
			roleLookupRef.value?.refresh()
			selectedRoleModel.value = undefined
		})
		.finally(() => {
			isDeleting.value = false
		})
}
</script>
