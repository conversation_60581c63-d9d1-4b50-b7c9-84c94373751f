ARG NODE_VERSION
ARG TARGETPLATFORM
ARG TARGETARCH

# Stage 1: Build the application
FROM --platform=$TARGETPLATFORM node:${NODE_VERSION} as build

ENV NODE_VERSION=20
# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json /app/

ARG GITHUB_TOKEN
ENV GITHUB_TOKEN=${RELEASE_GITHUB_TOKEN}
ENV TARGETPLATFORM=${TARGETPLATFORM}
ENV NODE_VERSION=${NODE_VERSION}

# Install dependencies
RUN npm install

# Copy the rest of the application files
COPY . .

ENV NUXT_PUBLIC_SOCKET_URL=https://socket.ingotpilot.app
ENV NUXT_PUBLIC_API_BASE_URL=https://api.ingotpilot.app/api
# Build the application
RUN npm run generate

# Stage 2: Serve the application with nginx
FROM --platform=$TARGETPLATFORM nginx AS final

# Expose port 80
EXPOSE 80

# Copy the built files from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy the nginx configuration file
COPY /uat.nginx.conf /etc/nginx/conf.d/default.conf
