const config: Record<string, any> = {
	defaultLocale: 'en',
	supportedLocales: [
		{
			code: 'en',
			language: 'en-US',
			file: 'en.json',
			dir: 'ltr',
			name: 'English',
		},
		// {
		// 	code: 'ar',
		// 	language: 'ar-J<PERSON>',
		// 	file: 'ar.json',
		// 	dir: 'rtl',
		// 	name: 'العربية',
		// },
	],
	get supportedLocalesCodes() {
		return this.supportedLocales.map((locale: { code: any }) => locale.code)
	},
	breakpoints: {
		xs: 600,
		sm: 960,
		md: 1280,
		lg: 1920,
		xl: 2560,
	},
	isDev: process.env.NODE_ENV === 'development',
	isProd: process.env.NODE_ENV === 'production',
	isTesting: process.env.NODE_ENV === 'test',
}

export const {
	defaultLocale,
	supportedLocales,
	supportedLocalesCodes,
	breakpoints,
	isDev,
	isProd,
} = config

export default config
