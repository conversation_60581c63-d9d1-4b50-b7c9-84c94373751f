import type { Common } from '~/types/Common'

export namespace Users {
	export interface TradingServer {
		tradingServerId: number
		allowedGroups: string[]
		allowAll: 0 | 1
	}

	export interface GETQuery extends Common.PaginationQuery {
		name: string
		email: string
		roleId: string
	}
	export namespace _Id {
		export const URL = (_Id: number | string) => `/users/${_Id}`
		export interface GETResponse {
			id: number
			name: string
			email: string
			createdAt: string
			updatedAt: string
			roleId: number
			role: {
				id: number
				name: string
				displayName: string
				description: null
				createdAt: null
				updatedAt: null
				permissions: null
				permissionIds: null
			}
			permissions: number[]
			tradingServers: TradingServer[]
			permissionList: null
			tradingServerList: null
			twoFactorEnabled: boolean
			whiteIpAddress: string[]
		}

		export interface POSTRequest {
			name: string
			email: string
			roleId: number
			permissions: number[]
			tradingServers: TradingServer[]
			twoFactorEnabled: boolean
			whiteIpAddress: string[]
		}

	}

	export namespace Lookup {

		export const URL = '/users/lookup'
		export interface SingleRecord {
			id: number
			name: string
			email: string
			twoFactorEnabled: null
			createdAt: string
			updatedAt: null
			roleId: number
			role: {
				id: number
				name: string
				displayName: string
				description: null
				createdAt: null
				updatedAt: null
				permissions: null
				permissionIds: null
			}
			permissions: unknown[]
			tradingServers: unknown[]
			permissionList: null
			tradingServerList: null
		}

		export type GETResponse = SingleRecord[]
	}

}
