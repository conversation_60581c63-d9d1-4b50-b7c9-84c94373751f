<template>
	<v-list
		v-model:opened="openedModel"
		class="position-relative"
		width="100%"
		slim
		density="compact"
	>
		<!-- <template #prepend> -->
		<v-skeleton-loader
			type="list-item@10"
			:loading="status === 'pending'"
		>
			<div class="flex-grow-1">
				<template
					v-for="item in computedPaths"
					:key="item.value"
				>
					<PathTreeItem
						v-if="item.type === 'Path'"
						:item="item"
						@path-select="pathSelectHandler"
					/>
				</template>
				<div v-if="!hasPaths">
					<v-empty-state :text="`No path found matching &quot;${searchPathModel}&quot;`">
						<template #media>
							<v-icon
								class="mb-3"
								size="x-large"
								icon="i-mdi:folder-off-outline"
							/>
						</template>
					</v-empty-state>
				</div>
			</div>
		</v-skeleton-loader>
	</v-list>
</template>

<script lang="ts" setup>
// import { VTreeview } from 'vuetify/labs/VTreeview'
import PathTreeItem from './PathTreeItem.vue'
import { Groups } from '~/types'

defineOptions({
	inheritAttrs: false,
})
type Path = Groups.MT5.Paths.Lookup.SingleRecord

type Emit = {
	(e: 'loaded', value: Groups.MT5.Paths.Lookup.GETResponse): void
}

const emit = defineEmits<Emit>()

const model = defineModel('modelValue', {
	type: String,
	default: '',
})

const searchPathModel = defineModel('search', {
	type: String,
	default: '',
})

const extractPathArray = (path: string) => {
	const parts = path.split('\\')
	return parts.reduce((acc, part) => {
		// only add \\ if not first one
		const path = (acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part
		acc.push(path)
		return acc
	}, [''])
		.filter(part => !!part)
}

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const paths = ref<Path[]>([])

const openedModel = defineModel('opened', {
	type: Array,
	default: [],
})

openedModel.value = extractPathArray(model.value)

const menuModel = ref(false)

const { data, status, refresh } = useApi<Groups.MT5.Paths.Lookup.GETResponse>(Groups.MT5.Paths.Lookup.URL, {
	key: 'pathTree',
})

watch(() => data.value, (value) => {
	if (value) {
		paths.value = value
		emit('loaded', value)
	}
})

watch(() => model.value, (value) => {
	openedModel.value = extractPathArray(value)
})

const computedPaths = computed(() => {
	if (!debouncedSearchPathModel.value) {
		return paths.value
	}
	// filter the data based on the debouncedSearchPathModel model, find the item.title recursively in all children and keep the parent folder
	const filterData = (pathsData: Groups.MT5.Paths.Lookup.GETResponse, search: string) => {
		return pathsData.slice().filter((item) => {
			if (item.children.length) {
				const children = filterData(item.children, search)
				if (children.length) {
					item.children = children
					return true
				}
			}
			return item.title.toLowerCase().includes(search.toLowerCase())
		})
	}
	return filterData(paths.value, debouncedSearchPathModel.value)
})

const hasPaths = computed(() => !!computedPaths.value.length)

const pathSelectHandler = (path: string) => {
	model.value = path
	menuModel.value = false
}

type Exposed = {
	refresh: typeof refresh
}

defineExpose<Exposed>({
	refresh,
})
</script>
