import type { CombinationOf } from '../Helpers'
import type { Symbols } from '../Symbols'
import type { GroupMargin as MT4GroupMargin } from './MT4/GroupMargin'
import * as MT4GroupSec from './MT4/GroupSec'
import type * as MT5Commission from './MT5/Commission'
import type * as MT5Symbol from './MT5/Symbol'

export namespace Groups {

	export namespace Lookup {
		export const URL = '/groups/lookup'
		export type SingleRecord = string

		export type GETResponse = SingleRecord[]
	}

	export namespace MT5 {
		export namespace Lookup {
			export const URL = '/groups/mt5/lookup'
			export interface SingleRecord {
				title: string
				subtitle: string
				value: string
				type: 'path' | 'symbol'
				children: SingleRecord[]
			}

			export type GETResponse = SingleRecord[]
		}
		export namespace Paths {

			export namespace Lookup {
				export const URL = '/groups/mt5/paths/lookup'
				export interface SingleRecord {
					title: string
					value: string
					children: SingleRecord[]
					totalItems: number
					type: 'Path' | 'Groups'
				}

				export type GETResponse = SingleRecord[]

			}
		}

		/**
		 * Flags of group permissions are listed in IMTConGroup::EnPermissionsFlags.
		 */
		export enum EnPermissionsFlags {
			/**
			 * No permissions. Default value.
			 */
			PERMISSION_NONE = 0x00000000,

			/**
			 * Enable confirmation of certificates.
			 */
			PERMISSION_CERT_CONFIRM = 0x00000001,

			/**
			 * Allow client connections.
			 */
			PERMISSION_ENABLE_CONNECTION = 0x00000002,

			/**
			 * Forces users to change the master password during the first connection.
			 */
			PERMISSION_RESET_PASSWORD = 0x00000004,

			/**
			 * Requires the use of one-time passwords for connection.
			 */
			PERMISSION_FORCED_OTP_USAGE = 0x00000008,

			/**
			 * Displays a warning about the risks associated with operations on the financial markets upon client connection.
			 */
			PERMISSION_RISK_WARNING = 0x00000010,

			/**
			 * Enforce country-specific regulatory restrictions for retail clients.
			 */
			PERMISSION_REGULATION_PROTECT = 0x00000020,

			/**
			 * Allow accounts to subscribe to server push notifications about deals.
			 */
			PERMISSION_NOTIFY_DEALS = 0x00000040,

			/**
			 * Allow accounts to subscribe to server push notifications about orders.
			 */
			PERMISSION_NOTIFY_ORDERS = 0x00000080,

			/**
			 * Allow accounts to subscribe to server push notifications about balance operations.
			 */
			PERMISSION_NOTIFY_BALANCES = 0x00000100,

			/**
			 * End of enumeration. Corresponds to enabling of all permissions.
			 */
			PERMISSION_ALL,
		}

		/**
		 * Types of authentication of clients from the group are listed in IMTConGroup::EnAuthMode.
		 */
		export enum EnAuthMode {
			/**
			 * Standard authorization.
			 */
			AUTH_STANDARD = 0,

			/**
			 * Extended authentication with 1024-bit encryption.
			 */
			AUTH_RSA1024 = 1,

			/**
			 * Extended authorization with 2048-bit encryption.
			 */
			AUTH_RSA2048 = 2,

			/**
			 * Extended authentication using the third-party certificates.
			 */
			AUTH_RSA_CUSTOM = 4,
		}

		/**
		 * Report generation modes are listed in IMTConGroup::EnReportsMode.
		 */
		export enum EnReportsMode {
			/**
			 * Reports are disabled.
			 */
			REPORTS_DISABLED = 0,

			/**
			 * Enables the generation of both end-of-day and end-of-month data.
			 */
			REPORTS_FULL = 1,

			/**
			 * Enable data generation for reports only at the end of the day.
			 */
			REPORTS_DAY_ONLY = 2,

			/**
			 * Enable data generation for reports only at the end of the month.
			 */
			REPORTS_MONTH_ONLY = 3,

		}

		/**
		 * Report generation options are listed in IMTConGroup::EnReportsFlags.
		 */
		export enum EnReportsFlags {
			/**
			 * No additional options enabled.
			 */
			REPORTSFLAGS_NONE = 0,

			/**
			 * Enables sending of generated HTML report files to clients by email.
			 */
			REPORTSFLAGS_EMAIL = 1,

			/**
			 * Enables sending of report copies to a technical support email address.
			 */
			REPORTSFLAGS_SUPPORT = 2,

			/**
			 * Enables account state report generation.
			 */
			REPORTSFLAGS_STATEMENTS = 4,

			/**
			 * End of enumeration. All flags are enabled.
			 */
			REPORTSFLAGS_ALL,
		}

		/**
		 * Modes of news sending are listed in IMTConGroup::EnNewsMode.
		 */
		export enum EnNewsMode {
			/**
			 * News sending is disabled.
			 */
			NEWS_MODE_DISABLED = 0,

			/**
			 * Only news headers.
			 */
			NEWS_MODE_HEADERS = 1,

			/**
			 * Full package.
			 */
			NEWS_MODE_FULL = 2,
		}

		/**
		 * Modes of using the internal mail system are listed in IMTConGroup::EnMailMode.
		 */
		export enum EnMailMode {
			/**
			 * Disable the internal mail system.
			 */
			MAIL_MODE_DISABLED = 0,

			/**
			 * Enable the internal mail system.
			 */
			MAIL_MODE_FULL = 1,

		}

		/**
		 * The intervals of trading history available to clients from the group are listed in IMTConGroup::EnHistoryLimit.
		 */
		export enum EnHistoryLimit {
			/**
			 * The entire history.
			 */
			TRADE_HISTORY_ALL = 0,

			/**
			 * One month.
			 */
			TRADE_HISTORY_MONTHS_1 = 1,

			/**
			 * Three months.
			 */
			TRADE_HISTORY_MONTHS_3 = 2,

			/**
			 * Six months.
			 */
			TRADE_HISTORY_MONTHS_6 = 3,

			/**
			 * One year.
			 */
			TRADE_HISTORY_YEAR_1 = 4,

			/**
			 * Two years.
			 */
			TRADE_HISTORY_YEAR_2 = 5,

			/**
			 * Three years.
			 */
			TRADE_HISTORY_YEAR_3 = 6,

		}

		/**
		 * Modes of including floating profit/loss into free margin calculation are listed in IMTConGroup::EnFreeMarginMode.
		 */
		export enum EnFreeMarginMode {
			/**
			 * Do not use unrealized profit/loss.
			 */
			FREE_MARGIN_NOT_USE_PL = 0,

			/**
			 * Use unrealized profit/loss.
			 */
			FREE_MARGIN_USE_PL = 1,

			/**
			 * Use unrealized profit.
			 */
			FREE_MARGIN_PROFIT = 2,

			/**
			 * Use unrealized loss.
			 */
			FREE_MARGIN_LOSS = 3,

		}

		/**
		 * Modes for checking Margin Call and Stop Out are listed in IMTConGroup::EnStopOutMode.
		 */
		export enum EnStopOutMode {
			/**
			 * The levels of Margin Call and Stop Out in percentage terms.
			 */
			STOPOUT_PERCENT = 0,

			/**
			 * The levels of Margin Call and Stop Out in money terms.
			 */
			STOPOUT_MONEY = 1,

		}

		/**
		 * Group trade options are enumerated in IMTConGroup::EnTradeFlags.
		 */
		export enum EnTradeFlags {
			/**
			 * Options are disabled.
			 */
			TRADEFLAGS_NONE = 0x00000000,

			/**
			 * Allow charging of swaps.
			 */
			TRADEFLAGS_SWAPS = 0x00000001,

			/**
			 * Enable trailing stop.
			 */
			TRADEFLAGS_TRAILING = 0x00000002,

			/**
			 * Enable trading using Expert Advisors.
			 */
			TRADEFLAGS_EXPERTS = 0x00000004,

			/**
			 * Enable order expiration.
			 */
			TRADEFLAGS_EXPIRATION = 0x00000008,

			/**
			 * Allows using the Signals service in trading terminals.
			 */
			TRADEFLAGS_SIGNALS_ALL = 0x00000010,

			/**
			 * Allow only using signals from the company's servers.
			 */
			TRADEFLAGS_SIGNALS_OWN = 0x00000020,

			/**
			 * Automatically execute DEAL_SO_COMPENSATION operation on negative balance after Stop Out.
			 */
			TRADEFLAGS_SO_COMPENSATION = 0x00000040,

			/**
			 * Perform Stop out on accounts with zero margin and negative equity.
			 */
			TRADEFLAGS_SO_FULLY_HEDGED = 0x00000080,

			/**
			 * Close positions by FIFO rule.
			 */
			TRADEFLAGS_FIFO_CLOSE = 0x00000100,

			/**
			 * Prohibit opening of opposite positions and orders.
			 */
			TRADEFLAGS_HEDGE_PROHIBIT = 0x00000200,

			/**
			 * Calculate deal execution costs and display them in client terminals.
			 */
			TRADEFLAGS_DEAL_COST = 0x00000400,

			/**
			 * Withdraw credit funds on negative balance compensation.
			 */
			TRADEFLAGS_SO_COMPENSATION_CREDIT = 0x00000800,
			/**
			 * End of enumeration. All flags are enabled.
			 */
			TRADEFLAGS_ALL,
		}

		/**
		 * Modes of using profit/loss recorded during a trading day in free margin calculation are listed in IMTConGroup::EnMarginFreeProfitFlags.
		 */
		export enum EnMarginFreeProfitFlags {
			/**
			 * Taking into account the profit and loss recorded during the trading day when calculating the free margin.
			 */
			FREE_MARGIN_PROFIT_PL = 0,

			/**
			 * Taking into account the loss recorded during the trading day when calculating the free margin.
			 */
			FREE_MARGIN_PROFIT_LOSS = 1,
		}

		/**
		 * Authentication modes using one-time passwords are enumerated in IMTConGroup::EnAuthOTPMode.
		 */
		export enum EnAuthOTPMode {
			/**
			 * Authentication via one-time passwords is disabled.
			 */
			AUTH_OTP_DISABLED = 0,

			/**
			 * Standard generator of one-time passwords TOTP SHA-256 for all connections.
			 */
			AUTH_OTP_TOTP_SHA256 = 1,

			/**
			 * Standard generator of one-time passwords TOTP SHA-256 only for web terminal connections.
			 */
			AUTH_OTP_TOTP_SHA256_WEB = 2,

		}

		/**
		 * Modes of money transfer between accounts are listed in IMTConGroup::EnTransferMode.
		 */
		export enum EnTransferMode {
			/**
			 * Transfer of funds is disabled.
			 */
			TRANSFER_MODE_DISABLED = 0,

			/**
			 * Transfer of funds is only allowed between accounts with matching name and email.
			 */
			TRANSFER_MODE_NAME = 1,

			/**
			 * Transfer of funds is only allowed between accounts from the same group or subgroup.
			 */
			TRANSFER_MODE_GROUP = 2,

			/**
			 * Transfer of funds is allowed with matching names, emails, and group/subgroup.
			 */
			TRANSFER_MODE_NAME_GROUP = 3,

		}

		/**
		 * Risk management models, which define the type of pre-trade control and the system of positions used, are listed in IMTConGroup::EnMarginMode.
		 */
		export enum EnMarginMode {
			/**
			 * Used for the OTC market with netting position accounting system.
			 */
			MARGIN_MODE_RETAIL = 0,

			/**
			 * Used on exchange markets based on discounts specified in symbol settings.
			 */
			MARGIN_MODE_EXCHANGE_DISCOUNT = 1,

			/**
			 * Used for the OTC market with hedging position accounting system.
			 */
			MARGIN_MODE_RETAIL_HEDGED = 2,

		}

		export enum NewsLanguage {
			Afrikaans = 54,
			Albanian = 28,
			Arabic = 1,
			Armenian = 43,
			Azerbaijani = 44,
			Basque = 45,
			Belarusian = 35,
			Bulgarian = 2,
			ChineseTraditional = 4,
			Croatian = 26,
			Czech = 3,
			Danish = 6,
			Dutch = 7,
			English = 0,
			Estonian = 8,
			Finnish = 9,
			French = 12,
			Georgian = 46,
			German = 13,
			Greek = 14,
			Hebrew = 15,
			Hindi = 47,
			Hungarian = 16,
			Indonesian = 17,
			Italian = 18,
			Japanese = 19,
			Korean = 20,
			Latvian = 38,
			Lithuanian = 39,
			Macedonian = 29,
			Norwegian = 21,
			Oriya = 48,
			Persian = 49,
			Polish = 22,
			Portuguese = 23,
			Romanian = 24,
			Russian = 25,
			Slovak = 27,
			Slovenian = 30,
			Spanish = 10,
			Swedish = 11,
			Tatar = 40,
			Thai = 31,
			Turkish = 32,
			Ukrainian = 33,
			Urdu = 50,
			Vietnamese = 34,
		}

		export enum EnMarginFlags {
			MARGIN_FLAGS_NONE = 0,
			/**
			 * Clear accumulated profit at the end of the day.
			 */
			MARGIN_FLAGS_CLEAR_ACC = 1,
		};

		export type MarginFlags = MT5Symbol.MarginFlags

		// export type Symbol = Omit<Defaultable<MT5Symbol.Symbol>, 'path' | 'permissionsBookdepth' | 'permissionsFlags'> & {
		// 	path: MT5Symbol.Symbol['path']
		// 	permissionsBookdepth: number
		// 	permissionsFlags: EnPermissionsFlags
		// }
		// export type Symbol = {}

		export interface Symbol {
			path: string
			spreadDiff: number
			spreadDiffDefault: boolean
			spreadDiffBalance: number
			spreadDiffBalanceDefault: boolean
			stopsLevel: number
			stopsLevelDefault: boolean
			freezeLevel: number
			freezeLevelDefault: boolean
			swap3Day: number
			swap3DayDefault: boolean
			swapYearDaysDefault: boolean
			volumeMin: number
			volumeMinDefault: boolean
			volumeMax: number
			volumeMaxDefault: boolean
			volumeStep: number
			volumeStepDefault: boolean
			volumeLimit: number
			volumeLimitDefault: boolean
			ieVolumeMax: number
			ieVolumeMaxDefault: boolean
			volumeMinExt: number
			volumeMinExtDefault: boolean
			volumeMaxExt: number
			volumeMaxExtDefault: boolean
			volumeStepExt: number
			volumeStepExtDefault: boolean
			volumeLimitExt: number
			volumeLimitExtDefault: boolean
			ieVolumeMaxExt: number
			ieVolumeMaxExtDefault: boolean
			marginInitial: number
			marginInitialDefault: boolean
			marginMaintenance: number
			marginMaintenanceDefault: boolean
			marginLong: number
			marginLongDefault: boolean
			marginShort: number
			marginShortDefault: boolean
			marginLimit: number
			marginLimitDefault: boolean
			marginStop: number
			marginStopDefault: boolean
			marginStopLimit: number
			marginStopLimitDefault: boolean
			swapLong: number
			swapLongDefault: boolean
			swapShort: number
			swapShortDefault: boolean
			marginRateInitial: number
			marginRateInitialDefault: boolean
			marginRateMaintenance: number
			marginRateMaintenanceDefault: boolean
			marginRateLiquidity: number
			marginRateLiquidityDefault: boolean
			marginHedged: number
			marginHedgedDefault: boolean
			marginRateCurrency: number
			marginRateCurrencyDefault: boolean
			swapRateSunday: number
			swapRateSundayDefault: boolean
			swapRateMonday: number
			swapRateMondayDefault: boolean
			swapRateTuesday: number
			swapRateTuesdayDefault: boolean
			swapRateWednesday: number
			swapRateWednesdayDefault: boolean
			swapRateThursday: number
			swapRateThursdayDefault: boolean
			swapRateFriday: number
			swapRateFridayDefault: boolean
			swapRateSaturday: number
			swapRateSaturdayDefault: boolean
			reTimeout: number
			reTimeoutDefault: boolean
			reFlags: Symbols.MT5.RequestFlags
			reFlagsDefault: boolean
			ieCheckMode: Symbols.MT5.TradeInstantFlags
			ieCheckModeDefault: boolean
			ieTimeout: number
			ieTimeoutDefault: boolean
			ieSlipProfit: number
			ieSlipProfitDefault: boolean
			ieSlipLosing: number
			ieSlipLosingDefault: boolean
			bookDepthLimit: number
			ieFlags: number
			ieFlagsDefault: boolean
			swapYearDays: number
			swapFlags: number
			swapFlagsDefault: boolean
			marginMaintenanceBuy: number
			marginMaintenanceSell: number
			marginMaintenanceBuyLimit: number
			marginMaintenanceSellLimit: number
			marginMaintenanceBuyStop: number
			marginMaintenanceSellStop: number
			marginMaintenanceBuyStopLimit: number
			marginMaintenanceSellStopLimit: number
			marginInitialBuy: number
			marginInitialSell: number
			marginInitialBuyLimit: number
			marginInitialSellLimit: number
			marginInitialBuyStop: number
			marginInitialSellStop: number
			marginInitialBuyStopLimit: number
			marginInitialSellStopLimit: number
			tradeMode: Symbols.MT5.TradeMode
			tradeModeDefault: boolean
			execMode: Symbols.MT5.ExecutionMode
			execModeDefault: boolean
			fillFlags: Symbols.MT5.FillingFlags
			fillFlagsDefault: boolean
			expirFlags: Symbols.MT5.ExpirationFlags
			expirFlagsDefault: boolean
			marginFlags: Symbols.MT5.MarginFlags
			marginFlagsDefault: boolean
			swapMode: number
			swapModeDefault: boolean
			orderFlags: number
			orderFlagsDefault: boolean
			permissionsFlags: MT5Symbol.EnPermissionsFlags
		}

		export type Commission = MT5Commission.Commission

		export type MTGroupStandard = {

			/**
			 * The name of a group, including a path to it in accordance with the hierarchy.
			 */
			group: string

			/**
			 * The ID of the trade server, to which the group is linked.
			 */
			server: number

			/**
			 * Flags of group permissions. Passed as a value of the EnPermissionsFlags enumeration (sum of values of appropriate flags).
			 */
			permissionsFlags: EnPermissionsFlags

			/**
			 * Authorization mode for accounts in the group. Passed in a value of the EnAuthMode enumeration.
			 */
			authMode: EnAuthMode

			authOTPMode: EnAuthOTPMode

			/**
			 * The minimum password length for accounts in the group. The minimum possible password length is 8 characters and the maximum is 16 characters.
			 */
			authPasswordMin: number

			/**
			 * Name of the company that services the group.
			 */
			company: string
			/**
			 * The website address of deposit page
			 */

			companyDepositPage: string
			/**
			 * The website address of withdrawal page
			 */
			companyWithdrawalPage: string

			/**
			 * The website address of the company that services the group.
			 */
			companyPage: string

			/**
			 * The email address of the company that services the group.
			 */
			companyEmail: string

			/**
			 * The technical support website address of the company that services the group.
			 */
			companySupportPage: string

			/**
			 * The technical support email address of the company that services the group.
			 */
			companySupportEmail: string

			/**
			 * The name of the subdirectory that stores the templates of reports, emails, etc. for the company that services this group.
			 */
			companyCatalog: string

			/**
			 * The group deposit currency.
			 */
			currency: string

			/**
			 * The number of digits after the decimal point in the group deposit currency.
			 */
			currencyDigits: number

			/**
			 * Report generation modes. Passed in a value of the EnReportsMode enumeration.
			 */
			reportsMode: EnReportsMode

			/**
			 * Report sending options. Passed as a value of the EnReportsFlags enumeration (sum of values of appropriate flags).
			 */
			reportsFlags: EnReportsFlags

			/**
			 * Mail server for reports
			 */

			reportsEmail: string

			/**
			 * Address of SMTP server for sending reports.
			 */
			reportsSMTP: string

			/**
			 * A login for the authorization on the SMTP server that is used for sending reports.
			 */
			reportsSMTPLogin: string

			/**
			 * A password for the authorization on the SMTP server that is used for sending reports.
			 */
			reportsSMTPPass: string

			/**
			 * The mode of news sending to the clients from the group. Passed in a value of the EnNewsMode enumeration.
			 */
			newsMode: EnNewsMode

			/**
			 * The categories of news received by the group. Use the backslash character "\" to specify subcategories.
			 */
			newsCategory: string

			/**
			 * The array of languages, in which the group receives news. The language is specified in the LANGID format used in the MS Windows (value from Prim.lang.identifier).
			 */
			newsLangs: NewsLanguage[]

			/**
			 * The mode of operation of the internal mail system for the group. Passed in a value of the EnMailMode enumeration.
			 */
			mailMode: EnMailMode

			/**
			 * Trade options of the group. Passed as a value of the EnTradeFlags enumeration (sum of values of appropriate flags).
			 */
			tradeFlags: EnTradeFlags

			/**
			 * The annual interest rate on deposits of the group accounts.
			 */
			tradeInterestrate: number

			/**
			 * The amount of additional funds that a brokerage company can provide to a client for opening a position with a volume larger than allowed by the client's current funds.
			 */
			tradeVirtualCredit: number

			/**
			 * The mode of using floating profit/loss in the free margin. Passed in a value of the EnFreeMarginMode enumeration.
			 */
			marginFreeMode: EnFreeMarginMode

			/**
			 * The mode of checking the levels of Stop Out and Margin Call. Passed in a value of the EnStopOutMode enumeration.
			 */
			marginSOMode: EnStopOutMode

			/**
			 * The level of Margin Call. Units are determined by the MarginSOMode parameter.
			 */
			marginCall: number

			/**
			 * The level of Stop Out. Units are determined by the MarginSOMode parameter.
			 */
			marginStopOut: number

			/**
			 * The mode of using the profit/loss fixed during a trade day in the free margin.
			 */
			marginFreeProfitMode: EnMarginFreeProfitFlags

			/**
			 * The risk management model of the group. Passed in a value of the EnMarginMode enumeration.
			 */
			marginMode: EnMarginMode

			/**
			 * The default credit leverage for demo accounts opened in the group.
			 */
			demoLeverage: number

			/**
			 * The default amount of deposit for demo accounts opened in the group.
			 */
			demoDeposit: number

			/**
			 * The maximum number of days, for which the group can request data on conducted trade operation. Passed in a value of the EnHistoryLimit enumeration.
			 */
			limitHistory: EnHistoryLimit

			/**
			 * The maximum number of orders that can be simultaneously placed by an account from this group.
			 */
			limitOrders: number

			/**
			 * The maximum number of symbols, for which an account can simultaneously receive quotes.
			 */
			limitSymbols: number

			/**
			 * Get and set the maximum number of open positions that can be present simultaneously on a client account from this group.
			 */
			limitPositions: number

			/**
			 * Currently the field is not used.
			 */
			// limitPositionsVolume?: number;

			/**
			 * The array of commission settings.
			 */
			commissions: Commission[]

			/**
			 * The array of individual symbol settings.
			 */
			symbols: Symbol[]

			/**
			 * Get and set the mode of money transfer between accounts.
			 */
			tradeTransferMode: EnTransferMode
			/**
			 * inactivity period in days
			 */

			demoTradesClean: number

			marginFlags: EnMarginFlags
			marginFloatingLeverage: string

		}

		// Example usage
		/* const exampleGroupConfiguration: GroupConfiguration = {
		  group: "Example Group",
		  server: 123,
		  permissionsFlags: 0,
		  authMode: 1,
		  authPasswordMin: 8,
		  company: "Example Company",
		  companyPage: "https://example.com",
		  companyEmail: "<EMAIL>",
		  companySupportPage: "https://support.example.com",
		  companySupportEmail: "<EMAIL>",
		  companyCatalog: "reports",
		  currency: "USD",
		  currencyDigits: 2,
		  reportsMode: 1,
		  reportsFlags: 0,
		  reportsSMTP: "smtp.example.com",
		  reportsSMTPLogin: "login",
		  reportsSMTPPass: "password",
		  newsMode: 1,
		  newsCategory: "finance",
		  newsLangs: [1033],
		  mailMode: 1,
		  tradeFlags: 0,
		  tradeInterestRate: 5.0,
		  tradeVirtualCredit: 1000.0,
		  marginFreeMode: 0,
		  marginSOMode: 0,
		  marginCall: 50.0,
		  marginStopOut: 30.0,
		  marginFreeProfitMode: 1,
		  marginMode: 0,
		  demoLeverage: 100,
		  demoDeposit: 10000.0,
		  limitHistory: 30,
		  limitOrders: 100,
		  limitSymbols: 50,
		  limitPositions: 20,
		  commissions: [],
		  symbols: [],
		  tradeTransferMode: 0
		}; */

		export namespace _Id {
			export const URL = (_Id: number | string) => `/groups/mt5/${encodeURIComponent(_Id)}`

			export interface GETResponse extends MTGroupStandard { }

			export interface POSTRequest extends MTGroupStandard { }

			export interface PUTRequest extends MTGroupStandard { }

		}

	}

	export namespace MT4 {

		// Enumerations used in the structure

		// One Time Password (OTP) Use Modes
		/**
		 * One Time Password (OTP) Use Modes
		 */
		export enum OtpMode {
			/**
			 * A ban to use One Time Password for the clients in the group
			 */
			OTP_MODE_DISABLED = 0,

			/**
			 * One Time Passwords are allowed, the standard TOTP SHA-256 generator is used.
			 */
			OTP_MODE_TOTP_SHA256 = 1,
		}

		/**
		 * Free Margin Calculation Mode
		 */
		export enum MarginMode {
			/**
			 * Do not take into account the profit/loss of current open positions.
			 */
			MARGIN_MODE_DONT_USE,

			/**
			 * Take into account the profit and the loss of current open positions.
			 */
			MARGIN_MODE_USE_ALL,

			/**
			 * Take into account only the profit of current open positions.
			 */
			MARGIN_MODE_USE_PROFIT,

			/**
			 * Take into account only the loss of current open positions.
			 */
			MARGIN_MODE_USE_LOSS,
		}

		/**
		 * Modes of Margin Call and Stop Out Specification
		 */
		export enum MarginType {
			/**
			 * The levels are specified as a percentage.
			 */
			MARGIN_TYPE_PERCENT,

			/**
			 * The levels are specified in the deposit currency.
			 */
			MARGIN_TYPE_CURRENCY,
		}

		/**
		 * News Feeding Modes
		 */
		export enum NewsFeedingMode {
			/**
			 * News is prohibited.
			 */
			NEWS_NO,

			/**
			 * Only news headers are sent to clients.
			 */
			NEWS_TOPICS,

			/**
			 * Entire newsletters.
			 */
			NEWS_FULL,
		}

		/**
		 * Flags of Group Permissions
		 */
		export enum GroupPermissionFlags {
			/**
			 * Allow the internal mail system.
			 */
			ALLOW_FLAG_EMAIL = 1,

			/**
			 * Allow the use of trailing stop.
			 */
			ALLOW_FLAG_TRAILING = 2,

			/**
			 * Allow use of Expert Advisors for trading.
			 */
			ALLOW_FLAG_ADVISOR = 4,

			/**
			 * Allow the use of expiration of pending orders in client terminals.
			 */
			ALLOW_FLAG_EXPIRATION = 8,

			/**
			 * Allow the unlimited use of the Signals service in the trading terminals.
			 */
			ALLOW_FLAG_SIGNALS_ALL = 16,

			/**
			 * Allow only the signals that are based on the accounts created with the same broker.
			 */
			ALLOW_FLAG_SIGNALS_OWN = 32,

			/**
			 * If the flag is enabled, a risk warning is displayed upon client connection.
			 */
			ALLOW_FLAG_RISK_WARNING = 64,

			/**
			 * The flag of a compulsory use of One Time Passwords.
			 */
			ALLOW_FLAG_FORCED_OTP_USAGE = 128,
		}

		export enum NewsLanguage {
			Afrikaans = 54,
			Albanian = 28,
			Arabic = 1,
			Armenian = 43,
			Azerbaijani = 44,
			Basque = 45,
			Belarusian = 35,
			Bulgarian = 2,
			ChineseTraditional = 4,
			Croatian = 26,
			Czech = 3,
			Danish = 6,
			Dutch = 7,
			English = 0,
			Estonian = 8,
			Finnish = 9,
			French = 12,
			Georgian = 46,
			German = 13,
			Greek = 14,
			Hebrew = 15,
			Hindi = 47,
			Hungarian = 16,
			Indonesian = 17,
			Italian = 18,
			Japanese = 19,
			Korean = 20,
			Latvian = 38,
			Lithuanian = 39,
			Macedonian = 29,
			Norwegian = 21,
			Oriya = 48,
			Persian = 49,
			Polish = 22,
			Portuguese = 23,
			Romanian = 24,
			Russian = 25,
			Slovak = 27,
			Slovenian = 30,
			Spanish = 10,
			Swedish = 11,
			Tatar = 40,
			Thai = 31,
			Turkish = 32,
			Ukrainian = 33,
			Urdu = 50,
			Vietnamese = 34,
		}

		export type GroupMargin = MT4GroupMargin

		export type GroupSec = MT4GroupSec.GroupSec

		export const AutoCloseoutMode = MT4GroupSec.AutoCloseoutMode

		export const CommissionCalculationType = MT4GroupSec.CommissionCalculationType

		export const CommissionUnit = MT4GroupSec.CommissionUnit

		export const ExecutionMode = MT4GroupSec.ExecutionMode

		export const TradeRights = MT4GroupSec.TradeRights

		export type MTGroupStandard = {
			group: string
			enable: 0 | 1
			timeout: number
			otpMode: OtpMode
			company: string
			signature: string
			supportPage: string
			smtpServer: string
			smtpLogin: string
			smtpPassword: string
			supportEmail: string
			templatesPath: string
			copies: number
			reports: number
			defaultLeverage: number
			defaultDeposit: number
			maxSecurities: number
			securityGroups: GroupSec[]
			specialSecuritiesSettings: (GroupMargin | {})[]
			securitiesSettingsCount: number
			currency: string
			credit: number
			marginCallLevel: number
			marginMode: MarginMode
			marginStopOut: number
			interestRate: number
			useSwap: number
			news: NewsFeedingMode
			rights: CombinationOf<GroupPermissionFlags>
			checkIEPrices: number
			maxPositions: number
			closeReopen: number
			hedgeProhibited: number
			closeFIFO: number
			hedgeLargeLeg: number
			unusedRights: [0, 0]
			securitiesHash: string
			marginType: MarginType
			archivePeriod: number
			archiveMaxBalance: number
			stopoutSkipHedged: number
			archivePendingPeriod: number
			newsLanguages: (NewsLanguage | 0)[]
			newsLanguagesTotal: number
			reserved: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/groups/mt4/${encodeURIComponent(_Id)}`

			export interface GETResponse extends MTGroupStandard { }

			export interface POSTRequest extends MTGroupStandard { }

			export interface PUTRequest extends MTGroupStandard { }

		}
	}
}
