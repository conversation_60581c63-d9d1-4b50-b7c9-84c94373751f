export namespace Transactions {
	export enum Status {
		Pending = 0,
		Approved = 1,
		Rejected = 2,
	}
	export namespace _Id {
		export const URL = (_Id: number | string) => `/transactions/${_Id}`
		export interface GETResponse {
			id: number
			actionId: number
			actionName: string
			tradingServerId: number
			tradingServerName: null
			receiverAccount: string
			senderAccount: null
			requestedBy: number
			requestedByUserName: string
			requestedUserRoleName: string
			requestedAmount: number
			receivedAmount: number
			requestedCurrencyId: number
			requestedCurrencySymbol: null
			receivedCurrencyId: number
			receivedCurrencySymbol: null
			exchangeRate: number
			status: Status
			statusName: string
			executedBy: number
			canExecute: boolean
			executedAt: string
			createdAt: string
			updatedAt: null
		}

		export interface PUTRequest {
			status: number
			comment: string
		}
		export interface Account {
			login: string
			group: string
			currency: string
		}
		export interface POSTRequest {
			actionId: number
			toAccount: Account
			fromAccount?: Account
			amount: number
		}

		export namespace Workflow {
			export enum Event {
				Reviewer = 'Reviewer',
				Approval = 'Approval',
				Fallback = 'Fallback',
				Requester = 'Requester',
			}

			export const URL = (_Id: number | string) => `${Transactions._Id.URL(_Id)}/workflow`
			export interface SingleRecord {
				id: number
				comment: string
				flowableType: Event
				roleName: string
				userName: string
				status: Status
				processedAt: string
			}

			export type GETResponse = SingleRecord[]
		}
	}

}
