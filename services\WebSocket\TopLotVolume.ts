import type { Socket } from 'socket.io-client'
import { WebSocketService } from './Base'
import type { ClientToServerEvents, ServerToClientEvents } from '~/types/Common/Socket.IO'

export type UpdateHandler = (data: SocketData) => void

export interface SocketData {
	[category: string]: Category
}

export interface Category {
	HedgingVolume: number
	ClientVolume: number
	Ratio: number
}

export interface Response extends ServerToClientEvents {
	update: UpdateHandler
}

export interface Request extends ClientToServerEvents {}

export class TopLotVolume extends WebSocketService<Socket<Response, Request>> {
	constructor() {
		super('top-lot-volume')
	}

	onUpdate(callback: UpdateHandler) {
		this.socket?.on('update', callback)
		return this
	}
}
