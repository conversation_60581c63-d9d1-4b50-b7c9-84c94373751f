import type { RouteLocationAsPath, RouteLocationAsRelative, RouteLocationAsString } from 'vue-router'

export default (route: RouteLocationAsString | RouteLocationAsRelative | RouteLocationAsPath) => {
	const router = useRouter()
	if (!route) {
		return true
	}
	// get the permission from the item.to defined in the route meta
	// if the permission is not defined, return true
	const resolvedRoute = router.resolve(route)

	const permission = resolvedRoute.meta.permission

	if (!permission) {
		return true
	}

	return can(permission)
}
