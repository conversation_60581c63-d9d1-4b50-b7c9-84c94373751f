<template>
	<info-tooltip
		open-on-hover
		:disabled="cannot('users.view')"
	>
		<slot />
		<template #info>
			<v-lazy
				v-model="lazyModel"
				min-width="220"
			>
				<v-skeleton-loader
					:loading="status === 'pending'"
					type="paragraph@1"
				>
					<div v-if="status === 'success'">
						<div class="mb-1">
							<b>{{ $t('UserInfoTooltip.name') }}</b> {{ data?.name }}
						</div>
						<div class="my-1">
							<b>{{ $t('UserInfoTooltip.email') }}</b> {{ data?.email }}
						</div>
						<div class="mt-1">
							<b>{{ $t('UserInfoTooltip.role') }} </b> {{ data?.role?.displayName }}
						</div>
					</div>
					<v-list-item
						v-else
						title="Couldn't Load User Info"
						subtitle="It could be the user has been deleted."
					>
						<template #prepend>
							<v-icon
								icon="i-mdi:alert"
								color="warning"
							/>
						</template>
					</v-list-item>
				</v-skeleton-loader>
			</v-lazy>
		</template>

		<template #actions>
			<v-spacer />
			<v-btn
				v-if="can('users.edit')"
				:disabled="status !== 'success'"
				size="small"
				prepend-icon="i-mdi:pencil"
				variant="text"
				@click="emit('update', { id })"
			>
				{{ $t('UserInfoTooltip.edit-user') }}
			</v-btn>
		</template>
	</info-tooltip>
</template>

<script lang="ts" setup>
import InfoTooltip from './InfoTooltip.vue'
import { Users } from '~/types/Users'

type Emit = {
	(e: 'update', value: { id: number }): void
}

type Props = {
	id: number
}

defineOptions({
	extends: InfoTooltip,
})

const p = defineProps<Props>()

const emit = defineEmits<Emit>()

const lazyModel = ref(false)

const { data, execute, status } = useApi<Users._Id.GETResponse>(Users._Id.URL(p.id), {
	immediate: false,
	cache: 'default',
	key: `user-info-tooltip:users/${p.id}`,
	headers: {
		'Cache-Control': 'max-age=3600',
	},
})

const unwatch = watch(() => lazyModel.value, (value) => {
	if (value) {
		execute()
		unwatch()
	}
})
</script>
