<template>
	<v-input
		ref="inputRef"
		:label="$t('SelectPermissions.permissions')"
		class="flex-grow-1"
		:rules="p.rules"
		:error-messages="p.errorMessages"
	>
		<div class="flex-grow-1 ">
			<div class="d-flex justify-space-between">
				<v-label text="Permissions" />
				<div>
					<v-btn
						:disabled="isAllDeselected"
						variant="text"
						size="small"
						@click="deselectAll"
					>
						{{ $t('SelectPermissions.deselect-all') }}
					</v-btn>
					<v-btn
						:disabled="isAllSelected"
						variant="text"
						size="small"
						@click="selectAll"
					>
						{{ $t('SelectPermissions.select-all') }}
					</v-btn>
				</div>
			</div>

			<v-skeleton-loader
				:loading="pending"
				type="list-item-two-line@4"
			>
				<v-list
					v-model:selected="model"
					density="compact"
					select-strategy="classic"
					class="flex-grow-1"
					color="primary"
				>
					<v-list-group
						v-for="group, g in filteredItems"
						:key="`g-${g}`"
					>
						<template #activator="{ props }">
							<v-list-item
								slim
								v-bind="props"
								density="compact"
							>
								<template #prepend>
									<v-list-item-action>
										<v-checkbox
											start
											class="me-2"
											color="primary"
											density="compact"
											direction="vertical"
											:model-value="hasAllValues(group.permissions).value"
											:indeterminate="hasSomeValues(group.permissions).value && !hasAllValues(group.permissions).value"
											hide-details
											@update:model-value="groupCheckboxHandler($event, group.permissions)"
											@click.stop
										/>
									</v-list-item-action>
								</template>
								<v-list-item-title class="text-capitalize d-flex align-center">
									<div class="me-2">
										{{ group.name }}
									</div>
									<v-chip
										v-for="(permission, pIndex) in group.permissions.filter(p => model?.includes(p.id))"
										:key="`p-${pIndex}`"
										class="me-1"
										color="success"
										size="x-small"
									>
										{{ permission.name }}
									</v-chip>
								</v-list-item-title>
							</v-list-item>
						</template>
						<v-list-item
							v-for="(permission, pIndex) in group.permissions"
							:key="`p-${pIndex}`"
							slim
							density="compact"
							:value="permission.id"
						>
							<template #prepend="{ isActive }">
								<v-list-item-action start>
									<v-checkbox-btn
										:model-value="isActive"
										density="compact"
									/>
								</v-list-item-action>
							</template>

							<v-list-item-title class="text-capitalize">
								{{ permission.name }}
							</v-list-item-title>

							<v-list-item-subtitle>
								{{ permission.description }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-list-group>
				</v-list>
			</v-skeleton-loader>
		</div>
	</v-input>
</template>

<script lang="ts" setup>
import type { VInput } from 'vuetify/components'
import { Permissions } from '~/types/Permissions'

const { errorSnackbar } = useSnackbar()
const { data, error, pending } = useApi<Permissions.LookupGrouped.GETResponse>(Permissions.LookupGrouped.URL)
const model = defineModel<Array<number> | null>({ default: () => [], type: Array })
const inputRef = ref<VInput | null>(null)

type Props = {
	errorMessages?: InstanceType<typeof VInput>['$props']['errorMessages']
	rules?: InstanceType<typeof VInput>['$props']['rules']
}
const p = withDefaults(defineProps<Props>(), {
	errorMessages: () => [],
	rules: () => [],
})

if (error.value) {
	errorSnackbar(error.value.message || 'An error occurred while fetching permissions')
}

const searchModel = ref('')

const filteredItems = computed<Permissions.LookupGrouped.GETResponse>(() => {
	const search = searchModel.value.toLowerCase()

	if (!data.value) {
		return []
	}

	if (!search) {
		return data.value
	}
	return (data.value).filter((group) => {
		const permissions = group.permissions.filter((permission) => {
			return permission.name.toLowerCase().includes(search) || (permission.description ? permission.description.toLowerCase().includes(search) : false)
		})
		return permissions.length > 0
	})
})

const hasAllValues = (permissions: any[]) => computed(() => {
	return permissions.every(p => model.value?.includes(p.id))
})

// const hasNoValues = (permissions:any[]) => computed(() => {
// 	return permissions.every(p => !model.value?.includes(p.id))
// })

const hasSomeValues = (permissions: any[]) => computed(() => {
	return permissions.some(p => model.value?.includes(p.id))
})

const selectAll = () => {
	const permissions = data.value?.flatMap(group => group.permissions.map(p => p.id))
	model.value = permissions || null
}

const deselectAll = () => {
	model.value = []
}

const groupCheckboxHandler = (value: boolean | null, permissions: any[]) => {
	if (value) {
		model.value = [...new Set([...model.value || [], ...permissions.map(p => p.id)])]
	} else {
		model.value = model.value?.filter(id => !permissions.map(p => p.id).includes(id)) || null
	}
}

const isAllSelected = computed(() => {
	if (!data.value) {
		return false
	}
	return model.value?.length === data.value.flatMap(group => group.permissions.map(p => p.id)).length
})

const isAllDeselected = computed(() => {
	return model.value?.length === 0
})

defineExpose({
	input: inputRef,
})
</script>
