import { defu } from 'defu'
import type { WritableComputedRef } from 'vue'

type Options = {
	length: number
	uppercase: boolean
	lowercase: boolean
	numbers: boolean
	symbols: boolean
}

const defaults: Options = {
	length: 8,
	uppercase: true,
	lowercase: true,
	numbers: true,
	symbols: true,
}

type Model = Ref<string> | WritableComputedRef<string> | string

export const useGeneratePassword = (model?: Model | Array<Model> | undefined, _options?: Options) => {
	const options = defu(_options, defaults)

	const generate = () => {
		const charset = {
			uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
			lowercase: 'abcdefghijklmnopqrstuvwxyz',
			numbers: '0123456789',
			symbols: '!@#$%^&*()_+',
		}

		let passwordArray = []

		if (options.uppercase) {
			passwordArray.push(charset.uppercase[Math.floor(Math.random() * charset.uppercase.length)])
		}
		if (options.lowercase) {
			passwordArray.push(charset.lowercase[Math.floor(Math.random() * charset.lowercase.length)])
		}
		if (options.numbers) {
			passwordArray.push(charset.numbers[Math.floor(Math.random() * charset.numbers.length)])
		}
		if (options.symbols) {
			passwordArray.push(charset.symbols[Math.floor(Math.random() * charset.symbols.length)])
		}

		while (passwordArray.length < options.length) {
			const activeCharset = Object.keys(charset).filter(key => options[key as keyof Options])
			const randomCharset = charset[activeCharset[Math.floor(Math.random() * activeCharset.length)] as keyof typeof charset]
			passwordArray.push(randomCharset[Math.floor(Math.random() * randomCharset.length)])
		}

		passwordArray = shuffle(passwordArray)

		const password = passwordArray.join('')

		if (Array.isArray(model)) {
			model.forEach((m) => {
				assignValue(m, password)
			})
		} else if (model !== undefined) {
			assignValue(model, password)
		}

		return password
	}

	const assignValue = (model: Model, password: string) => {
		if (typeof model === 'object') {
			model.value = password
		} else {
			model = password
		}
	}

	const shuffle = <T>(array: T[]): T[] => {
		for (let i = array.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[array[i], array[j]] = [array[j], array[i]]
		}
		return array
	}

	return {
		generate,
		model,
	}
}
