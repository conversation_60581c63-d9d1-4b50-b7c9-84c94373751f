<template>
	<v-defaults-provider
		:defaults="{
			global: {
				density: 'compact',
			},
		}"
	>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model="item.name"
					:label="$t('Groups.MT5.Crud.Commission.CommissionForm.name')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<groups-crud-symbol-path-tree
					v-model="item.path"
					:label="$t('Groups.MT5.Crud.Commission.CommissionForm.symbol')"
				/>
			</v-col>
		</v-row>
		<v-text-field
			v-model="item.description"
			:label="$t('Groups.MT5.Crud.Commission.CommissionForm.description')"
		/>
		<v-row>
			<v-col
				cols="12"
				md="4"
			>
				<v-select
					v-model="item.rangeMode"
					:items="rangeModeItems"
					:label="$t('Groups.MT5.Crud.Commission.CommissionForm.range')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
			>
				<v-select
					v-model="item.chargeMode"
					:items="computedChargeModeItems"
					item-props="props"
					:label="$t('Groups.MT5.Crud.Commission.CommissionForm.charge')"
				/>
			</v-col>
			<v-col
				cols="12"
				md="4"
			>
				<v-text-field
					v-model="item.turnoverCurrency"
					:label="$t('Groups.MT5.Crud.Commission.CommissionForm.turnover-currency')"
				/>
			</v-col>
		</v-row>
		<v-radio-group
			v-model="item.mode"
			:items="modeItems"
			inline
			@update:model-value="modeChangeHandler"
		>
			<v-radio
				v-for="mode in modeItems"
				:key="mode.value"
				:label="(mode.title as string)"
				:value="mode.value"
				class="me-4"
			/>
		</v-radio-group>

		<div
			v-if="item.chargeMode === EnCommChargeMode.COMM_CHARGE_INSTANT"
			v-auto-animate
		>
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.entryMode"
						:label="$t('Groups.MT5.Crud.Commission.CommissionForm.entry-mode')"
						:items="entryModeItems"
						:rules="rules({ required: true })"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.actionMode"
						:label="$t('Groups.MT5.Crud.Commission.CommissionForm.action-mode')"
						:items="actionModeItems"
						:rules="rules({ required: true })"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.profitMode"
						:label="$t('Groups.MT5.Crud.Commission.CommissionForm.profit-mode')"
						:items="profitModeItems"
						:rules="rules({ required: true })"
					/>
				</v-col>
			</v-row>
			<v-select
				v-model="reasonModeModel"
				:label="$t('Groups.MT5.Crud.Commission.CommissionForm.reasons')"
				multiple
				chips
				closable-chips
				:items="reasonModeItems"
			/>
		</div>

		<v-data-table
			height="150"
			:headers="headers"
			hover
			:items-per-page="-1"
			:items="item.tiers"
			hide-default-footer
			fixed-header
		>
			<template #item.actions="{ index }">
				<div class="d-flex">
					<v-btn
						variant="text"
						size="x-small"
						icon="i-mdi:trash-can"
						class="me-2"
						@click="remove(index)"
					/>
					<v-btn
						variant="text"
						size="x-small"
						icon="i-mdi:pencil"
						@click="setForEdit(index)"
					/>
				</div>
			</template>
			<template #item.mode="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item: selectedItem }"
					item-key="value"
					:items="commissionModeItems"
				>
					{{ selectedItem.title }}
				</find-item-by-id>
			</template>
			<template #item.type="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item: selectedItem }"
					item-key="value"
					:items="typeItems"
				>
					{{ selectedItem.title }}
				</find-item-by-id>
			</template>
			<template #body.append>
				<td
					:colspan="headers.length"
					class="text-center py-3"
				>
					<v-btn
						prepend-icon="i-mdi:plus"
						variant="text"
						@click="setForEdit(-1)"
					>
						{{ $t('Groups.MT5.Crud.Commission.CommissionForm.add-new-tier') }}
					</v-btn>
				</td>
			</template>
		</v-data-table>
		<v-dialog
			v-model="tierDialogModel"
			max-width="500"
		>
			<v-defaults-provider :defaults="{ global: { density: 'compact' } }">
				<v-confirm-edit
					v-slot="{ actions, model }"
					v-model="currentTier"
					cancel-text="Reset"
					@save="save"
				>
					<v-form ref="tierFormRef">
						<v-card :title="tierDialogTitle">
							<template #text>
								<v-row>
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model.number="model.value.rangeFrom"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.from')"
										/>
									</v-col>
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model.number="model.value.rangeTo"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.to')"
										/>
									</v-col>
								</v-row>
								<v-row class="mt-n5">
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model.number="model.value.value"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.commission')"
										/>
									</v-col>
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model="model.value.currency"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.currency')"
										/>
									</v-col>
								</v-row>
								<v-row class="mt-n5">
									<v-col
										cols="12"
										md="6"
									>
										<v-select
											v-model="model.value.type"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.type')"
											:items="typeItems"
										/>
									</v-col>
									<v-col
										cols="12"
										md="6"
									>
										<v-select
											v-model="model.value.mode"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.mode')"
											:items="commissionModeItems"
										/>
									</v-col>
								</v-row>
								<v-row class="mt-n5">
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model.number="model.value.minimal"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.min')"
										/>
									</v-col>
									<v-col
										cols="12"
										md="6"
									>
										<v-text-field
											v-model.number="model.value.maximal"
											:label="$t('Groups.MT5.Crud.Commission.CommissionForm.max')"
										/>
									</v-col>
								</v-row>
							</template>

							<template #actions>
								<v-btn @click="tierDialogModel = false">
									{{ $t('Groups.MT5.Crud.Commission.CommissionForm.cancel') }}
								</v-btn>
								<v-spacer />
								<component :is="actions" />
							</template>
						</v-card>
					</v-form>
				</v-confirm-edit>
			</v-defaults-provider>
		</v-dialog>
	</v-defaults-provider>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
// import type { ItemErrors } from '~/components/Crud.vue'
import type { Groups } from '~/types'
import { EnCommMode, EnCommRangeMode, EnCommChargeMode, EnCommEntryMode, EnCommActionMode, EnCommProfitMode, EnCommReasonFlags, EnCommissionMode, EnCommissionVolumeType } from '~/types/Groups/MT5/Commission'
import type { CommissionLevel } from '~/types/Groups/MT5/Commission'

const confirm = useNuxtApp().$confirm

const tierFormRef = ref<InstanceType<typeof VForm>>()

const item = defineModel<Groups.MT5.Commission>({
	required: true,
})

type Props = {
	// errors: ItemErrors<Groups.MT5._Id.GETResponse['commissions']>
}

defineProps<Props>()

const { t } = useI18n()

const rangeModeItems = enumToItems(EnCommRangeMode, 'Groups.MT5.EnCommRangeMode')

const chargeModeItems = enumToItems(EnCommChargeMode, 'Groups.MT5.EnCommChargeMode')

const computedChargeModeItems = computed(() => {
	return chargeModeItems.map(modeItem => ({
		...modeItem,
		props: {
			disabled: modeItem.value !== EnCommChargeMode.COMM_CHARGE_INSTANT && item.value.mode === EnCommMode.COMM_FEE,
		},
	}))
})

const modeItems = enumToItems(EnCommMode, 'Groups.MT5.EnCommMode')

const entryModeItems = enumToItems(EnCommEntryMode, 'Groups.MT5.EnCommEntryMode')

const actionModeItems = enumToItems(EnCommActionMode, 'Groups.MT5.EnCommActionMode')

const profitModeItems = enumToItems(EnCommProfitMode, 'Groups.MT5.EnCommProfitMode')

const reasonModeItems = enumToItems(EnCommReasonFlags, 'Groups.MT5.EnCommReasonFlags', item => [-1, EnCommReasonFlags.COMM_REASON_FLAG_NONE].includes(item.value) ? undefined : item)

const computedReasonMode = computed({
	get: () => item.value.reasonMode,
	set: (value: EnCommReasonFlags) => {
		item.value.reasonMode = value
	},
})

const { model: reasonModeModel } = useMultiSelectEnum(computedReasonMode, EnCommReasonFlags, {
	exclude: [EnCommReasonFlags.COMM_REASON_FLAG_NONE],
})

const headers = [
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.from'),
		value: 'rangeFrom',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.to'),
		value: 'rangeTo',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.comm'),
		value: 'value',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.min'),
		value: 'minimal',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.max'),
		value: 'maximal',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.mode'),
		value: 'mode',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.currency'),
		value: 'currency',
	},
	{
		title: t('Groups.MT5.Crud.Commission.CommissionForm.type'),
		value: 'type',
	},
	{
		title: '',
		value: 'actions',
	},
]

const commissionModeItems = enumToItems(EnCommissionMode, 'Groups.MT5.EnCommissionMode')

const typeItems = enumToItems(EnCommissionVolumeType, 'Groups.MT5.EnCommissionVolumeType')

const tierDialogModel = ref(false)

const currentTierIndex = ref(-1)

const currentTier = ref<CommissionLevel>()

const defaultTier: CommissionLevel = {
	rangeFrom: 0,
	rangeTo: 0,
	value: 0,
	minimal: 0,
	maximal: 0,
	mode: EnCommissionMode.COMM_MONEY_DEPOSIT,
	currency: '',
	type: EnCommissionVolumeType.COMM_TYPE_DEAL,
}

const tierDialogTitle = computed(() => currentTierIndex.value === -1 ? t('Groups.MT5.Crud.Commission.CommissionForm.add-tier') : t('Groups.MT5.Crud.Commission.CommissionForm.edit-tier'))

const modeChangeHandler = (value: EnCommMode | null) => {
	if (value === EnCommMode.COMM_FEE) {
		item.value.chargeMode = EnCommChargeMode.COMM_CHARGE_INSTANT
	}
}

const setForEdit = (index: number) => {
	currentTierIndex.value = index
	if (index === -1) {
		currentTier.value = useCloned(defaultTier).cloned.value
	} else {
		currentTier.value = useCloned(item.value.tiers[index]).cloned.value
	}
	tierDialogModel.value = true
}

const save = async (tier: CommissionLevel) => {
	const validation = await tierFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	const tiers = useCloned(item.value.tiers).cloned.value
	if (currentTierIndex.value === -1) {
		tiers.push(tier)
	} else {
		tiers[currentTierIndex.value] = tier
	}

	item.value.tiers = tiers
	tierDialogModel.value = false
}

const remove = (index: number) => {
	confirm(t('Groups.MT5.Crud.Commission.CommissionForm.are-you-sure-you-want-to-remove-tier')).then(() => {
		const tiers = useCloned(item.value.tiers).cloned.value
		tiers.splice(index, 1)
		item.value.tiers = tiers
	}).catch(() => {})
}
</script>
