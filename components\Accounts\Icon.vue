<template>
	<v-icon v-bind="iconProps" />
</template>

<script lang="ts" setup>
import { VIcon } from 'vuetify/components'
import { Accounts } from '~/types'

type MT5Account = Pick<Accounts.MT5._Id.GETResponse, 'group' | 'rights'>
type MT4Account = Pick<Accounts.MT4._Id.GETResponse, 'enable'>

type Props = {
	account: MT5Account
	platform: 'MT5'
} | {
	account: MT4Account
	platform: 'MT4'
}

defineOptions({
	extends: VIcon,
})

const p = defineProps<Props>()

const computedMT5Rights = computed(() => p.platform === 'MT5' ? p.account.rights : 0 as Accounts.MT5.EnUsersRights)

const { model: mt5RightsModel } = useMultiSelectEnum(computedMT5Rights, Accounts.MT5.EnUsersRights)

const iconProps = computed<VIcon['$props']>(() => {
	let icon, color, style
	if (p.platform === 'MT5') {
		if (p.account.group.startsWith('demo')) {
			icon = 'i-mdi:account'
			color = 'success'
		} else if (p.account.group.startsWith('preliminary')) {
			icon = 'i-mdi:account'
			style = 'background: linear-gradient(90deg, rgba(var(--v-theme-account),1) 49.9%, rgba(var(--v-theme-success),1) 50%)'
		} else if (p.account.group.startsWith('manager')) {
			icon = 'i-mdi:account-tie'
			color = 'info'
		} else {
		// real
			icon = 'i-mdi:account'
			color = 'account'
		}

		if (mt5RightsModel.value.includes(Accounts.MT5.EnUsersRights.USER_RIGHT_TECHNICAL)) {
			icon = 'i-mdi:account-wrench'
			color = 'info'
		}

		if (mt5RightsModel.value.includes(Accounts.MT5.EnUsersRights.USER_RIGHT_TRADE_DISABLED)) {
			color = 'grey'
		}
	} else {
		icon = 'i-mdi:account'
		color = p.account.enable ? 'account' : 'grey'
	}

	const { platform, account, ...rest } = p

	return {
		...rest,
		icon,
		color,
		style,
	}
})
</script>
