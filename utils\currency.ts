export default (currency: string | null | undefined, options: Intl.NumberFormatOptions = {}) => {
	if (!currency) {
		currency = undefined
	}

	const { $i18n } = useNuxtApp()

	const ISOLocale = $i18n.localeProperties.value.iso || 'en-US'

	const formatter = new Intl.NumberFormat(ISOLocale, {
		style: currency ? 'currency' : undefined,
		currency,
		// currencyDisplay: 'code',
		...options,
	})

	const parts = formatter.formatToParts(0) // Using 0 as a dummy number
	const symbolPart = parts.find(part => part.type === 'currency')

	return symbolPart?.value
}
