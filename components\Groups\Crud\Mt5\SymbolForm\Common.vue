<template>
	<div>
		<groups-crud-symbol-path-tree
			v-model="item.path"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.path')"
		/>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-checkbox
					v-model="item.permissionsFlags"
					:true-value="EnPermissionsFlags.PERMISSION_BOOK"
					:false-value="EnPermissionsFlags.PERMISSION_NONE"
					:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.enable-market-depth')"
					color="primary"
					hide-details
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.bookDepthLimit"
					:disabled="!item.permissionsFlags"
					:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.market-depth-limit')"
					:items="permissionsBookdepthItems"
				/>
			</v-col>
		</v-row>
		<DefaultsGroup
			v-model="item.spreadDiffDefault"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.use-default-spread')"
			:keys="['spreadDiff', 'spreadDiffBalance']"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="(item.spreadDiff as number)"
						:items="Array.from({ length: 7 }, (_, i) => i - 3)"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.spread-difference')"
						@update:model-value="item.spreadDiffBalance = minBalance"
					/>
				</v-col>
				<v-col
					cols="12"
					md="8"
				>
					<div class="d-flex justify-space-between">
						<v-label>
							{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Common.spread-balance') }}
						</v-label>
						<span
							v-if="typeof item.spreadDiffBalance === 'number' && typeof item.spreadDiff === 'number'"
							class="text-caption"
						>
							{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.Common.bidprice-bid-askprice-ask', [bidPrice, askPrice]) }}
						</span>
					</div>

					<v-slider
						v-model="item.spreadDiffBalance"
						color="transparent"
						thumb-color="primary"
						track-color="primary"
						:error-messages="errors.spreadDiffBalance"
						:min="minBalance"
						:max="maxBalance"
						step="1"
						thumb-label
					/>
				</v-col>
			</v-row>
		</DefaultsGroup>
		<DefaultsGroup
			v-model="useMultipleDefaultModel(item, ['volumeMinDefault', 'volumeMaxDefault', 'volumeStepDefault', 'volumeMinExtDefault', 'volumeMaxExtDefault', 'volumeStepExtDefault']).value"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.use-default-volume')"
			:keys="[
				'volumeMin',
				'volumeMinExt',
				'volumeMax',
				'volumeMaxExt',
				'volumeStep',
				'volumeStepExt']"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<!-- <groups-crud-mt5-symbol-form-components-fraction-ext
						v-model.number="item.volumeMin"
						v-model:ext="item.volumeMinExt"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.min')"
					/> -->
					<symbols-crud-fraction-ext
						v-model.number="item.volumeMin"
						v-model:ext="item.volumeMinExt"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.min')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<symbols-crud-fraction-ext
						v-model.number="item.volumeStep"
						v-model:ext="item.volumeStepExt"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.step')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<symbols-crud-fraction-ext
						v-model.number="item.volumeMax"
						v-model:ext="item.volumeMaxExt"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.max')"
					/>
				</v-col>
			</v-row>
		</DefaultsGroup>
		<DefaultsGroup
			v-model="item.volumeLimitDefault"
			:symbol="item"
			:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.use-default-limit')"
			:keys="[
				'volumeLimit',
				'volumeLimitExt',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<groups-crud-mt5-symbol-form-components-fraction-ext
						v-model="item.volumeLimit"
						v-model:ext="item.volumeLimitExt"
						:component="VCombobox"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Common.limit')"
						:items="[0, 5, 10, 100, 1000]"
					/>
				</v-col>
			</v-row>
		</DefaultsGroup>
	</div>
</template>

<script lang="ts" setup>
import { VCombobox } from 'vuetify/components'
import DefaultsGroup from './Components/DefaultsGroup.vue'
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
// import { Groups } from '~/types'
import { EnPermissionsFlags } from '~/types/Groups/MT5/Symbol'

defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const { t } = useI18n()

const permissionsBookdepthItems = [
	{
		title: t('Groups.MT5.Crud.Symbols.SymbolForm.Common.unlimited'),
		value: 0,
	},
	2,	4, 6, 8, 10, 12, 16, 32,
]
// const computedPermissionsFlags = computed<typeof p.item.permissionsFlags>({
// 	get: () => p.item.permissionsFlags,
// 	set: (value: Groups.MT5.EnPermissionsFlags) => {
// 		emit('update:item', { permissionsFlags: value })
// 	},
// })

// const { model: permissionsFlagModel } = useMultiSelectEnum(computedPermissionsFlags, Groups.MT5.EnPermissionsFlags)

// Compute the Bid and Ask values based on spreadDiff and spreadDiffBalance

const bidPrice = computed(() => {
	if (typeof p.item.spreadDiff !== 'number' || typeof p.item.spreadDiffBalance !== 'number') {
		return 0
	}
	const factor = (maxBalance.value - minBalance.value)
	// return factor - p.item.spreadDiffBalance + minBalance.value
	let result = p.item.spreadDiffBalance - factor - minBalance.value
	if (p.item.spreadDiff > 0) {
		result = result * -1
	}

	if (p.item.spreadDiff === 0) {
		result = 0
	}
	return result
})

const askPrice = computed(() => {
	if (typeof p.item.spreadDiff !== 'number' || typeof p.item.spreadDiffBalance !== 'number') {
		return 0
	}
	const factor = (maxBalance.value - minBalance.value)
	let result = maxBalance.value - p.item.spreadDiffBalance - factor
	if (p.item.spreadDiff > 0) {
		result = result * -1
	}

	if (p.item.spreadDiff === 0) {
		result = 0
	}
	return result
})

const minBalance = computed(() => {
	if (typeof p.item.spreadDiff !== 'number') {
		return 0
	}
	if (p.item.spreadDiff < 0) {
		return Math.ceil(p.item.spreadDiff / 2)
	} else {
		return Math.floor(p.item.spreadDiff / 2) * -1
	}
})

const maxBalance = computed(() => {
	if (typeof p.item.spreadDiff !== 'number') {
		return 0
	}
	return minBalance.value + Math.abs(p.item.spreadDiff)
})
</script>
