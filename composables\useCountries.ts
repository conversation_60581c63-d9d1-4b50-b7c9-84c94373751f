import { getNames, registerLocale, alpha2ToAlpha3, alpha2ToNumeric } from 'i18n-iso-countries'
import en from 'i18n-iso-countries/langs/en.json'
import ar from 'i18n-iso-countries/langs/ar.json'
import { defu } from 'defu'

registerLocale(en)
registerLocale(ar)

interface Options {
	value: 'alpha2' | 'alpha3' | 'numeric' | 'name'
	titleKey: string
	valueKey: string
	locale: 'en' | 'ar' | 'auto'
}

const defaults: Options = {
	value: 'alpha2',
	titleKey: 'title',
	valueKey: 'value',
	locale: 'auto',
}

export const useCountries = (_options?: Partial<Options>) => {
	const options = defu(_options, defaults)

	const { $i18n } = useNuxtApp()

	const locale = options.locale === 'auto' ? $i18n.locale.value : options.locale

	const items = computed(() => {
		const countriesObject = getNames(locale,	{ select: 'official' })
		const result = Object.entries(countriesObject).map(([iso, name]) => {
			let value
			switch (options.value) {
				case 'alpha2':
					value = iso
					break
				case 'alpha3':
					value = alpha2ToAlpha3(iso)
					break

				case 'numeric':
					value = alpha2ToNumeric(iso)
					break

				case 'name':
					value = name
					break
			}

			return {
				[options.titleKey]: name,
				[options.valueKey]: value,
			}
		})

		return result
	})
	return {
		items,
	}
}
