<template>
	<info-tooltip
		v-model="lazyModel"
		open-on-hover
		:disabled="cannot('manager.view')"
	>
		<slot />
		<template #info>
			<v-lazy

				min-width="220"
			>
				<v-skeleton-loader
					:loading="status === 'pending'"
					type="paragraph@1"
				>
					<div v-if="status === 'success'">
						<h4 class="mb-2">
							{{ data?.name }}
						</h4>
						<div class="mb-1">
							<b>{{ $t('Manager.InfoTooltip.mailbox') }}:</b> {{ data?.mailbox || 'N/A' }}
						</div>
						<div class="my-1 d-flex align-center">
							<b class="flex-shrink-0 me-1">{{ $t('Manager.InfoTooltip.groups') }}:</b>
							<v-chip-group column>
								<v-chip
									v-for="group in data?.groups"
									:key="group"
									:text="group"
									size="x-small"
								/>
							</v-chip-group>
						</div>
					</div>
					<div v-else-if="isNotFound">
						{{ $t('Manager.InfoTooltip.there-is-no-manager-with-', [p.login]) }}
					</div>
					<v-list-item
						v-else
						:title="$t('Manager.InfoTooltip.couldnt-load-manager-info')"
						:subtitle="$t('Manager.InfoTooltip.it-could-be-the-manager-deleted')"
					>
						<template #prepend>
							<v-icon
								icon="i-mdi:alert"
								color="warning"
							/>
						</template>
					</v-list-item>
				</v-skeleton-loader>
			</v-lazy>
		</template>

		<template #actions>
			<v-spacer />
			<v-btn
				v-if="can('manager.create') && isNotFound"
				size="small"
				prepend-icon="i-mdi:pencil"
				variant="text"
				@click="emit('create', { login })"
			>
				{{ $t('Manager.InfoTooltip.create') }}
			</v-btn>
			<v-btn
				v-if="can('manager.edit') && !isNotFound"
				:disabled="status !== 'success'"
				size="small"
				prepend-icon="i-mdi:pencil"
				variant="text"
				@click="emit('update', { login })"
			>
				{{ $t('Manager.InfoTooltip.edit') }}
			</v-btn>
		</template>
	</info-tooltip>
</template>

<script lang="ts" setup>
import InfoTooltip from '~/components/InfoTooltip.vue'
import { Manager } from '~/types'

type Emit = {
	(e: 'update' | 'create', value: { login: number }): void
}

type Props = {
	login: number
}

defineOptions({
	extends: InfoTooltip,
})

const p = defineProps<Props>()

const emit = defineEmits<Emit>()

const lazyModel = ref(false)

const { data, execute, status, error } = useApi<Manager.MT5._Id.GETResponse>(Manager.MT5._Id.URL(p.login), {
	immediate: false,
})

const unwatch = watch(() => lazyModel.value, (value) => {
	if (value) {
		execute()
	}
})

watch(() => status.value, (value) => {
	if (value === 'success') {
		unwatch()
	}
})

const isNotFound = computed(() => error.value?.statusCode === 404)
</script>
