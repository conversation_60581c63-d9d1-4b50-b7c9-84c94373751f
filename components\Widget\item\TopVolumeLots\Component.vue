<template>
	<v-card
		ref="card"
		height="100%"
		flat
		border
	>
		<v-card-title class="d-flex align-center">
			<div class="text-subtitle-1 text-wrap">
				Top Volume Lots
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text>
			<PieChart
				:chart-data="chartData"
				:options="options"
				:height="170"
			/>
		</v-card-text>
	</v-card>
</template>

<script lang="ts" setup>
import { PieChart } from 'vue-chart-3'
import type { ChartData, ChartOptions } from 'chart.js'
import type { VCard } from 'vuetify/components'
import type { WidgetItemProps } from '../../types'
import type { SocketData } from '~/services/WebSocket/TopLotVolume'
import { TopLotVolume } from '~/services/WebSocket/TopLotVolume'

type Props = WidgetItemProps & {}

const card = ref<InstanceType<typeof VCard> | null>(null)

const cardWidth = ref(170)

const widthHandler = () => {
	if (card.value) {
		cardWidth.value = card.value.$el.clientWidth
	}
}

useResizeObserver(card, widthHandler)

const theme = useTheme()

const options = computed<ChartOptions<'pie'>>(() => ({
	color: theme.global.current.value.colors['on-background'],
	plugins: {
		legend: {
			position: 'right',
			display: cardWidth.value > 500,
			labels: {
				usePointStyle: true,
				pointStyle: 'rect',
				font: {
					size: 12,
				},
			},
		},
		tooltip: {
			boxPadding: 4,
		},
	},
}))

const chartData = computed<ChartData<'pie'>> (() => ({
	labels: categoriesArray.value,
	datasets: [
		{
			data: categoriesPercentage.value,
			backgroundColor: [
				'#059bff', // Bright Blue
				'#ff5b5b', // Red
				'#ffc859', // Yellow
				'#4cd97b', // Green
				'#9b59b6', // Purple
				'#f39c12', // Orange
				'#1abc9c', // Turquoise
				'#3498db', // Light Blue
				'#c0392b', // Dark Red
				'#e74c3c', // Coral
			],
			borderAlign: 'inner',
			type: 'pie',
			hoverBorderWidth: 2,
			borderWidth: 0,
		},
	],

}))

const categoriesArray = computed(() => Object.keys(data.value))

const categoriesPercentage = computed(() => Object.keys(data.value).map(key => data.value[key].Ratio))

const p = defineProps<Props>()

const topLotVolumeInstance = new TopLotVolume()

const previewData = ref<SocketData>({
	'Cryptocurrencies': {
		HedgingVolume: 0,
		ClientVolume: 2654800,
		Ratio: 0,
	},
	'Energies': {
		HedgingVolume: 0,
		ClientVolume: 1896000,
		Ratio: 0,
	},
	'FX': {
		HedgingVolume: 4000000,
		ClientVolume: -62924600,
		Ratio: -0.06279947499638903,
	},
	'Indices': {
		HedgingVolume: 0,
		ClientVolume: -49864000,
		Ratio: 0,
	},
	'Metals': {
		HedgingVolume: 5000000,
		ClientVolume: -387600,
		Ratio: -0.7152768836816732,
	},
	'US Shares': {
		HedgingVolume: 0,
		ClientVolume: 3134000,
		Ratio: 0,
	},
	'France Shares': {
		HedgingVolume: 0,
		ClientVolume: 10000,
		Ratio: 0,
	},
	'Softs': {
		HedgingVolume: 0,
		ClientVolume: 25000,
		Ratio: 0,
	},
	'ETF\'s': {
		HedgingVolume: 0,
		ClientVolume: 3000,
		Ratio: 0,
	},
})

const realData = ref<SocketData>({} as SocketData)

const data = computed<SocketData>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return realData.value
})

// const myPref = usePreferencesStore()

watch(() => p.isPreview, (isPreview) => {
	if (!isPreview) {
		topLotVolumeInstance.connect().onUpdate((data) => {
			realData.value = data
		})
	}
}, { immediate: true, once: true })
</script>
