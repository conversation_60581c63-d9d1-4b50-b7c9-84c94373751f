<template>
	<Title>{{ $t('login.login') }}</Title>
	<div class="text-center text-h6 pb-4">
		{{ $t('login.login') }}
	</div>
	<v-window v-model="windowModel">
		<v-window-item value="new">
			<v-form
				ref="formRef"
				validate-on="lazy"
				class="h-100 d-flex flex-column"
				@submit.prevent="validateThenLogin"
			>
				<v-text-field
					v-model.trim="form.email"
					:rules="rules({ type: 'email', required: true })"
					:error-messages="errors.email"
					autocomplete="email"
					:placeholder="$t('login.enter-your-email-address')"
					:label="$t('login.email')"
				/>

				<password
					v-model.trim="form.password"
					autocomplete="password"
					hide-details="auto"
					:rules="rules({ required: true })"
					:error-messages="errors.password"
					:label="$t('login.password')"
					:placeholder="$t('login.enter-your-password')"
				/>
				<div
					v-if="authStore.LoginMode === Auth.LoginMode.Modes.Database"
					class="d-flex justify-end mt-1 "
				>
					<v-btn
						:to="{ path: '/auth/forgot' }"
						variant="text"
						size="small"
						class="text-capitalize"
					>
						{{ $t('login.forgot-your-password') }}
					</v-btn>
				</div>
				<v-spacer />
				<v-btn
					class="mt-7 mb-4"
					color="primary"
					size="large"
					type="submit"
					variant="elevated"
					block
					:disabled="loading"
					:loading="loading"
				>
					{{ $t('login.login') }}
				</v-btn>
				<v-btn
					variant="text"
					block
					:disabled="!hasLoginHistory"
					prepend-icon="i-mdi:account-clock"
					@click="switchWindowModel('history')"
				>
					{{ $t('login.login-with-saved-account') }}
				</v-btn>
			</v-form>
		</v-window-item>
		<v-window-item
			v-click-outside="{
				handler: () => !loading && setActiveHistoryItem(null),
				include,
			}"
			value="history"
		>
			<v-list
				bg-color="transparent"
				max-height="250"
				class="overscroll-y-auto"
			>
				<v-list-item
					v-for="item, i in loginHistorySorted"
					:key="i"
					:ripple="false"
					lines="two"
					:active="activeHistoryItem === item.email"
					class="history-item mb-1"
					border
					rounded
					@click.stop="setActiveHistoryItem(item.email)"
					@keydown.esc="setActiveHistoryItem(null)"
				>
					<div v-auto-animate>
						<div class="d-flex justify-space-between align-start">
							<div>
								<v-list-item-title>
									{{ item.name }}
								</v-list-item-title>
								<v-list-item-subtitle class="mb-2">
									{{ item.email }}
								</v-list-item-subtitle>
							</div>
							<v-btn
								size="small"
								variant="text"
								icon
							>
								<v-menu activator="parent">
									<v-list
										slim
										density="compact"
									>
										<v-list-item
											prepend-icon="i-mdi:trash-can-outline"
											:title="$t('login.delete-item')"
											@click="deleteItem(item.email)"
										/>
									</v-list>
								</v-menu>
								<v-icon icon="i-mdi:dots-vertical" />
							</v-btn>
						</div>
						<v-form
							v-if="activeHistoryItem === item.email"
							class="d-flex"
							@keydown.enter="login"
							@submit="login"
						>
							<!-- This hidden input is to provide the correct password suggestion to browsers -->
							<input
								type="email"
								name="email"
								:value="item.email"
								class="d-none"
							>
							<password
								:id="`${item.email}-password`"
								v-model="form.password"
								autocomplete="current-password"
								single-line
								autofocus
								density="comfortable"
								hide-details
								:error-messages="errors.password"
								:label="$t('login.password')"
								:placeholder="$t('login.enter-your-password')"
							/>

							<v-btn
								:disabled="!form.password"
								:loading="loading"
								variant="text"
								icon="i-mdi:chevron-right"
								type="submit"
								@click.prevent="login"
							/>
						</v-form>
					</div>
				</v-list-item>
			</v-list>
			<v-btn
				variant="text"
				block
				prepend-icon="i-mdi:account-plus"
				@click="switchWindowModel('new')"
			>
				{{ $t('login.login-with-a-different-acccount') }}
			</v-btn>
		</v-window-item>
	</v-window>
</template>

<script lang="ts" setup>
import { VForm, VTextField } from 'vuetify/components'
import type { LoginHistoryItem } from '../auth.vue'
import { Auth } from '~/types'

definePageMeta({
	middleware: 'guest',
})

const authStore = useAuthStore()

authStore.verifyLoginMode()

const { t } = useI18n()

const formRef = ref<VForm | null>(null)

const loginHistory = useLocalStorage<LoginHistoryItem[]>('login-history', [], {})

const loginHistorySorted = computed(() => loginHistory.value.slice().sort((a, b) => Number(a.lastLogin) - Number(b.lastLogin)))

const hasLoginHistory = computed(() => loginHistory.value.length > 0)

const windowModel = ref<'history' | 'new'>(hasLoginHistory.value ? 'history' : 'new')

const activeHistoryItem = ref<string | null>(null)

watch(() => loginHistory.value, (value) => {
	if (value.length === 0) {
		windowModel.value = 'new'
	}
})

const form = reactive({
	email: '',
	password: '',
})
const { errors, handler } = useFormHandler(form)
const loading = ref(false)

const router = useRouter()

const hash = router.currentRoute.value.hash

const validateThenLogin = async () => {
	const validation = await formRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	login()
}

const login = async () => {
	loading.value = true
	await authStore.login(toRaw(form))
		.then(async (resp) => {
			if (!resp.twoFactorEnabled) {
				await router.push({ name: 'auth-logging', hash })
				loading.value = false
			} else {
				router.push({ name: 'auth-2fa', hash })
				loading.value = false
			}
		})
		.catch((error) => {
			handler(error)
			loading.value = false
		})
}

const setActiveHistoryItem = (email: string | null) => {
	if (activeHistoryItem.value !== email) {
		activeHistoryItem.value = email

		nextTick(() => {
			if (activeHistoryItem.value) {
				form.email = email || ''
				form.password = ''
			}
			formRef.value?.resetValidation()
		})
	} else {
		// same list item clicked again
		document.getElementById(`${email}-password`)?.focus()
	}
}

const deleteItem = (email: string) => {
	// confirm dialog
	const { $confirm } = useNuxtApp()
	$confirm(t('login.are-you-sure-you-want-to-delete-this-history'))
		.then(() => {
			loginHistory.value = loginHistory.value.filter(item => item.email !== email)
		}).catch(() => {})
}

const switchWindowModel = (model: 'history' | 'new') => {
	windowModel.value = model

	if (model === 'new') {
		form.email = ''
	}

	form.password = ''

	formRef.value?.resetValidation()
	nextTick(() => {
		formRef.value?.resetValidation()
	})

	activeHistoryItem.value = null
}

function include() {
	return [document.querySelector('.history-item')]
}
</script>
