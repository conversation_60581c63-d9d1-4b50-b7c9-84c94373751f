<template>
	<v-chip
		rounded
		:color="color"
		variant="tonal"
	>
		{{ text }}
	</v-chip>
</template>

<script lang="ts" setup>
import { Transactions } from '~/types/Transactions'

const model = defineModel<Transactions.Status>()

const { t } = useI18n()

const text = computed(() => {
	switch (model.value) {
		case Transactions.Status.Pending:
			return t('TransactionsStatusLabel.pending')
		case Transactions.Status.Approved:
			return t('TransactionsStatusLabel.approved')
		case Transactions.Status.Rejected:
			return t('TransactionsStatusLabel.rejected')
		default:
			return t('TransactionsStatusLabel.unknown')
	}
})

const color = computed(() => {
	switch (model.value) {
		case Transactions.Status.Pending:
			return 'warning'
		case Transactions.Status.Approved:
			return 'success'
		case Transactions.Status.Rejected:
			return 'error'
		default:
			return 'grey'
	}
})
</script>
