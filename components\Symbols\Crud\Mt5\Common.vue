<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.trim="item.symbol"
					:error-messages="errors.symbol"
					:rules="rules({ required: true, whitespace: true })"
					:label="$t('Symbols.MT5.Crud.Common.symbol')"
					persistent-hint
					:hint="!!loadedSymbol && loadedSymbol !== item.symbol ? $t('Symbols.MT5.Crud.Common.loaded-symbol', [loadedSymbol]) : undefined"
					append-inner-icon="i-mdi:caps-lock"
					@click:append-inner="item.symbol = item.symbol.toUpperCase()"
				/>
				<v-text-field
					v-model="item.exchange"
					:error-messages="errors.exchange"
					:label="$t('Symbols.MT5.Crud.Common.exchange')"
				/>
				<v-text-field
					v-model="item.isin"
					:error-messages="errors.isin"
					label="ISIN"
				/>
				<v-text-field
					v-model="item.cfi"
					:error-messages="errors.cfi"
					label="CFI"
				/>

				<symbols-crud-symbols-tree
					v-model="item.basis"
					:error-messages="errors.basis"
					:label="$t('Symbols.MT5.Crud.Common.basis')"
				/>

				<symbols-crud-symbols-tree
					v-model="item.source"
					:error-messages="errors.source"
					:label="$t('Symbols.MT5.Crud.Common.source')"
				/>

				<background-color
					v-model:background="item.colorBackground"
					v-model:color="item.color"
					:error-messages="errors.symbol"
					:label="$t('Symbols.MT5.Crud.Common.background')"
				/>
				<v-autocomplete
					v-model.number="item.digits"
					v-mask="'#'"
					:items="Array.from({ length: 8 }, (_, i) => i + 1)"
					:error-messages="errors.digits"
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.Common.digits')"
				/>
				<v-text-field
					v-model="item.spread"
					v-mask="Mask.Integer"
					:error-messages="errors.spread"
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.Common.spread')"
					suffix="pt"
				/>
				<div class="d-flex justify-space-between">
					<v-label>
						{{ $t('Symbols.MT5.Crud.Common.spread-balance') }}
					</v-label>
					<span class="text-caption">
						{{ $t('Symbols.MT5.Crud.Common.item-spreadbalance-spread', [item.spreadBalance - spreadBalanceBid, item.spreadBalance + spreadBalanceAsk]) }}	</span>
				</div>
				<v-slider
					v-model="item.spreadBalance"
					color="transparent"
					thumb-color="primary"
					track-color="primary"
					:error-messages="errors.spreadBalance"
					:min="-150"
					:max="150"
					step="1"
					thumb-label
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<symbols-crud-path-tree
					v-model="item.path"
					:symbol="item.symbol"
					:label="$t('Symbols.MT5.Crud.Common.path')"
				/>
				<v-text-field
					v-model="item.description"
					:error-messages="errors.description"
					:label="$t('Symbols.MT5.Crud.Common.description')"
				/>
				<v-text-field
					v-model="item.international"
					:error-messages="errors.symbol"
					:label="$t('Symbols.MT5.Crud.Common.international')"
				/>
				<v-autocomplete
					v-model="item.sector"
					:error-messages="errors.sector"
					auto-select-first
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.Common.sector')"
					:items="sectorItems"
					@update:model-value="item.industry = Symbols.MT5.Industries.UNDEFINED"
				/>
				<v-autocomplete
					v-model="item.industry"
					:disabled="!item.sector || [Symbols.MT5.Sectors.SECTOR_INDEXES, Symbols.MT5.Sectors.SECTOR_CURRENCY, Symbols.MT5.Sectors.SECTOR_CURRENCY_CRYPTO].includes(item.sector)"
					auto-select-first
					:error-messages="errors.industry"
					:items="filteredIndustryItems"
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.Common.industry')"
				/>
				<v-autocomplete
					v-model="item.country"
					auto-select-first
					:items="countriesItems"
					:error-messages="errors.country"
					:label="$t('Symbols.MT5.Crud.Common.country')"
				/>
				<v-text-field
					v-model="item.category"
					:error-messages="errors.category"
					:label="$t('Symbols.MT5.Crud.Common.category')"
				/>
				<v-text-field
					v-model="item.page"
					:error-messages="errors.page"
					:rules="rules({ type: 'url' })"
					:label="$t('Symbols.MT5.Crud.Common.page')"
				/>
				<v-select
					v-model="item.tickBookDepth"
					:error-messages="errors.tickBookDepth"
					:items="marketDepthItems"
					:label="$t('Symbols.MT5.Crud.Common.market-depth')"
				/>

				<v-select
					v-model="item.tickChartMode"
					:error-messages="errors.tickChartMode"
					:items="chartModeItems"
					:label="$t('Symbols.MT5.Crud.Common.chart-mode')"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
import { Mask } from '~/types/Mask'
import { Symbols } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const { t } = useI18n()

const sectorItems = enumToItems(Symbols.MT5.Sectors, 'Symbols.MT5.Sectors')

const chartModeItems = enumToItems(Symbols.MT5.ChartMode, 'Symbols.MT5.ChartMode')

const industryItems = enumToItems(Symbols.MT5.Industries, 'Symbols.MT5.Industries')

const filteredIndustryItems = computed(() => industryItems.filter((i) => {
	const sectorIndex = p.item.sector === Symbols.MT5.Sectors.SECTOR_COMMODITIES ? 12 : p.item.sector
	const max = sectorIndex * 50
	const min = max - 50
	return (i.value > min && i.value <= max) || i.value === 0
}))

const { items: countriesItems } = useCountries({ value: 'alpha3' })

const marketDepthItems = [
	{ title: t('Symbols.MT5.Crud.Common.off'), value: 0 },
	{ title: '10', value: 10 },
	{ title: '16', value: 16 },
	{ title: '32', value: 32 },
]

const spreadBalanceBid = computed(() => Math.floor((p.item.spread || 0) / 2))

const spreadBalanceAsk = computed(() => p.item.spread - spreadBalanceBid.value)
</script>
