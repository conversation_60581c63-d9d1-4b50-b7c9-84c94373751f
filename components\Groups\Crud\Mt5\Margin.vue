<template>
	<v-container>
		<v-select
			v-model="item.marginMode"
			:items="marginModeItems"
			:label="$t('Groups.MT5.Crud.Margin.risk-management')"
		/>
		<template v-if="item.marginMode !== Groups.MT5.EnMarginMode.MARGIN_MODE_EXCHANGE_DISCOUNT">
			<v-row>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.marginCall"
						type="number"
						:label="$t('Groups.MT5.Crud.Margin.margin-call-level')"
						:error-messages="errors.marginCall"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model="item.marginStopOut"
						type="number"
						:label="$t('Groups.MT5.Crud.Margin.stop-out-level')"
						:error-messages="errors.marginStopOut"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-select
						v-model="item.marginSOMode"
						:label="$t('Groups.MT5.Crud.Margin.in')"
						:error-messages="errors.marginStopOut"
						:items="marginSOModeItems"
					/>
				</v-col>
			</v-row>
			<v-checkbox
				v-model="tradeFlagsModel"
				color="primary"
				hide-details
				:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_SO_FULLY_HEDGED"
				:label="$t('Groups.MT5.Crud.Margin.stop-out-fully-hedged-acc')"
				class="compact"
			/>
			<v-checkbox
				v-model="tradeFlagsModel"
				color="primary"
				hide-details
				:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_SO_COMPENSATION"
				:label="$t('Groups.MT5.Crud.Margin.compensate-negative-balan')"
				class="compact"
			/>
			<v-checkbox
				v-model="tradeFlagsModel"
				:disabled="!tradeFlagsModel.includes(Groups.MT5.EnTradeFlags.TRADEFLAGS_SO_COMPENSATION)"
				color="primary"

				:true-value="Groups.MT5.EnTradeFlags.TRADEFLAGS_SO_COMPENSATION_CREDIT"
				:label="$t('Groups.MT5.Crud.Margin.withdraw-credit-after-neg')"
				class="compact"
			/>
			<api-items
				v-slot="props"
				:url="Leverages.Lookup.URL"
			>
				<v-select
					v-model="item.marginFloatingLeverage "
					:label="$t('Groups.MT5.Crud.Margin.floating-leverage-profile')"
					v-bind="props"
					item-title="name"
					item-value="name"
				/>
			</api-items>
			<fieldset class="mb-4">
				<legend>
					{{ $t('Groups.MT5.Crud.Margin.profit-loss-in-free-margin') }}
				</legend>
				<v-select
					v-model="item.marginFreeMode"
					:items="marginFreeModeItems"
					:error-messages="errors.marginFreeMode"
					:label="$t('Groups.MT5.Crud.Margin.unrealized-profit')"
				/>
				<v-select
					v-model="item.marginFreeProfitMode"
					:error-messages="errors.marginFreeProfitMode"
					:items="marginFreeProfitModeItems"
					:label="$t('Groups.MT5.Crud.Margin.daily-fixed-profit')"
				/>
				<v-checkbox
					v-model="item.marginFlags"
					:true-value="Groups.MT5.EnMarginFlags.MARGIN_FLAGS_CLEAR_ACC"
					:false-value="Groups.MT5.EnMarginFlags.MARGIN_FLAGS_NONE"
					:label="$t('Groups.MT5.Crud.Margin.release-fixed-profit-at-t')"
					class="compact"
					hide-details
					color="primary"
				/>
			</fieldset>
			<v-row>
				<v-col>
					<v-text-field
						v-model="item.tradeVirtualCredit"
						type="number"
						hide-spin-buttons
						:error-messages="errors.tradeVirtualCredit"
						:label="$t('Groups.MT5.Crud.Margin.virtual-credit')"
						hint="Applies Only To Opening New Positions"
					/>
				</v-col>
			</v-row>
		</template>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups, Leverages } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const marginModeItems = enumToItems(Groups.MT5.EnMarginMode, 'Groups.MT5.EnMarginMode')

const marginSOModeItems = enumToItems(Groups.MT5.EnStopOutMode, 'Groups.MT5.EnStopOutMode')

const computedTradeFlags = computed({
	get: () => p.item.tradeFlags,
	set: (value: number) => emit('update:item', { tradeFlags: value }),
})

const { model: tradeFlagsModel } = useMultiSelectEnum(computedTradeFlags, Groups.MT5.EnTradeFlags, {
	exclude: [Groups.MT5.EnTradeFlags.TRADEFLAGS_NONE],
})

const marginFreeModeItems = enumToItems(Groups.MT5.EnFreeMarginMode, 'Groups.MT5.EnFreeMarginMode')

const marginFreeProfitModeItems = enumToItems(Groups.MT5.EnMarginFreeProfitFlags, 'Groups.MT5.EnMarginFreeProfitFlags')
</script>
