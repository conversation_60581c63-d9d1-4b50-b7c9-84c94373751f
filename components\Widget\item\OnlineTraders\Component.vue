<template>
	<v-card
		height="100%"
		flat
		border
		:to="{ name: 'reports-online-traders' }"
	>
		<template #item>
			<div class=" text-caption text-medium-emphasis">
				{{ $t('widget-online-traders.online-traders') }}
			</div>
			<v-card-title class="v-card-title pt-0 mt-n1 d-flex align-center">
				<div class="me-2">
					<v-skeleton-loader
						type="list-item"
						:loading="isLoading"
						min-width="100"
						class="d-inline-block"
					>
						{{ $t('widget-online-traders.data-total-sessions', [data.total]) }}
					</v-skeleton-loader>
				</div>
			</v-card-title>
		</template>
		<template #append>
			<v-icon
				size="48"
				:color="`rgba(var(--v-theme-${iconColor}),0.3)`"
				icon="i-mdi:account-online-outline"
			/>
		</template>
		<!-- <template #actions>
			<v-spacer />
			<nuxt-link-locale class="text-caption text-decoration-none text-medium-emphasis" :to="{name: 'reports-online-traders'}">
				View Sessions
			</nuxt-link-locale>
		</template> -->
		<!-- <v-sparkline
			:model-value="data.history"
			color="rgb(var(--v-theme-success))"
			:gradient="['rgb(var(--v-theme-success))','rgb(var(--v-theme-surface))']"
			fill
			padding="0"
			stroke-linecap="round"
			smooth
		/> -->
	</v-card>
</template>

<script lang="ts" setup>
import type { WidgetItemProps } from '../../types'

type Props = WidgetItemProps & {}

type ModifiedData = {
	total: number | null
}

const p = defineProps<Props>()

const isConnected = computed(() => onlineSessionsStore.connectionStatus === ConnectionStatus.Connected)

const isLoading = ref(false)

const previewData = ref<ModifiedData>({
	total: 117,
})

const realData = computed(() => ({ total: onlineSessionsStore.data.totalSessions }))

const data = computed<ModifiedData>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return realData.value
})

const iconColor = computed(() => {
	if (p.isPreview) {
		return 'success'
	}
	if (isConnected.value) {
		return 'success'
	} else {
		return 'warning'
	}
})

const onlineSessionsStore = useOnlineSessionsStore()

const myPref = usePreferencesStore()

watch(() => p.isPreview, (value) => {
	if (!value && onlineSessionsStore.connectionStatus === ConnectionStatus.Disconnected) {
		onlineSessionsStore.connect().to(myPref.getKey('tradingServerName') as string)
	}
}, { immediate: true, once: true })

const tradingServerName = computed(() => myPref.keys.tradingServerName)

watch(() => tradingServerName.value, (value) => {
	if (!p.isPreview && value) {
		onlineSessionsStore.connect().to(value)
	}
}, {
	immediate: true,
})
</script>
