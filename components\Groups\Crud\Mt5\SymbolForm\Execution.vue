<template>
	<v-container fluid>
		<v-row>
			<v-col
				cols="12"
				md="8"
			>
				<DefaultsGroup
					v-model="useMultipleDefaultModel(item, ['execModeDefault',
						'reTimeoutDefault', 'ieTimeoutDefault', 'ieSlipLosingDefault',
						'ieSlipProfitDefault', 'ieFlagsDefault', 'ieVolumeMaxDefault', 'ieVolumeMaxExtDefault', 'reFlagsDefault']).value"
					:symbol="item"
					:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.use-default-exec-setting')"
					:keys="[
						'execMode',
						'reTimeout',
						'reFlags',
						'ieTimeout',
						'ieSlipProfit',
						'ieSlipLosing',
						'ieFlags',
						'ieVolumeMax',
						'ieVolumeMaxExt',
					]"
					@update:symbol="$emit('update:item', $event)"
				>
					<v-select
						v-model="item.execMode"
						:items="execModeItems"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.trade')"
						:rules="rules({ required: true })"
					/>
					<template v-if="item.execMode === Symbols.MT5.ExecutionMode.EXECUTION_REQUEST">
						<v-text-field
							v-model.number="item.reTimeout"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.timeout')"
							suffix="Seconds For Confirmation"
							type="number"
							:rules="rules({ required: true, min: 3, max: 30, type: 'number' })"
						/>
						<!-- TODO: UNKNOWN -->
						<v-checkbox
							v-model="item.reFlags"
							color="primary"
							hide-details
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.confirm-orders')"
							:true-value="EnREFlags.RE_FLAGS_ORDER"
							:false-value="EnREFlags.RE_FLAGS_NONE"
						/>
					</template>

					<template v-if="Symbols.MT5.ExecutionMode.EXECUTION_INSTANT === item.execMode">
						<v-text-field
							v-model.number="item.ieTimeout"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.max-time-deviation')"
							suffix="seconds"
							:rules="rules({ required: true, min: 3, max: 30, type: 'number' })"
						/>
						<v-text-field
							v-model.number="item.ieSlipProfit"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.max-profit-deviation')"
							suffix="points"
						/>
						<v-text-field
							v-model.number="item.ieSlipLosing"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.max-losing-deviation')"
							suffix="points"
						/>
						<v-switch
							v-model="item.ieFlags"
							color="success"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.fast-confirmation-of-request')"
							:true-value="Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_FAST_CONFIRMATION"
							:false-value="Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_NONE"
						/>
						<groups-crud-mt5-symbol-form-components-fraction-ext
							v-model="item.ieVolumeMax"
							v-model:ext="item.ieVolumeMaxExt"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.max-volume')"
						/>
						<v-text-field
							v-if="item.ieVolumeMax"
							v-model.number="item.reTimeout"
							:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.Execution.timeout')"
							suffix="Seconds For Confirmation"
							type="number"
							:rules="rules({ required: true, min: 3, max: 30, type: 'number' })"
						/>
					</template>
				</DefaultsGroup>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import DefaultsGroup from './Components/DefaultsGroup.vue'
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import { EnREFlags } from '~/types/Groups/MT5/Symbol'
import { Symbols } from '~/types'

defineEmits<Emit>()

withDefaults(defineProps<Props>(), defaults)

const execModeItems = enumToItems(Symbols.MT5.ExecutionMode, 'Symbols.MT5.ExecutionMode')
</script>
