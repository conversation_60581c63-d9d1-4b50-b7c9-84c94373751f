<template>
	<v-card
		ref="card"
		height="100%"
		flat
		border
	>
		<v-card-title class="d-flex align-center">
			<div class="text-subtitle-1 text-wrap">
				Top Net Volume
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text>
			<PieChart
				:chart-data="testData"
				:options="options"
				:height="170"
			/>
		</v-card-text>
	</v-card>
</template>

<script lang="ts" setup>
import { PieChart } from 'vue-chart-3'
import type { ChartData, ChartOptions } from 'chart.js'
import type { VCard } from 'vuetify/components'
import type { WidgetItemProps } from '../../types'

type Props = WidgetItemProps & {}

type Data = {
	total: number | null
}

const card = ref<InstanceType<typeof VCard> | null>(null)

const cardWidth = ref(170)

const widthHandler = () => {
	if (card.value) {
		cardWidth.value = card.value.$el.clientWidth
	}
}

useResizeObserver(card, widthHandler)

const theme = useTheme()

const options = computed<ChartOptions<'pie'>>(() => ({
	color: theme.global.current.value.colors['on-background'],
	plugins: {
		legend: {
			position: 'right',
			display: cardWidth.value > 500,
			labels: {
				usePointStyle: true,
				pointStyle: 'rect',
				font: {
					size: 12,
				},
			},
		},
		tooltip: {
			boxPadding: 4,
		},
	},
}))

const testData = ref<ChartData<'pie'>> ({
	labels: ['Paris the capital', 'Nîmes', 'Toulon', 'Perpignan', 'Autre', 'Nîmes', 'Toulon', 'Perpignan test test', 'Autre', 'Nîmes', 'Toulon', 'Perpignan', 'Autre', 'Nîmes'],
	datasets: [
		{
			data: [30, 40, 60, 70, 5],
			backgroundColor: ['#059bff', '#ff5b5b', '#ffc859', '#4cd97b', '#ff5b5b'],
			borderAlign: 'inner',
			type: 'pie',
			hoverBorderWidth: 2,
			borderWidth: 0,
		},
	],

})
const p = defineProps<Props>()

const _isLoading = ref(false)

const previewData = ref<Data>({
	total: 117,
})

const realData = computed(() => ({ total: onlineSessionsStore.data.totalSessions }))

const _data = computed<Data>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return realData.value
})

const onlineSessionsStore = useOnlineSessionsStore()

const myPref = usePreferencesStore()

watch(() => p.isPreview, (value) => {
	if (!value && onlineSessionsStore.connectionStatus === ConnectionStatus.Disconnected) {
		onlineSessionsStore.connect().to(myPref.getKey('tradingServerName') as string)
	}
}, { immediate: true, once: true })

const tradingServerName = computed(() => myPref.keys.tradingServerName)

watch(() => tradingServerName.value, (value) => {
	if (!p.isPreview && value) {
		onlineSessionsStore.connect().to(value)
	}
}, {
	immediate: true,
})
</script>
