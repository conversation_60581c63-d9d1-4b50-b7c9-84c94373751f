<template>
	<v-card class="py-8">
		<div class="d-flex justify-center align-center">
			<v-card
				flat
				max-width="600"
				class="flex-grow-1"
			>
				<v-card-title>
					{{ $t('security.2-factors-authentication') }}
				</v-card-title>
				<v-window v-model="windowModel">
					<v-window-item value="disabled">
						<v-card-subtitle>
							{{ $t('security.you-will-need-to-install') }} <a
								href="https://www.microsoft.com/en-us/security/mobile-authenticator-app#overview"
								target="_blank"
							>{{ $t('security.microsoft-authentication-app') }}</a> {{ $t('security.on-your-phone-to-use-2fa') }} <br>
							{{ $t('security.scan-the-qr-code-below-then-enter-the-code-generat') }}
						</v-card-subtitle>
						<v-card-text class="text-center">
							<div class="d-flex justify-center">
								<v-skeleton-loader
									type="image"
									style="width:150px;height: 150px"
									:loading="pending"
								>
									<v-img
										aspect-ratio="1"
										max-width="150"
										:src="`data:image/jpg;base64,${data?.qrCodeImage}`"
									/>
								</v-skeleton-loader>
							</div>
							<div class="py-4 text-uppercase">
								<b>{{ $t('security.or') }}</b>
							</div>
							<v-alert
								color="info"
								variant="tonal"
								density="compact"
								class="code-content"
							>
								<v-skeleton-loader
									color="transparent"
									type="text"
									class="justify-center"
									:loading="pending"
								>
									<span v-html="readableCode" /> <v-btn
										v-if="isCopySupported"
										color="info"
										variant="text"
										size="x-small"
										class="ms-2"
										icon="i-mdi:content-copy"
										@click="copyHandler"
									/>
								</v-skeleton-loader>
							</v-alert>
							<v-lazy>
								<v-otp-input
									ref="disabledOtpInputRef"
									v-model="form.code"
									dir="ltr"
									autofocus
									:error-messages="errors.code"
									length="6"
									@vue:mounted="inputFocusHandler(disabledOtpInputRef?.$el)"
									@finish="verifyHandler"
								/>
							</v-lazy>
						</v-card-text>

						<v-card-actions>
							<v-btn :to="{ name: 'index' }">
								{{ $t('security.cancel') }}
							</v-btn>
							<v-spacer />
							<v-btn
								:disabled="String(form.code).length !== 6"
								color="primary"
								:loading="isVerifying"
								@click="verifyHandler"
							>
								{{ $t('security.continue-setup') }}
							</v-btn>
						</v-card-actions>
					</v-window-item>
					<v-window-item value="enabled">
						<v-banner
							color="success"
							icon="i-mdi:security-account-outline"
							border="0"
						>
							<template #text>
								<div class="text-h6">
									{{ $t('security.your-account-is-secured') }}
								</div>
								<p>
									{{ $t('security.two-factor-authentication-2fa-is-currently-enabled') }}
								</p>
							</template>
							<template #actions>
								<v-btn
									color=""
									:to="{ name: 'index' }"
								>
									Return Home
								</v-btn>
								<v-btn
									color="error"
									:loading="isVerifying"
									@click="disable2FADialogModel = true"
								>
									{{ $t('security.disable-2fa') }}
								</v-btn>
							</template>
						</v-banner>
					</v-window-item>
				</v-window>
			</v-card>
		</div>
		<v-dialog
			v-model="disable2FADialogModel"
			max-width="400"
		>
			<v-card title="Disable 2FA">
				<v-card-text>
					<p>
						{{ $t('security.to-disable-2fa-enter-the-code-from-your-authentica') }}
					</p>
					<v-otp-input
						ref="enabledOtpInputRef"
						v-model="form.code"
						dir="ltr"
						autofocus
						:error-messages="errors.code"
						length="6"
						@vue:mounted="inputFocusHandler(enabledOtpInputRef?.$el)"
						@finish="disable2FAHandler"
					/>
				</v-card-text>
				<v-card-actions>
					<v-btn @click="disable2FADialogModel = false">
						{{ $t('security.cancel-1') }}
					</v-btn>
					<v-spacer />
					<v-btn
						color="error"
						:disabled="String(form.code).length !== 6"
						:loading="isVerifying"
						@click="disable2FAHandler"
					>
						{{ $t('security.disable-2fa-0') }}
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</v-card>
</template>

<script lang="ts" setup>
import { VOtpInput } from 'vuetify/components'

const { t } = useI18n()

const { generate2FA, verify2FA, $state } = useAuthStore()

const { errorSnackbar, infoSnackbar, successSnackbar } = useSnackbar()

const { copy, isSupported: isCopySupported } = useClipboard()

const disabledOtpInputRef = ref<VOtpInput>()
const enabledOtpInputRef = ref<VOtpInput>()

type Form = {
	code: string | undefined
}

const form = reactive<Form>({
	code: undefined,
})

const isVerifying = ref(false)

const windowModel = ref<'disabled' | 'enabled'>($state?.twoFactorEnabled ? 'enabled' : 'disabled')

const disable2FADialogModel = ref(false)

const { $api } = useNuxtApp()

const { errors, handler } = useFormHandler(form)

const authStore = useAuthStore()

const readableCode = computed(() => {
	// wrap each 4 chars with span that has a class mx-2
	return (data.value?.code || '').match(/.{1,4}/g)?.map(code => `<span class="mx-1">${code}</span>`).join('') || ''
})

const copyHandler = () => {
	if (!data.value?.code) {
		return
	}

	copy(data.value?.code)
	infoSnackbar(t('security.code-has-been-copied'))
}

const verifyHandler = () => {
	isVerifying.value = true
	verify2FA(form.code as string, 'app')
		.then(() => {
			successSnackbar(t('security.2fa-has-been-enabled'))
			windowModel.value = 'enabled'
			form.code = undefined
		})
		.catch(handler)
		.finally(() => {
			isVerifying.value = false
		})
}

const disable2FAHandler = () => {
	isVerifying.value = true
	$api('/my/update-two-factor', {
		method: 'PATCH',
		body: {
			twoFactorEnabled: false,
			code: form.code,
		},
	})
		.then(() => {
			authStore.refresh()
			authStore.set2FAEnabledStatus(false)
			successSnackbar(t('security.2fa-has-been-disabled'))
			windowModel.value = 'disabled'
			disable2FADialogModel.value = false
		})
		.catch(handler)
		.finally(() => {
			isVerifying.value = false
			form.code = undefined
		})
}

const { data, pending, error, execute } = useAsyncData(() => generate2FA('app'), { immediate: false })

watch(() => error.value, (error) => {
	if (error) {
		errorSnackbar(error.message)
	}
})

watch(() => windowModel.value, (val) => {
	if (val === 'disabled' && !authStore.twoFactorEnabled) {
		execute()
	}
}, { immediate: true })

const inputFocusHandler = (el: HTMLElement) => {
	el.querySelector('input')?.focus()
}
</script>
