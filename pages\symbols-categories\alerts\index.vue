<template>
	<div>
		<page title="Alerts">
			<template #actions>
				<v-btn
					v-if="can('alerts.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					Create Alert
				</v-btn>
			</template>

			<datatable
				ref="table"
				:url="Alerts.URL"
				:headers="headers"
				search-key="name"
			>
				<template #filter="{ model }">
					<v-select
						v-model="model.type"
						label="Type"
						:items="[
							{ title: 'Price stops change', value: Alerts.AlertType.STOP_DURATION },
							{ title: 'Daily Price change', value: Alerts.AlertType.PERCENTAGE },
							{ title: 'Spread change', value: Alerts.AlertType.SPREAD },
						]"
						clearable
					/>
				</template>
				<template #item.trigger="{ item }">
					<div v-if="item.type === Alerts.AlertType.STOP_DURATION">
						Price stops changing for {{ item.configValue }} minutes
					</div>
					<div v-else-if="item.type === Alerts.AlertType.PERCENTAGE">
						Daily Price change is more than {{ item.configValue }}%
					</div>
					<div v-else-if="item.type === Alerts.AlertType.SPREAD">
						Spread changes to (or higher) {{ item.configValue }}%
					</div>
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('alerts.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('alerts.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<AlertsCrud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import AlertsCrud from '~/components/Alerts/Crud.vue'
import { Alerts } from '~/types'

definePageMeta({
	permission: 'alerts.view',
})

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof AlertsCrud>()

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: 'Name',
		value: 'name',
	},
	{
		title: 'Trigger',
		value: 'trigger',
	},
	{
		title: 'Date',
		value: 'timestamp',
	},
	{
		value: 'actions',
	},
]
</script>

<style scoped lang="scss">
.v-sheet{
	&:fullscreen .v-table{
			height: 100%;
	}
	&:fullscreen .v-navigation-drawer{
			left:-320px !important;
			margin:0 !important;
			top:0 !important;
			height: 100% !important;
			padding-bottom: 0 !important;
		&.v-navigation-drawer--active {
			left:0 !important
		}
	}
}
.v-locale--is-rtl .v-sheet{
	&:fullscreen .v-navigation-drawer{
		right:-320px !important;
		left:unset !important;
		&.v-navigation-drawer--active {
			right:0 !important
		}
	}

}
</style>
