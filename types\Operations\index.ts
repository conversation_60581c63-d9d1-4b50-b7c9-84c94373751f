export namespace Operations {
	export namespace _Id {

		export const URL = (_Id: number | string) => `/operations/${_Id}`

		export interface GETResponse {
			id: number
			// name: string
			displayName: string
			type: number
			tradingPlatformId: number
			tradingPlatform: {
				id: number
				name: string
				createdAt: string
				updatedAt: null
			}
			createdAt: string
			updatedAt: string
		}

		export interface PUTRequest {
			// name: string
			displayName: string
			type: number
			tradingPlatformId: number
		}

		export interface POSTRequest {
			// name: string
			displayName: string
			type: number
			tradingPlatformId: number
		}

	}

}
