<template>
	<v-card
		v-resize="resizeHandler"
		height="100%"
		flat
		border
		:to="{ name: 'reports-online-traders' }"
	>
		<template #title>
			<div class="d-flex">
				<div>
					<div class=" text-caption text-medium-emphasis">
						{{ $t('widget-online-traders.online-traders') }}
					</div>
					<div class="v-card-title py-0 mt-n1 d-flex align-center ">
						<div class="me-2">
							<v-skeleton-loader
								type="list-item"
								:loading="isLoading"
								min-width="100"
								class="d-inline-block"
							>
								{{ $t('widget-online-traders.data-total-sessions', [data.total]) }}
							</v-skeleton-loader>
						</div>
					</div>
				</div>
				<v-spacer />
				<v-icon
					size="48"
					:color="`rgba(var(--v-theme-${iconColor}),0.3)`"
					icon="i-mdi:account-online-outline"
				/>
			</div>
		</template>
		<!-- <template #item> -->
		<div
			ref="mapContainerRef"
			style="height: calc(100% - 68px)"
		>
			<VueVectorMap
				:key="JSON.stringify(data.countries)"
				ref="mapRef"
				width="100%"
				height="100%"
				:options="{
					onRegionTooltipShow: tooltipHandler,
					zoomButtons: false,
					showTooltip: true,
					visualizeData: {
						scale: ['#ccedcd', $vuetify.theme.current.colors.success],
						values: data.countries,
					},
					map: 'world',
					// backgroundColor: 'transparent',
					draggable: false,
					// zoomButtons: true,
					// zoomOnScroll: true,
					// zoomOnScrollSpeed: 3,
					// zoomMax: 12,
					// zoomMin: 1,
					// zoomAnimate: true,
					// showTooltip: true,
					// zoomStep: 1.5,
					// bindTouchEvents: true,
					// // Line options
					// lineStyle: {
					// 	stroke: '#808080',
					// 	strokeWidth: 1,
					// 	strokeLinecap: 'round'
					// },
					// // Marker options
					// markersSelectable: false,
					// markersSelectableOne: false,
					// markerStyle: {
					// 	initial: {
					// 		r: 7,
					// 		fill: '#374151',
					// 		fillOpacity: 1,
					// 		stroke: '#FFF',
					// 		strokeWidth: 5,
					// 		strokeOpacity: .5
					// 	},
					// 	hover: {
					// 		fill: '#3cc0ff',
					// 		cursor: 'pointer'
					// 	},
					// 	selected: {
					// 		fill: 'blue'
					// 	},
					// 	selectedHover: {}
					// },
					// markerLabelStyle: {
					// 	initial: {
					// 		fontFamily: 'Verdana',
					// 		fontSize: 12,
					// 		fontWeight: 500,
					// 		cursor: 'default',
					// 		fill: '#374151'
					// 	},
					// 	hover: {
					// 		cursor: 'pointer'
					// 	},
					// 	selected: {},
					// 	selectedHover: {}
					// },
					// // Region options
					//regionsSelectable: true,
					// regionsSelectableOne: false,
					// regionStyle: {
					// 	initial: {
					// 		fill: '#dee2e8',
					// 		fillOpacity: 1,
					// 		stroke: 'none',
					// 		strokeWidth: 0
					// 	},
					// 	hover: {
					// 		fillOpacity: .7,
					// 		cursor: 'pointer'
					// 	},
					// 	selected: {
					// 		fill: '#9ca3af'
					// 	},
					// 	selectedHover: {}
					// },
					// regionLabelStyle: {
					// 	initial: {
					// 		fontFamily: 'Verdana',
					// 		fontSize: '12',
					// 		fontWeight: 'bold',
					// 		cursor: 'default',
					// 		fill: '#35373e'
					// 	},
					// 	hover: {
					// 		cursor: 'pointer'
					// 	}
					// }
				}"
			/>
		</div>
		<!-- </template> -->
	</v-card>
</template>

<script lang="ts" setup>
// @ts-ignore
import VueVectorMap from 'vuevectormap/src/js/component.js'

import 'vuevectormap/dist/css/vuevectormap.css.css'
import { type Alpha2Code, getName, registerLocale } from 'i18n-iso-countries'

import type { WidgetItemProps } from '../../types'
import { ConnectionStatus } from '~/stores/onlineSessions'
// @ts-ignore
await import('jsvectormap/dist/maps/world')
// @ts-ignore
registerLocale(await import('i18n-iso-countries/langs/en.json'))
registerLocale(await import('i18n-iso-countries/langs/ar.json'))

type Props = WidgetItemProps & {}

type Countries = {
	[key in Alpha2Code]?: number

}

type ModifiedData = {
	total: number
	countries: Countries
}

const mapRef = ref()

const p = defineProps<Props>()

const isLoading = ref(false)

const onlineSessionsStore = useOnlineSessionsStore()

const mapContainerRef = ref<HTMLElement>()

const isConnected = computed(() => onlineSessionsStore.connectionStatus === ConnectionStatus.Connected)

const { locale, t } = useI18n()

const previewData = ref<ModifiedData>({
	total: 117,
	countries: {
		EG: 29,
		US: 100,
		CA: 190,
		BR: 75,
	},
})

const realData = computed<ModifiedData>(() => {
	const countries: Countries = {}

	onlineSessionsStore.data?.data?.forEach((session) => {
		const iso = session.iso as Alpha2Code
		if (countries[iso] !== undefined) {
			countries[iso]++
		} else {
			countries[iso] = 1
		}
	})
	return {
		total: onlineSessionsStore.data.totalSessions,
		countries,
	}
})

const data = computed<ModifiedData>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return realData.value
})

const iconColor = computed(() => {
	if (p.isPreview) {
		return 'success'
	}
	if (isConnected.value) {
		return 'success'
	} else {
		return 'warning'
	}
})

const myPref = usePreferencesStore()

watch(() => p.isPreview, (value) => {
	if (!value && onlineSessionsStore.connectionStatus === ConnectionStatus.Disconnected) {
		onlineSessionsStore.connect().to(myPref.getKey('tradingServerName') as string)
	}
}, { immediate: true, once: true })
const tradingServerName = computed(() => myPref.keys.tradingServerName)

watch(() => tradingServerName.value, (value) => {
	if (!p.isPreview && value) {
		onlineSessionsStore.connect().to(value)
	}
}, {
	immediate: true,
})

const isMounted = useMounted()

const resizeHandler = () => {
	nextTick(() => {
		if (isMounted.value && mapRef.value?.map) {
			setTimeout(() => {
				mapRef.value?.map.updateSize()
			}, 10) // setTimeout needed when clicking cancel
		}
	})
}

const ro = new ResizeObserver(resizeHandler)
onMounted(() => {
	ro.observe(mapContainerRef.value!)
})

onUnmounted(() => {
	ro.disconnect()

	// remove class active from all pending jvm-tooltip elements
	const tooltips = document.querySelectorAll('.jvm-tooltip')
	tooltips.forEach((tooltip) => {
		tooltip.classList.remove('active')
	})
})

const getSessionsByCode = (code: Alpha2Code) => {
	return data.value.countries[code] || 0
}

const tooltipHandler = (_event: any, tooltip: any, code: Alpha2Code) => {
	tooltip.text(
		`<div class="text-subtitle-2">${getName(code, locale.value, { select: 'official' }) || tooltip.text()}</div>
		<p class="text-caption">${t('widget-online-traders.data-total-sessions', [getSessionsByCode(code)])}</p>`,
		true, // Enables HTML
	)
	tooltip.css({ zIndex: 10, backgroundColor: 'rgb(var(--v-theme-primary))' })
}
</script>
