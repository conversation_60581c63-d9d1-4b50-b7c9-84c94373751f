<template>
	<v-container>
		<v-row>
			<v-col>
				<v-text-field
					v-model.number="item.marginLiquidity"
					:error-messages="errors.marginLiquidity"
					hide-spin-buttons
					type="number"
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.MarginRates.liquidity-margin-rate')"
				/>
			</v-col>
			<v-col>
				<v-text-field
					v-model="item.marginCurrency"
					type="number"
					hide-spin-buttons
					:error-messages="errors.marginCurrency"
					:rules="rules({ required: true })"
					:label="$t('Symbols.MT5.Crud.MarginRates.currency-margin-rate')"
				/>
			</v-col>
		</v-row>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.MarginRates.margin-rates') }}</legend>
			<v-table>
				<thead>
					<tr>
						<th
							class="border-e"
							style="min-width: 150px"
						/>
						<th class="text-center">
							{{ $t('Symbols.MT5.Crud.MarginRates.market-order') }}
						</th>
						<th class="text-center">
							{{ $t('Symbols.MT5.Crud.MarginRates.limit-order') }}
						</th>
						<th class="text-center">
							{{ $t('Symbols.MT5.Crud.MarginRates.stop-order') }}
						</th>
						<th class="text-center">
							{{ $t('Symbols.MT5.Crud.MarginRates.stop-limit-order') }}
						</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td
							colspan="5"
							class="bg-grey-lighten-3 font-weight-medium"
						>
							{{ $t('Symbols.MT5.Crud.MarginRates.initial-margin') }}
						</td>
					</tr>
					<tr>
						<td class="border-e">
							{{ $t('Symbols.MT5.Crud.MarginRates.buy') }}
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialBuy"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialBuyLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialBuyStop"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialBuyStopLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
					</tr>
					<tr>
						<td class="border-e">
							{{ $t('Symbols.MT5.Crud.MarginRates.sell') }}
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialSell"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialSellLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialSellStop"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginInitialSellStopLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
					</tr>
					<tr>
						<td
							colspan="5"
							class="bg-grey-lighten-3 font-weight-medium"
						>
							{{ $t('Symbols.MT5.Crud.MarginRates.maintenance-margin') }}
						</td>
					</tr>
					<tr>
						<td class="border-e">
							{{ $t('Symbols.MT5.Crud.MarginRates.buy') }}
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceBuy"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceBuyLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceBuyStop"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceBuyStopLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
					</tr>
					<tr>
						<td class="border-e">
							{{ $t('Symbols.MT5.Crud.MarginRates.sell') }}
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceSell"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceSellLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceSellStop"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
						<td>
							<v-text-field
								v-model="item.marginMaintenanceSellStopLimit"
								single-line
								density="compact"
								hide-details
								flat
								variant="solo-filled"
								class="my-1"
							/>
						</td>
					</tr>
				</tbody>
			</v-table>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'

withDefaults(defineProps<Props>(), defaults)
</script>
