<template>
	<v-expansion-panel @group:selected="panelOpenHandler">
		<v-expansion-panel-title>
			<v-row
				no-gutters
				align="center"
			>
				<v-col class="d-flex justify-start">
					<div class="d-flex align-center">
						<!-- <div v-if="currentServer?.allowAll">
							<v-icon start icon="i-mdi:checkbox-marked" />
						</div>
						<div v-else-if="currentServer?.allowedGroups?.length">
							<v-icon start icon="i-mdi:minus-box" />
						</div>
						<div v-else>
							<v-icon start icon="i-mdi:checkbox-blank-outline" />
						</div> -->
						<v-checkbox
							:model-value="currentServer?.allowAll"
							start
							class="me-2"
							color="primary"
							density="compact"
							direction="vertical"
							:indeterminate="!!currentServer?.allowedGroups?.length"
							hide-details
							@update:model-value="checkboxHandler"
							@click.stop
						/>
						<div class="text-no-wrap">
							{{ p.item.displayName }}
						</div>
					</div>
				</v-col>
				<v-spacer />
				<v-col
					class="text--secondary"
					cols="auto"
				>
					<div class="text-medium-emphasis me-2 d-flex align-center">
						<div v-if="currentServer?.allowAll">
							{{ $t('TradingServerSelectPanel.all-groups-selected') }}
						</div>
						<div v-else-if="currentServer?.allowedGroups?.length">
							{{ $t('TradingServerSelectPanel.groups-selected-length', [currentServer?.allowedGroups?.length]) }}
						</div>
						<div v-else>
							{{ $t('TradingServerSelectPanel.no-group-selected') }}
						</div>
					</div>
				</v-col>
			</v-row>
		</v-expansion-panel-title>
		<v-expansion-panel-text v-if="p.item && currentServer">
			<div v-auto-animate>
				<v-switch
					v-model="currentServer.allowAll"
					:label="$t('TradingServerSelectPanel.allow-for-all-groups')"
					color="success"
					hide-details
				/>
				<trading-servers-select-groups
					v-if="!currentServer.allowAll"
					:item="p.item"
					:model-value="currentServer.allowedGroups"
					@update:model-value="setAllowedGroups"
				/>
			</div>
		</v-expansion-panel-text>
	</v-expansion-panel>
</template>

<script lang="ts" setup>
import type { Users } from '~/types/Users'
import type { TradingServers } from '~/types'

type Props = {
	item: TradingServers.Lookup.SingleRecord
	modelValue: Users.TradingServer[]
}

const p = defineProps<Props>()

const emit = defineEmits(['update:modelValue'])

const setAllowedGroups = (allowedGroups: Users.TradingServer['allowedGroups']) => {
	if (!allowedGroups) {
		return
	}
	const index = p.modelValue.findIndex(server => server.tradingServerId === p.item.id)
	if (index === -1) {
		emit('update:modelValue', [...p.modelValue, {
			tradingServerId: p.item.id,
			allowedGroups,
			allowAll: 0,
		}])
	} else {
		emit('update:modelValue', [
			...p.modelValue.slice(0, index),
			{
				...p.modelValue[index],
				allowedGroups,
			},
			...p.modelValue.slice(index + 1),
		])
	}
}

const currentServer = computed(() => {
	const server = p.modelValue.find(server => server.tradingServerId === p.item.id)

	if (server) {
		server.allowedGroups = server.allowedGroups || []
	}

	return server
})

const panelOpenHandler = ({ value }: { value: boolean }) => {
	if (value && !currentServer.value) {
		setAllowedGroups([])
	}
}

const checkboxHandler = (value: 0 | 1 | null) => {
	panelOpenHandler({ value: true })

	nextTick(() => {
		if (!currentServer.value) {
			return
		}
		currentServer.value.allowAll = value ?? 0
	})
}
</script>
