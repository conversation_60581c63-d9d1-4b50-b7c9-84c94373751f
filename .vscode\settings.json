{
   "[jsonc]": {
      "editor.defaultFormatter": "vscode.json-language-features"
   },
   "[typescript]": {
      "editor.defaultFormatter": "dbaeumer.vscode-eslint"
   },
   "[vue]": {
      "editor.defaultFormatter": "dbaeumer.vscode-eslint"
   },
   "cSpell.words": [
      "arcticons",
      "Argb",
      "autocloseout",
      "Bookdepth",
      "btns",
      "cacheable",
      "COLORREF",
      "datatable",
      "DataTable",
      "Defaultable",
      "defu",
      "expir",
      "Forex",
      "formkit",
      "iconify",
      "ingotbrokers",
      "jsonld",
      "jsvectormap",
      "klona",
      "lastmod",
      "lazyload",
      "meilisearch",
      "metatrader",
      "mqid",
      "Nuxt",
      "nuxtjs",
      "pinia",
      "RECALC",
      "REPORTSFLAGS",
      "riskman",
      "sidebase",
      "SLTP",
      "symbolcategory",
      "<PERSON><PERSON><PERSON>",
      "TRADEFLAGS",
      "treeview",
      "unocss",
      "Vite",
      "vuetify",
      "vueuse",
      "vuevectormap",
      "zadigetvoltaire",
      "zipcode"
   ],
   "css.autoValidation": "Save",
   "css.styleSheets": [
      "node_modules\\vuetify\\dist\\vuetify.css"
   ],
   "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "always"
   },
   "editor.detectIndentation": false,
   "editor.formatOnSave": true,
   "editor.formatOnType": false,
   "editor.insertSpaces": false,
   "editor.linkedEditing": true,
   "editor.tabSize": 3,
   "editor.tokenColorCustomizations": {
      "textMateRules": [
         {
            "scope": "invalid.illegal.character-not-allowed-here.html",
            "settings": {
               "foreground": "#808080"
            }
         }
      ]
   },
   "editor.wordBasedSuggestions": "off",
   "eslint.codeActionsOnSave.mode": "problems",
   "eslint.enable": true,
   "eslint.format.enable": true,
   "eslint.useESLintClass": true,
   "eslint.useFlatConfig": true,
   "files.autoSaveDelay": 1000,
   "html.autoClosingTags": true,
   "i18n-ally.dirStructure": "file",
   "i18n-ally.displayLanguage": "en",
   "i18n-ally.extract.autoDetect": false,
   "i18n-ally.extract.ignored": [
      "\n\t\t\t\t\t{{ 100 / value }}%\n\t\t\t\t",
      "*",
      "*",
      "Ingot Africa Ltd",
      "Ingot Broker Limited",
      "Ingot Brokers (Australia) Pty Ltd.",
      "INGOT Financial Brokerage LLC",
      "INGOT Financial Brokerage LLC",
      "INGOT Global Ltd.",
      "INGOT Global Ltd.",
      "International Development Bank",
      "YYYY-MM-DD HH:mm",
      "YYYY-MM-DD HH:mm:ss Z"
   ],
   "i18n-ally.extract.ignoredByFiles": {
      "components\\Crud.vue": [
         "Escape"
      ],
      "components\\Groups\\Crud\\Mt4\\Symbols.vue": [
         "\n\t\t\t\t\t{{ 100 / value }}%\n\t\t\t\t"
      ],
      "components\\Groups\\Crud\\Mt5\\Commissions.vue": [
         "*"
      ],
      "components\\Groups\\Crud\\Mt5\\Company.vue": [
         "Ingot Brokers (Australia) Pty Ltd."
      ],
      "components\\Symbols\\Crud\\MT5\\Sessions.vue": [
         "YYYY-MM-DD HH:mm"
      ],
      "components\\ViewTransactionCrud.vue": [
         "text-subtitle-1 font-weight-regular"
      ],
      "layouts\\default.vue": [
         ".v-main > div"
      ],
      "pages\\auth\\logging.vue": [
         "Getting Preferences Data..."
      ]
   },
   "i18n-ally.extract.keyMaxLength": 25,
   "i18n-ally.extract.keyPrefix": "{fileNameWithoutExt}.",
   "i18n-ally.extract.parsers.html": {
      "attributes": [
         "alt",
         "aria-label",
         "label",
         "placeholder",
         "text",
         "title"
         // "rules"
      ],
      "ignoredTags": [
         "script",
         "style"
      ],
      "inlineText": true,
      "vBind": true
   },
   "i18n-ally.indent": "\t",
   "i18n-ally.keystyle": "nested",
   "i18n-ally.localesPaths": [
      "config/i18n/locales"
   ],
   "iconify.includes": [
      "arcticons",
      "fe",
      "material-symbols",
      "material-symbols-light",
      "mdi",
      "mdi-light",
      "ph"
   ],
   "javascript.preferences.renameMatchingJsxTags": true,
   "nuxt.isNuxtApp": false,
   "nuxtr.defaultPackageManager": "NPM",
   "todo-tree.tree.disableCompactFolders": true,
   "todo-tree.tree.showBadges": true,
   "typescript.preferences.renameMatchingJsxTags": true,
   "typescript.tsdk": "node_modules\\typescript\\lib",
   "vue.autoInsert.dotValue": true,
   "vue.codeActions.enabled": true,
   "vue.codeLens.enabled": true,
   "vue.format.wrapAttributes": "auto",
   "vue.inlayHints.inlineHandlerLeading": true,
   "vue.inlayHints.missingProps": true,
   "vue.inlayHints.optionsWrapper": true,
   "vue.inlayHints.vBindShorthand": true,
   "vue.splitEditors.icon": true,
   "vue.splitEditors.layout.left": [
      "script",
      "scriptSetup",
      "styles"
   ],
   "vue.splitEditors.layout.right": [
      "customBlocks",
      "template"
   ],
   "vue.updateImportsOnFileMove.enabled": true,
   "workbench.colorCustomizations": {
      "commandCenter.border": "#e7e7e799",
      "sash.hoverBorder": "#ec4b00",
      "titleBar.activeBackground": "#ec4b00",
      "titleBar.activeForeground": "#e7e7e7",
      "titleBar.inactiveBackground": "#ec4b00",
      "titleBar.inactiveForeground": "#e7e7e799"
   }
}