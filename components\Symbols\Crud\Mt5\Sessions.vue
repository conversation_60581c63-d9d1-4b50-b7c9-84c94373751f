<template>
	<v-container>
		<v-row>
			<v-col cols="12">
				<fieldset>
					<legend>{{ $t('Symbols.MT5.Crud.Sessions.sessions-timing') }}</legend>

					<v-table hover>
						<thead>
							<tr>
								<th>
									{{ $t('Symbols.MT5.Crud.Sessions.day') }}
								</th>
								<th class="text-center">
									{{ $t('Symbols.MT5.Crud.Sessions.quote') }}
								</th>
								<th class="text-center">
									{{ $t('Symbols.MT5.Crud.Sessions.trade') }}
								</th>
								<th />
							</tr>
						</thead>
						<tbody>
							<tr
								v-for="day in daysOfWeek"
								:key="day.value"
								@dblclick="edit(day)"
							>
								<td>{{ day.text }}</td>
								<td
									class="text-center"
									v-html="item.sessionsQuotes?.[day.value]?formatRanges(item.sessionsQuotes[day.value]) : '-'"
								/>
								<td
									class="text-center"
									v-html="item.sessionsTrades?.[day.value]?formatRanges(item.sessionsTrades[day.value]) : '-'"
								/>
								<td>
									<v-btn
										:disabled="!item.sessionsQuotes?.[day.value]"
										variant="text"
										icon="i-mdi:pencil"
										size="small"
										@click="edit(day)"
									/>
								</td>
							</tr>
						</tbody>
					</v-table>
				</fieldset>
			</v-col>
		</v-row>
		<v-row>
			<v-col>
				<v-checkbox
					v-model="isTimeLimitEnabled"
					:label="$t('Symbols.MT5.Crud.Sessions.use-time-limit')"
					color="primary"
					@update:model-value="timeLimitHandler"
				/>
			</v-col>
			<v-col>
				<date-time-input
					v-model="timeStart"
					:disabled="!isTimeLimitEnabled"
					prepend-inner-icon="i-mdi:calendar"
				/>
			</v-col>
			<v-col>
				<date-time-input
					ref="timeExpirationRef"
					:key="`isTimeLimitEnabled-${isTimeLimitEnabled}`"
					v-model="timeExpiration"
					:disabled="!isTimeLimitEnabled"
					prepend-inner-icon="i-mdi:calendar"
					:min="timeStart"
					:rules="[!isTimeLimitEnabled?true:timeExpirationRule]"
				/>
			</v-col>
		</v-row>
	</v-container>
	<v-dialog
		v-model="dialogModel"
		max-width="800"
	>
		<v-confirm-edit
			v-if="item && editDay"
			v-slot="{ model: proxyModel, actions }"
			v-model="editDay"
			cancel-text="Reset"
			@save="confirmEditSaveHandler"
		>
			<v-card :title="$t('Symbols.MT5.Crud.Sessions.edit-editday-text', [editDay?.text])">
				<v-card-text>
					<symbols-crud-session-time-slider
						v-model="proxyModel.value.quote"
						type="MT5"
						:label="$t('Symbols.MT5.Crud.Sessions.quotes')"
						class="mt-4"
					/>
					<symbols-crud-session-time-slider
						v-model="proxyModel.value.trade"
						type="MT5"
						:label="$t('Symbols.MT5.Crud.Sessions.trades')"
						:disabled="!proxyModel.value.isSeparateTrading"
					/>
					<v-switch
						v-model="proxyModel.value.isSeparateTrading"
						hide-details
						color="success"
						class="ms-3 compact"
						:label="$t('Symbols.MT5.Crud.Sessions.enable-separate-trading-s')"
					/>
				</v-card-text>
				<template #actions>
					<v-btn
						variant="text"
						@click="dialogModel = false"
					>
						{{ $t('Symbols.MT5.Crud.Sessions.cancel') }}
					</v-btn>
					<v-spacer />
					<component :is="actions" />
				</template>
			</v-card>
		</v-confirm-edit>
	</v-dialog>
</template>

<script lang="ts" setup>
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import type { Symbols } from '~/types'

// import { Mask } from '~/types/Mask'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

type Day = {
	text: string
	value: number
}

type DayData = {
	text: string
	value: number
	isSeparateTrading: boolean
	trade: Symbols.MT5.MTConSymbolSession[]
	quote: Symbols.MT5.MTConSymbolSession[]
}

const isTimeLimitEnabled = ref((!!p.item.timeStart && !!p.item.timeExpiration) || false)

const { t } = useI18n()

const daysOfWeek = ref<Day[]>([
	{
		text: t('Symbols.MT5.Crud.Sessions.sunday'),
		value: 0,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.monday'),
		value: 1,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.tuesday'),
		value: 2,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.wednesday'),
		value: 3,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.thursday'),
		value: 4,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.friday'),
		value: 5,
	},
	{
		text: t('Symbols.MT5.Crud.Sessions.saturday'),
		value: 6,
	},
])

const dialogModel = ref(false)

const editDay = ref<DayData | null>(null)

const edit = (day: Day) => {
	const trade = toRaw(useCloned(p.item.sessionsTrades[day.value]).cloned.value)
	const quote = toRaw(useCloned(p.item.sessionsQuotes[day.value]).cloned.value)

	editDay.value = {
		text: day.text,
		value: day.value,
		isSeparateTrading: !isEqual(trade, quote),
		trade,
		quote,
	}

	nextTick(() => {
		dialogModel.value = true
	})
}

const format = (time: number) => {
	const hours = Math.floor(time / 60)
	const minutes = time % 60
	return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
}

const formatRanges = (ranges: Symbols.MT5.MTConSymbolSession[]) => {
	return ranges.map((range) => {
		return `${format(range.open)} - ${format(range.close)}`
	}).join('<br>')
}

const confirmEditSaveHandler = (dayData: DayData) => {
	dialogModel.value = false

	const sessionsQuotes = useCloned(p.item.sessionsQuotes).cloned.value
	const sessionsTrades = useCloned(p.item.sessionsTrades).cloned.value

	sessionsQuotes[dayData.value] = dayData.quote

	if (dayData.isSeparateTrading) {
		sessionsTrades[dayData.value] = dayData.trade
	} else {
		sessionsTrades[dayData.value] = dayData.quote
	}

	emit('update:item', {
		sessionsQuotes,
		sessionsTrades,
	})
}

const dayjs = useDayjs()

const timeStart = computed({
	get() {
		// convert from unix timestamp to date
		return dayjs.unix(p.item.timeStart).utc().format('YYYY-MM-DD HH:mm')
	},
	set(value: string) {
		emit('update:item', {
			timeStart: dayjs(value).utc().unix(),
		})
	},
})

const timeExpiration = computed({
	get() {
		// convert from unix timestamp to date
		return dayjs.unix(p.item.timeExpiration).utc().format('YYYY-MM-DD HH:mm')
	},
	set(value: string) {
		emit('update:item', {
			timeExpiration: dayjs(value).utc().unix(),
		})
	},
})

const timeLimitHandler = (value: boolean | null) => {
	if (value === false) {
		emit('update:item', {
			timeStart: 0,
			timeExpiration: 0,
		})
	}
}

const timeExpirationRule = (v: string) => {
	return (dayjs(v).unix() > dayjs(timeStart.value).unix()) || t('Symbols.MT5.Crud.Sessions.time-expiration-must-be-greater-than-time-start')
}
</script>
