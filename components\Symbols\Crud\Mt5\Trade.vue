<template>
	<v-container>
		<fieldset>
			<legend>
				{{ $t('Symbols.MT5.Crud.Trade.settings') }}
			</legend>
			<v-row>
				<v-col
					cols="12"
					md="7"
				>
					<v-text-field
						v-model.number="item.contractSize"
						:error-messages="errors.contractSize"
						:label="$t('Symbols.MT5.Crud.Trade.contract-size')"
						type="number"
					/>
					<v-select
						v-model="item.calcMode"
						:items="calcModeItems"
						:label="$t('Symbols.MT5.Crud.Trade.calculation')"
					/>
					<v-select
						v-model="item.tradeMode"
						:items="tradeModeItems"
						:label="$t('Symbols.MT5.Crud.Trade.trade')"
					/>
					<v-select
						v-model="item.gtcMode"
						:items="gtcModeItems"
						label="GTC"
					/>
					<v-select
						v-model="fillFlagsModel"
						multiple
						chips
						closable-chips
						:items="fillFlagsItems"
						:label="$t('Symbols.MT5.Crud.Trade.filling')"
						:rules="rules({ required: true, type: 'array' })"
					/>
					<v-select
						v-model="expirFlagsModel"
						chips
						closable-chips
						multiple
						:items="expirationFlagsItems"
						:label="$t('Symbols.MT5.Crud.Trade.expiration')"
						:rules="rules({ required: true, type: 'array' })"
					/>
				</v-col>
				<v-col
					cols="12"
					md="5"
				>
					<v-text-field
						v-model.number="item.stopsLevel"
						:error-messages="errors.stopsLevel"
						:label="$t('Symbols.MT5.Crud.Trade.limit-and-stop-level')"
						type="number"
						suffix="pt"
					/>
					<v-text-field
						v-model.number="item.freezeLevel"
						:error-messages="errors.freezeLevel"
						:label="$t('Symbols.MT5.Crud.Trade.freeze-level')"
						type="number"
						suffix="pt"
					/>
					<v-text-field
						v-model="item.quotesTimeout"
						:error-messages="errors.quotesTimeout"
						:label="$t('Symbols.MT5.Crud.Trade.max-quote-delay')"
						type="number"
						:base-color="$t('Symbols.MT5.Crud.Trade.seconds')"
					/>
					<!-- <div>selectTradeFlagsModel {{ selectTradeFlagsModel }}</div>
					<div>tradeFlagsModel {{ tradeFlagsModel }}</div>
					<div>item.tradeFlags {{ item.tradeFlags }}</div> -->
					<v-select
						v-if="[Symbols.MT5.CalcMode.TRADE_MODE_FOREX, Symbols.MT5.CalcMode.TRADE_MODE_FOREX_NO_LEVERAGE].includes(item.calcMode)"
						v-model="selectTradeFlagsModel"
						:items="tradeFlagsItems"
						:label="$t('Symbols.MT5.Crud.Trade.convert-profit')"
					/>
					<template v-else>
						<v-text-field
							v-model.number="item.tickSize"
							:label="$t('Symbols.MT5.Crud.Trade.tick-size')"
						/>
						<v-text-field
							v-model.number="item.tickValue"
							:label="$t('Symbols.MT5.Crud.Trade.tick-value')"
						/>
					</template>

					<v-select
						v-model="orderFlagsModel"
						multiple
						chips
						closable-chips
						:items="orderFlagsItems"
						:label="$t('Symbols.MT5.Crud.Trade.orders')"
					/>
					<v-switch
						v-model="tradeSignalModel"
						color="success"
						:label="$t('Symbols.MT5.Crud.Trade.enable-trading-signal')"
					/>
				</v-col>
			</v-row>
		</fieldset>

		<fieldset>
			<legend>{{ $t('Symbols.MT5.Crud.Trade.volumes') }}</legend>
			<v-row>
				<v-col
					cols="6"
					md="3"
				>
					<symbols-crud-fraction-ext
						v-model="item.volumeMin"
						v-model:ext="item.volumeMinExt"
						:label="$t('Symbols.MT5.Crud.Trade.minimum')"
					/>
				</v-col>
				<v-col
					cols="6"
					md="3"
				>
					<symbols-crud-fraction-ext
						v-model="item.volumeStep"
						v-model:ext="item.volumeStepExt"
						:label="$t('Symbols.MT5.Crud.Trade.step')"
					/>
				</v-col>
				<v-col
					cols="6"
					md="3"
				>
					<symbols-crud-fraction-ext
						v-model="item.volumeMax"
						v-model:ext="item.volumeMaxExt"
						:label="$t('Symbols.MT5.Crud.Trade.maximum')"
					/>
				</v-col>
				<v-col
					cols="6"
					md="3"
				>
					<symbols-crud-fraction-ext
						v-model="item.volumeLimit"
						v-model:ext="item.volumeLimitExt"
						:label="$t('Symbols.MT5.Crud.Trade.limit')"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
import { Symbols } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const calcModeItems = enumToItems(Symbols.MT5.CalcMode, 'Symbols.MT5.CalcMode')

const tradeModeItems = enumToItems(Symbols.MT5.TradeMode, 'Symbols.MT5.TradeMode')

const gtcModeItems = enumToItems(Symbols.MT5.GTCMode, 'Symbols.MT5.GTCMode')

const fillFlagsExcludedItems = [Symbols.MT5.FillingFlags.FILL_FLAGS_ALL, Symbols.MT5.FillingFlags.FILL_FLAGS_NONE]
const fillFlagsItems = enumToItems(Symbols.MT5.FillingFlags, 'Symbols.MT5.FillingFlags',
	item => fillFlagsExcludedItems.includes(item.value)
		? undefined
		: item,
)
const expirationFlagsExcludedItems = [Symbols.MT5.ExpirationFlags.TIME_FLAGS_ALL, Symbols.MT5.ExpirationFlags.TIME_FLAGS_NONE]
const expirationFlagsItems = enumToItems(Symbols.MT5.ExpirationFlags, 'Symbols.MT5.ExpirationFlags',
	item => expirationFlagsExcludedItems.includes(item.value)
		? undefined
		: item)

const orderFlagsExcludedItems = [Symbols.MT5.OrderFlags.NONE, Symbols.MT5.OrderFlags.ALL]
const orderFlagsItems = enumToItems(Symbols.MT5.OrderFlags, 'Symbols.MT5.OrderFlags',
	item => orderFlagsExcludedItems.includes(item.value)
		? undefined
		: item)

const tradeFlagsItems = enumToItems(Symbols.MT5.TradeFlags, 'Symbols.MT5.TradeFlags', item => [Symbols.MT5.TradeFlags.ALLOW_SIGNALS, Symbols.MT5.TradeFlags.ALL].includes(item.value) ? undefined : item)

const computedFillFlags = computed({
	get() {
		return p.item.fillFlags
	},
	set(value: number) {
		emit('update:item', {
			fillFlags: value,
		})
	},
})

const { model: fillFlagsModel } = useMultiSelectEnum(computedFillFlags, Symbols.MT5.FillingFlags, {
	exclude: fillFlagsExcludedItems,
})

const computedExpirFlags = computed({
	get() {
		return p.item.expirFlags
	},
	set(value: number) {
		emit('update:item', {
			expirFlags: value,
		})
	},
})

const { model: expirFlagsModel } = useMultiSelectEnum(computedExpirFlags, Symbols.MT5.ExpirationFlags, { exclude: expirationFlagsExcludedItems })

const computedOrderFlags = computed({
	get() {
		return p.item.orderFlags
	},
	set(value: number) {
		emit('update:item', {
			orderFlags: value,
		})
	},
})

const { model: orderFlagsModel } = useMultiSelectEnum(computedOrderFlags, Symbols.MT5.OrderFlags, { exclude: orderFlagsExcludedItems })

const computedTradeFlags = computed({
	get() {
		return p.item.tradeFlags
	},
	set(value: number) {
		emit('update:item', {
			tradeFlags: value,
		})
	},
})

const { model: tradeFlagsModel } = useMultiSelectEnum(computedTradeFlags, Symbols.MT5.TradeFlags)

const selectTradeFlagsModel = computed<number | undefined>({
	get() {
		const rArray = tradeFlagsModel.value.filter((item) => {
			return item !== Symbols.MT5.TradeFlags.ALLOW_SIGNALS
		})
		return rArray?.[rArray.length - 1]
	},
	set(value) {
		if (value || value === 0) {
			const arr = []

			if (tradeSignalModel.value) {
				arr.push(Symbols.MT5.TradeFlags.ALLOW_SIGNALS)
			}

			arr.push(value)

			tradeFlagsModel.value = arr
		} else {
			// tradeFlagsModel.value = tradeFlagsModel.value.filter(item => item !== Symbols.TradeFlags.ALLOW_SIGNALS)
		}
	},
})

const tradeSignalModel = computed({
	get() {
		return tradeFlagsModel.value.includes(Symbols.MT5.TradeFlags.ALLOW_SIGNALS)
	},
	set(value: boolean) {
		if (value) {
			// tradeFlagsModel.value.push(Symbols.TradeFlags.ALLOW_SIGNALS)
			tradeFlagsModel.value = [...tradeFlagsModel.value, Symbols.MT5.TradeFlags.ALLOW_SIGNALS]
		} else {
			tradeFlagsModel.value = tradeFlagsModel.value.filter(item => item !== Symbols.MT5.TradeFlags.ALLOW_SIGNALS)
		}
	},
})
</script>
