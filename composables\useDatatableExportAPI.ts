export const useUseDatatableExportAPI = ({
	url,
	query,
}: {
	url: string
	query: Ref<Record<string, any>>
}) => {
	const pending = ref(false)

	const error = ref(null)

	const { $api } = useNuxtApp()

	const { successSnackbar, errorSnackbar } = useSnackbar()

	const handler = () => {
		pending.value = true

		// const query = tableRef.value?.query

		const { cloned: clonedQuery } = useCloned(query, { deep: true })

		delete clonedQuery.value.page
		delete clonedQuery.value.pageSize

		$api(url, {
			params: {
				...clonedQuery.value,
			},
		})
			.then(() => {
				successSnackbar({
					title: 'Exporting data...',
					text: 'The data is being exported. You will be notified by email when it is ready.',
				})
			})
			.catch((e) => {
				errorSnackbar(e.value.message)
			})
			.finally(() => {
				pending.value = false
			})
	}
	return {
		pending,
		handler,
		error,
	}
}
