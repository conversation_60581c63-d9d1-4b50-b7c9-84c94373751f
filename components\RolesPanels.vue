<template>
	<v-expansion-panels
		v-model="expansionModel"
		eager
		class="no-separator"
		expand-icon="i-mdi:pencil"
	>
		<div
			ref="container"
			v-auto-animate
			class="flex-grow-1"
		>
			<v-form
				v-for="item, index in items"
				ref="formRef"
				v-slot="{ isValid }"
				:key="`item-${JSON.stringify(item)}`"
				class="mb-2"
			>
				<v-expansion-panel
					elevation="0"
					rounded="lg"
					class="border"
					:class="{ 'border-error': isValid.value ===false }"
					expand-icon="i-mdi:pencil-outline"
					:focusable="false"
					:disabled="(!isValid.value && expansionModel === index) || p.disabled"
					eager
				>
					<template #title>
						<v-icon
							v-if="sortable"
							class="handle me-2"
							:ripple="false"
							variant="text"
							icon="i-mdi:drag"
							@click.stop
						/>
						<div
							:class="{ 'text-error': isValid.value ===false }"
							class="text-subtitle-2"
						>
							<slot
								name="title"
								v-bind="{ item, index }"
							/>
						</div>
						<!-- <v-spacer />
						<v-btn
							:disabled="items.length === 1"
							variant="text"
							icon="i-mdi:trash-outline"
							@click.stop="removeFromArray(index)"
						/> -->
					</template>
					<v-expansion-panel-text>
						<v-confirm-edit
							v-slot="{ model, save, cancel, isPristine, actions }"
							v-model="items[index]"
							@save="expansionModel = null"
							@cancel="expansionModel = null"
						>
							<v-card flat>
								<template #text>
									<slot v-bind="{ model }" />
								</template>

								<template #actions>
									<div class="d-none">
										<!-- to hide the default btns -->
										<component :is="actions" />
									</div>
									<v-btn
										variant="text"
										color="error"
										@click="removeFromArray(index)"
									>
										{{ $t('RolesPanels.delete') }}
									</v-btn>
									<v-spacer />

									<v-btn
										variant="text"
										:disabled="isPristine"
										@click="cancelHandler(index, cancel)"
									>
										{{ $t('RolesPanels.cancel') }}
									</v-btn>
									<v-btn
										variant="text"
										:disabled="!isValid.value || isPristine"
										@click="saveHandler(index, save)"
									>
										{{ $t('RolesPanels.save') }}
									</v-btn>
								</template>
							</v-card>
						</v-confirm-edit>
					</v-expansion-panel-text>
				</v-expansion-panel>
			</v-form>
		</div>
		<v-alert
			v-if="!items.length"
			density="compact"
			variant="tonal"
			type="info"
			class="flex-grow-1 text-medium-emphasis"
		>
			{{ $t('RolesPanels.there-is-no-any-data') }}
		</v-alert>
	</v-expansion-panels>
</template>

<script lang="ts" setup>
import { useSortable, type UseSortableOptions } from '@vueuse/integrations/useSortable'
import type { VForm } from 'vuetify/components'

const { $confirm } = useNuxtApp()

const { t } = useI18n()

const items = defineModel<any[]>({
	type: Array,
	default: () => [],
})

type Props = {
	currentPanel?: number | null
	sortable?: boolean
	disabled?: boolean
}

const p = withDefaults(defineProps<Props>(), {
	sortable: true,
	currentPanel: null,
	disabled: false,
})

const expansionModel = defineModel<Props['currentPanel']>('currentPanel', {
	type: Number,
	default: null,
})

const removeFromArray = (index: number) => {
	$confirm(t('RolesPanels.are-you-sure-you-want-to-remove')).then(() => {
		items.value.splice(index, 1)
		expansionModel.value = null
	}).catch(() => {})
}
const defaultSortableOptions: UseSortableOptions = {
	handle: '.handle',
	animation: false,
	// fallbackOnBody: true,
	swapThreshold: 0.1,
	ghostClass: 'ghost',
	// forceFallback: true,
}

const container = ref<HTMLElement | null>(null)

if (p.sortable) {
	useSortable(container, items, defaultSortableOptions)
}

const formRef = ref<typeof VForm | null>(null)

const saveHandler = async (index: number, save: Function) => {
	await formRef.value?.[index].validate()
	save()
}

const cancelHandler = async (index: number, cancel: Function) => {
	await formRef.value?.[index].validate()
	cancel()
}

watch(expansionModel, async (_val, valBefore) => {
	if (valBefore !== null && valBefore !== undefined) {
		await formRef.value?.[valBefore].validate()
	}
})
</script>

<style scoped lang="scss">
 .ghost {
	background-color: rgb(var(--v-theme-background));
	border: 1px dashed rgb(var(--v-theme-on-background));
	margin-bottom: 8px !important;
	border-radius: 8px;

	* {
		visibility: hidden;
	}
}
.no-separator{
	.v-expansion-panel:not(:first-child)::after{
		content: unset;
	}
}
</style>
