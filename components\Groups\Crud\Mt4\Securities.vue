<template>
	<v-container fluid>
		<v-data-table
			:headers="headers"
			:items="items"
			hide-default-footer
			:items-per-page="-1"
			density="compact"
			class="compact"
		>
			<template #item.icon="{ item }">
				<!-- <v-badge inline :color="item.show?'success':'grey'" :model-value="true" /> -->
				<truthy-icon
					:model-value="item.show"
					true-icon="i-mdi:check-circle-outline"
					type="number"
					false-icon-color="warning"
					false-icon="i-mdi:alert-circle-outline"
				/>
			</template>
			<template #item.type="{ index }:{index:number}">
				<div v-if="types && types[index]">
					{{ types[index].title }}
				</div>
			</template>
			<template #item.execution="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item }"
					:items="executionModeItems"
					item-key="value"
				>
					{{ item.title }}
				</find-item-by-id>
			</template>
			<template #item.trade="{ value }">
				<truthy-icon
					:model-value="value"
					type="number"
				/>
			</template>
			<template #item.spreadDiff="{ value }">
				<div v-if="!value">
					{{ $t('Groups.MT4.Crud.Securities.default') }}
				</div>
				<div v-else>
					{{ value }}
				</div>
			</template>
			<template #item.commission="{ item }">
				<div>{{ money(Math.round(item.commBase)) }} / {{ money(Math.round(item.commAgent)) }}</div>
			</template>
			<template #item.actions="{ index }">
				<v-btn
					icon="i-mdi:pencil"
					size="small"
					variant="text"
					@click="setForEdit(index)"
				/>
			</template>
		</v-data-table>

		<v-dialog
			v-model="editModel"
			max-width="900"
			persistent
		>
			<v-confirm-edit
				ref="confirmEditRef"
				v-slot="{ actions, model }"
				v-model="currentSec"
				cancel-text="Reset"
				@save="save"
			>
				<v-card :title="$t('Groups.MT4.Crud.Securities.edit')">
					<template #text>
						<v-form ref="editFormRef">
							<groups-crud-mt4-edit-security v-model="model.value" />
						</v-form>
					</template>
					<template #actions>
						<v-btn @click="editModel = false">
							{{ $t('Groups.MT4.Crud.Securities.cancel') }}
						</v-btn>
						<v-spacer />
						<component :is="actions" />
					</template>
				</v-card>
			</v-confirm-edit>
		</v-dialog>
	</v-container>
</template>

<script lang="ts" setup>
import type { VConfirmEdit, VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups, Symbols } from '~/types'
// import { AutoCloseoutMode, CommissionCalculationType, CommissionUnit, ExecutionMode } from '~/types/Groups/GroupSec'

const editFormRef = ref <InstanceType<typeof VForm>>()

const confirmEditRef = ref<InstanceType<typeof VConfirmEdit>>()

const { data: types } = useApi<Symbols.MT4.Type.Lookup.GETResponse>(Symbols.MT4.Type.Lookup.URL)

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const editModel = ref(false)

const { t } = useI18n()

const headers = [
	{
		title: '',
		value: 'icon',
	},
	{
		title: t('Groups.MT4.Crud.Securities.type'),
		value: 'type',
	},
	{
		title: t('Groups.MT4.Crud.Securities.trade'),
		value: 'trade',
	},
	{
		title: t('Groups.MT4.Crud.Securities.exec'),
		value: 'execution',
	},
	// {
	// 	title: 'Spread',
	// 	value: 'spreadDiff',
	// },
	{
		title: t('Groups.MT4.Crud.Securities.commission'),
		value: 'commission',
	},
	{
		title: '',
		value: 'actions',
	},
]

const executionModeItems = enumToItems(Groups.MT4.ExecutionMode, 'Groups.MT4.ExecutionMode')

const currentSymbolIndex = ref<number>(0)

const items = computed<Groups.MT4.GroupSec[]>({
	get() {
		return p.item.securityGroups
	},
	set(value) {
		emit('update:item', {
			securityGroups: value,
		})
	},
})

const currentSec = computed<Groups.MT4.GroupSec>({
	get: () => items.value[currentSymbolIndex.value],
	set: (value) => {
		const clonedItems = useCloned(items.value).cloned.value
		clonedItems[currentSymbolIndex.value] = value
		items.value = clonedItems
	},
})

const setForEdit = (index: number) => {
	currentSymbolIndex.value = index
	editModel.value = true
	setTimeout(() => {
	}, 500)
}
const save = async (_value: Groups.MT4.GroupSec) => {
	const validation = await editFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	editModel.value = false
}
</script>
