<template>
	<v-card
		class="mx-auto px-5 pt-8 pb-4"
		elevation="10"
		width="450"
	>
		<v-card-title class="mb-8 d-flex align-end">
			<nuxt-link
				:to="{ path: '/auth/login' }"
				class="text-decoration-none"
			>
				<logo width="110" />
			</nuxt-link>
			<v-spacer />
		</v-card-title>
		<v-card-text>
			<v-window v-model="windowModel">
				<v-window-item
					v-for="route in authRoutes"
					:key="route.name"
					:value="route.name"
				>
					<component :is="route.component" />
				</v-window-item>
			</v-window>
		</v-card-text>
		<!-- <v-card-actions>
			<v-row>
				<v-spacer />
				<v-col class="text-end">
					<v-btn
						prepend-icon="i-mdi:earth"
						size="small"
						color=""
						append-icon="i-mdi:caret-down"
					>
						{{ $i18n.localeProperties.name }}
						<v-menu activator="parent">
							<v-list
								:items="$i18n.locales"
								item-title="name"
								item-value="code"
								density="compact"
								@update:selected="switchLocaleHandler($event as string[])"
							/>
						</v-menu>
					</v-btn>
				</v-col>
			</v-row>
		</v-card-actions> -->
	</v-card>
</template>

<script lang="ts" setup>
import login from './auth/login.vue'
import logging from './auth/logging.vue'
import twoFA from './auth/2fa.vue'
import forgot from './auth/forgot.vue'
import reset from './auth/reset.vue'

export type LoginHistoryItem = {
	email: string
	name: string
	lastLogin: string
}

const routeBaseName = useRouteBaseName()

const router = useRouter()

definePageMeta({
	layout: 'auth',
})

const authRoutes = [
	{
		name: 'login',
		component: login,
	},
	{
		name: 'logging',
		component: logging,
	},
	{
		name: '2fa',
		component: twoFA,
	},
	{
		name: 'forgot',
		component: forgot,
	},
	{
		name: 'reset',
		component: reset,
	},
]

const windowModel = computed(() => {
	// if (!routeBaseName(router.currentRoute.value)?.includes('auth-')) {
	// 	// it means its redirecting, so stay at last view
	// 	return lastWindowModel.value
	// }
	return routeBaseName(router.currentRoute.value)?.replace('auth-', '') || 'login'
})
</script>
