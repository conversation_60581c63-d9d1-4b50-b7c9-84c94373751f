<template>
	<v-container
		fluid
		class="py-0"
	>
		<fieldset>
			<legend>
				{{ $t('Symbols.MT4.Crud.Calculation.settings') }}
			</legend>
			<v-row dense>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.contractSize"
						:error-messages="errors.contractSize"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.contract-size')"
					/>
				</v-col>
				<v-col offset-md="7" />
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginInitial"
						:error-messages="errors.marginInitial"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.initial-margin')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginMaintenance"
						:error-messages="errors.marginMaintenance"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.maintenance')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.marginHedged"
						:error-messages="errors.marginHedged"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.hedged')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.tickSize"
						:error-messages="errors.tickSize"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.tick-size')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="item.tickValue"
						:error-messages="errors.tickValue"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.tick-price')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="4"
				>
					<v-text-field
						v-model.number="marginDivider"
						:error-messages="errors.marginDivider"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT4.Crud.Calculation.percentage')"
					/>
				</v-col>
			</v-row>
		</fieldset>

		<fieldset>
			<legend>
				{{ $t('Symbols.MT4.Crud.Calculation.calculation-method') }}
			</legend>
			<v-row dense>
				<v-col
					cols="12"
					md="12"
				>
					<v-select
						v-model="item.marginMode"
						:error-messages="errors.marginMode"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT4.Crud.Calculation.margin-calculation')"
						:items="marginModeItems"
					/>
				</v-col>
				<v-col
					cols="12"
					md="12"
				>
					<v-select
						v-model="item.profitMode"
						:error-messages="errors.profitMode"
						:rules="rules({ required: true })"
						:label="$t('Symbols.MT4.Crud.Calculation.profit-calculation')"
						:items="profitModeItems"
					/>
				</v-col>
				<v-col
					cols="12"
					md="12"
				>
					<v-checkbox
						v-model="item.marginHedgedStrong"
						:true-value="1"
						:false-value="0"
						color="primary"
						hide-details
						:label="$t('Symbols.MT4.Crud.Calculation.strong-hedged-margin-mode')"
						class="compact"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Symbols } from '~/types'

const emit = defineEmits<Emit>()

const p = withDefaults(defineProps<Props>(), defaults)

const marginModeItems = enumToItems(Symbols.MT4.MarginCalculationMode, 'Symbols.MT4.MarginCalculationMode')

const profitModeItems = enumToItems(Symbols.MT4.ProfitCalculationMode, 'Symbols.MT4.ProfitCalculationMode')

const marginDivider = computed({
	get: () => 100 / p.item.marginDivider,
	set: (value: number) => emit('update:item', { marginDivider: 100 / value }),
})
</script>
