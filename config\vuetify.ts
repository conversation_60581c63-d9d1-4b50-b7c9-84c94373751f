import type { ModuleOptions } from 'vuetify-nuxt-module'
import { md3 } from 'vuetify/blueprints'
import { supportedLocalesCodes } from './general'

const config: ModuleOptions = {
	moduleOptions: {
		importComposables: true,
		includeTransformAssetsUrls: true,
		prefixComposables: false,
		// styles: {
		// 	configFile: './assets/styles/vuetify.variables.scss',
		// },
		ssrClientHints: {
			prefersColorScheme: true,
			viewportSize: true,
			prefersColorSchemeOptions: {
				cookieName: 'color-scheme',
				darkThemeName: 'dark',
				lightThemeName: 'light',
				useBrowserThemeOnly: false,
			},
		},
	},
	vuetifyOptions: {
		blueprint: md3,
		date: {
			adapter: 'dayjs',
		},
		display: {
			thresholds: {
				xs: 600,
				sm: 960,
				md: 1280,
				lg: 1920,
				xl: 2560,
			},
		},
		aliases: {
			// 'Select': 'VSlideGroup',
		},
		directives: true,
		defaults: {
			global: {
				// ripple: false,
			},

			VSheet: {
				// elevation: 10,
			},
			VBtn: {
				rounded: 'lg',
				flat: true,
			},
			VCombobox: {
				returnObject: false,
			},

		},
		icons: {
			defaultSet: 'unocss-mdi',
			unocssIcons: {
				/* Override vuetify icons */

			},
			unocssAdditionalIcons: {
				/* additional common icons */
				eyeDropper: 'i-mdi:eyedropper',
				treeviewCollapse: 'i-mdi:menu-down',
				treeviewExpand: 'i-mdi:menu-right',
			},
		},
		localeMessages: supportedLocalesCodes,
		theme: {
			defaultTheme: 'dark',
			themes: {
				light: {
					colors: {
						surface: '#ffffff',
						primary: '#592282', // Purple
						accent: '#BD1C80', // pink
						tertiary: '#328bcc', // blue
						background: '#F7F8FA',
						active: '#4CAF50',
						inactive: '#9E9E9E',
						link: '#3F51B5',
						account: '#face00',
						snackbar: '#464855',
						dark: '#464855',
						warning: '#FFC107',
					},
					dark: false,
				},
				dark: {
					colors: {
						surface: '#252527',
						primary: '#a75fdb', // Purple
						accent: '#ff23ac', // pink
						tertiary: '#328bcc', // blue
						background: '#1c1c1e',
						active: '#4CAF50',
						inactive: '#9E9E9E',
						link: '#7489ff',
						account: '#face00',
						snackbar: '#464855',
						dark: '#464855',
						warning: '#FFC107',
					},
					dark: true,
				},
			},
		},

	},
}

export default config
