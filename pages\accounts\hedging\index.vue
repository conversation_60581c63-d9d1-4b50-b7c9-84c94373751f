<template>
	<page title="Hedging Accounts">
		<template #actions>
			<!-- v-if="can('hedging.create')" -->
			<v-btn
				v-if="can('hedging.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="hedgingCrud?.create()"
			>
				Create Hedging Account
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/hedging"
			:headers="headers"
			search-key="search"
			item-value="login"
			:default-model="{ name: '' }"
		>
			<!-- <template #filter="{model}">
				<api-items v-slot="props" :url="Groups.Lookup.URL">
					<v-autocomplete v-model="model.group" :label="$t('accounts.group')" v-bind="props" clearable />
				</api-items>
			</template> -->
			<!-- <template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:loading="isClearingCache"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template> -->

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('hedging.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="hedgingCrud?.update(item)"
					/>
					<v-btn
						v-if="can('hedging.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="hedgingCrud?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<HedgingCrud
		ref="hedgingCrud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
</template>

<script lang="ts" setup>
import HedgingCrud from '@/components/Hedging/Crud.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'

const table = ref <InstanceType<typeof Datatable> | null>(null)

const hedgingCrud = ref<InstanceType<typeof HedgingCrud> | null>(null)

// const isClearingCache = ref(false)

const { t } = useI18n()

const headers: Headers = [
	{
		title: t('accounts.login'),
		value: 'login',
		nowrap: true,
	},
	{
		title: 'Description',
		value: 'description',
		nowrap: true,
	},
	{
		title: 'Trading Server',
		value: 'tradingServerId',
		nowrap: true,
	},
	{
		value: 'actions',
	},
]

// const clearCacheAndRefresh = (callback: Function) => {
// 	const { $api } = useNuxtApp()

// 	isClearingCache.value = true

// 	$api('/accounts/refresh', {
// 		method: 'POST',
// 	}).then(() => {
// 		if (callback) {
// 			callback()
// 		}
// 	}).finally(() => {
// 		isClearingCache.value = false
// 	})
// }
</script>
