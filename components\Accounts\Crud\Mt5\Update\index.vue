<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Accounts.MT4.Crud.account')"
		item-identity="name"
		:navigation-drawer-props="{ width: 650 }"
		:url="Accounts.MT5._Id.URL(':login')"
		:validation-callback="validate"
		:update-url="Accounts.MT5._Id.URL('')"
		v-bind="$attrs"
		@closed="closeHandler"
	>
		<template #title="{ item }">
			{{ $t('Accounts.MT5.Crud.update-account', [item.name]) }}
		</template>
		<template #default="{ item, errors, isUpdateAction }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' }, VBtn: { density: 'default' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-3 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="!tab.passValidation && tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class="flex-grow-1"
						>
							<v-form
								v-for="tab in tabs"
								ref="tabForm"
								:key="tab.value"
								v-model="tab.isValid.value"
							>
								<v-tabs-window-item
									:value="tab.value"
									:eager="isValidated"
								>
									<component
										:is="tab.component"
										:update-action="isUpdateAction "
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-tabs-window-item>
							</v-form>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Personal from './Personal.vue'
import Account from './Account.vue'
import Limits from './Limits.vue'
import Security from './Security.vue'
// import Info from './Info.vue'
import type { DefaultItem } from '~/components/Crud.vue'
import Crud from '~/components/Crud.vue'
import { Accounts } from '~/types'

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem<Accounts.MT5._Id.PUTRequest> = {
	login: 0,
	group: 'managers\\administrators',
	certSerialNumber: 0,
	rights: 2403 as Accounts.MT5.EnUsersRights,
	registration: 0,
	lastAccess: 0,
	lastIP: '',
	name: '',
	company: '',
	account: '',
	country: 'Jordan',
	language: 1,
	city: '',
	state: '',
	zipCode: '',
	address: '',
	phone: '',
	eMail: '',
	id: '',
	status: '',
	comment: '',
	color: **********,
	phonePassword: '',
	leverage: 100,
	agent: 0,
	balance: 0,
	credit: 0,
	interestRate: 0,
	commissionDaily: 0,
	commissionMonthly: 0,
	commissionAgentDaily: 0,
	commissionAgentMonthly: 0,
	balancePrevDay: 0,
	balancePrevMonth: 0,
	equityPrevDay: 0,
	equityPrevMonth: 0,
	lastPassChange: **********,
	leadCampaign: '',
	leadSource: '',
	externalAccountTotal: 0,
	mqid: '0',
	clientID: 0,
	firstName: '',
	lastName: '',
	middleName: '',
	otpSecret: '',
	limitOrders: 0,
	limitPositionsValue: 0,
} satisfies DefaultItem<Accounts.MT5._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	passValidation?: boolean | undefined
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs = computed<Tab[]>(() => [
	{
		value: 'personal',
		text: t('Accounts.MT5.Crud.personal'),
		component: Personal,
		isValid: ref(true),
	},
	{
		value: 'account',
		text: t('Accounts.MT5.Crud.account'),
		component: Account,
		isValid: ref(true),
	},
	{
		value: 'limits',
		text: t('Accounts.MT5.Crud.limits'),
		component: Limits,
		isValid: ref(true),
	},
	{
		value: 'security',
		text: t('Accounts.MT4.Crud.security'),
		passValidation: true,
		isValid: ref(true),
		component: Security,

	},
	// {
	// 	value: 'info',
	// 	text: t('Accounts.MT4.Crud.info'),
	// 	component: Info,
	// 	isValid: ref(true),
	// },
]
	.map((tab: Tab, index: number) => {
		tab.errorsCount = computed(() => tabForm.value?.[index]?.errors.length || 0)
		return tab
	}),
)

const tabModel = ref(tabs.value[0].value)

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error(t('Accounts.MT4.Crud.please-fix-the-form-errors')))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.value.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs.value[0].value
}

type Expose = {
	create: () => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: () => crudRef.value!.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
