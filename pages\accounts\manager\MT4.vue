<template>
	<page :title="$t('accounts.manager')">
		<template #actions>
			<v-btn
				variant="text"
				prepend-icon="i-mdi:export-variant"
				color="primary"
				@click="exportPermissions"
			>
				Export
			</v-btn>
			<v-btn
				v-if="can('manager.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="crud?.create()"
			>
				{{ $t('accounts.create-manger') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/manager/mt4"
			:headers="headers"
			search-key="search"
			item-value="login"
			:default-model="{ name: '' }"
			show-expand
			v-bind="props"
		>
			<!-- <template #filter="{model}">
				<api-items v-slot="props" :url="Groups.Lookup.URL">
					<v-autocomplete v-model="model.group" :label="$t('accounts.group')" v-bind="props" clearable />
				</api-items>
			</template> -->
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:loading="isClearingCache"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>
			<template #toolbar.middle-end>
				<v-btn
					v-if="selectedModel.length"
					prepend-icon="i-mdi:content-copy"
				>
					{{ $t('accounts.copy-as') }}
					<v-menu
						activator="parent"
						offset-y
					>
						<v-list
							:items="copyMenuItems"
							density="compact"
						/>
					</v-menu>
				</v-btn>
			</template>
			<template #item.login="{ value, item }">
				<manager-icon
					platform="MT4"
					:manager="item"
					start
				/>
				{{ value }}
			</template>
			<template #item.groups="{ value, item }">
				<v-chip-group column>
					<v-chip
						v-for="group in value.split(',').filter(Boolean)"
						:key="item.login+'-'+group"
						density="compact"
						color="primary"
						:text="group"
					/>
				</v-chip-group>
			</template>
			<template #item.data-table-expand="{ toggleExpand, internalItem, isExpanded }">
				<v-btn
					:prepend-icon="`i-mdi:chevron-${isExpanded(internalItem)? 'up':'down'}`"
					variant="text"
					size="small"
					@click="toggleExpand(internalItem)"
				>
					{{ isExpanded(internalItem)? t('accounts.hide-rights') : t('accounts.show-rights') }}
				</v-btn>
			</template>
			<template #expanded-row="{ item }">
				<td
					:colspan="headers.length"
					class="pa-2"
				>
					<v-card
						flat
						:title="t('accounts.rights')"
					>
						<template #text>
							<v-defaults-provider :defaults="{ VChip: { density: 'compact', class: 'text-caption flex-shrink-0', rounded: true } }">
								<v-chip-group
									v
									density="compact"
									variant="flat"
									column
									color="primary"
								>
									<v-chip
										v-if="item.manager"
										:text="$t('Manager.MT4.Crud.Permissions.manager')"
									/>

									<v-chip
										v-if="item.seeTrades"
										:text="$t('Manager.MT4.Crud.Permissions.supervise-trades')"
									/>

									<v-chip
										v-if="item.admin"
										:text="$t('Manager.MT4.Crud.Permissions.administrator')"
									/>

									<v-chip
										v-if="item.money"
										:text="$t('Manager.MT4.Crud.Permissions.accountant')"
									/>

									<v-chip
										v-if="item.reports"
										:text="$t('Manager.MT4.Crud.Permissions.reports')"
									/>

									<v-chip
										v-if="item.riskman"
										:text="$t('Manager.MT4.Crud.Permissions.risk-manager')"
									/>

									<v-chip
										v-if="item.email"
										:text="$t('Manager.MT4.Crud.Permissions.internal-mail-system')"
									/>

									<v-chip
										v-if="item.logs"
										:text="$t('Manager.MT4.Crud.Permissions.journals')"
									/>

									<v-chip
										v-if="item.news"
										:text="$t('Manager.MT4.Crud.Permissions.send-news')"
									/>

									<v-chip
										v-if="item.marketWatch"
										:text="$t('Manager.MT4.Crud.Permissions.market-watch')"
									/>

									<v-chip
										v-if="item.online"
										:text="$t('Manager.MT4.Crud.Permissions.connections-show-online-c')"
									/>

									<v-chip
										v-if="item.userDetails"
										:text="$t('Manager.MT4.Crud.Permissions.personal-details')"
									/>

									<v-chip
										v-if="item.plugins"
										:text="$t('Manager.MT4.Crud.Permissions.configure-server-plugins')"
									/>

									<v-chip
										v-if="item.serverReports"
										:text="$t('Manager.MT4.Crud.Permissions.automatic-server-reports')"
									/>

									<v-chip
										v-if="item.techSupport"
										:text="$t('Manager.MT4.Crud.Permissions.access-to-technical-support')"
									/>

									<v-chip
										v-if="item.market"
										:text="$t('Manager.MT4.Crud.Permissions.access-to-app-market')"
									/>

									<v-chip
										v-if="item.notifications"
										:text="$t('Manager.MT4.Crud.Permissions.push-notification')"
									/>
								</v-chip-group>
							</v-defaults-provider>

							<v-empty-state
								v-if="![
									item.manager,
									item.seeTrades,
									item.admin,
									item.money,
									item.reports,
									item.riskman,
									item.email,
									item.logs,
									item.news,
									item.marketWatch,
									item.online,
									item.userDetails,
									item.plugins,
									item.serverReports,
									item.techSupport,
									item.market,
									item.notifications,
								].includes(1)"
								title="This Manager Have No Rights"
								action-text="Add Rights"
								@click:action="crud?.update(item); crud?.openTab('permissions')"
							/>
						</template>
					</v-card>
				</td>
			</template>
			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('manager.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('manager.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<manager-crud-mt4
		ref="crud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type AccountsCrud from '@/components/Manager/Crud/Mt4/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import type { Manager } from '~/types'

const table = ref <InstanceType<typeof Datatable> | null>(null)

const crud = ref<InstanceType<typeof AccountsCrud> | null>(null)

const isClearingCache = ref(false)

const { t } = useI18n()

const headers: Headers = [
	{
		title: t('accounts.login'),
		value: 'login',
		nowrap: true,
	},
	{
		title: t('accounts.name'),
		value: 'name',
		nowrap: true,
	},
	{
		title: t('accounts.mailbox'),
		value: 'mailbox',
		nowrap: true,
	},
	{
		title: t('accounts.groups'),
		value: 'groups',
		nowrap: true,
	},
	{
		value: 'rights',
		key: 'data-table-expand',
	},
	{
		value: 'actions',
	},
]

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/accounts/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
	}).finally(() => {
		isClearingCache.value = false
	})
}

const { props, model: selectedModel } = useDatatableSelection<Manager.MT4._Id.GETResponse>()

const { copy } = useClipboard()

const { infoSnackbar, errorSnackbar } = useSnackbar()

const copyAsLogins = () => {
	const data = selectedModel.value.map((item: any) => item.login).join(',')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyAsRows = () => {
	const itemKeys = headers.map(header => header.value) as (keyof Manager.MT4._Id.GETResponse)[]

	const data = selectedModel.value.map(item =>
		itemKeys.map(key => item[key]).join(','),
	).join('\n')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyMenuItems = [
	{
		title: t('accounts.logins'),
		props: {
			onClick: copyAsLogins,
		},
	},
	{
		title: t('accounts.rows'),
		props: {
			onClick: copyAsRows,
		},
	},
]

const exportPermissions = () => {
	const { $api } = useNuxtApp()

	const server = useCurrentMtServer()

	$api<Blob>('/manager/mt4/export', { method: 'POST' })
		.then((excelFile) => {
			downloadFileFromApi(excelFile, `${server.value?.displayName} - Managers Permissions - ${new Date().toISOString()}.xlsx`)
		})
		.catch((e) => {
			errorSnackbar(e.message)
		})
}
</script>
