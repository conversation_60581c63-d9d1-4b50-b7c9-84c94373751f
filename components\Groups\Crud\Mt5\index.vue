<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Groups.MT5.Crud.group')"
		item-identity="group"
		:navigation-drawer-props="{ width: 800 }"
		url="/groups/mt5/:group"
		:validation-callback="validate"
		update-method="POST"
		update-url="/groups/mt5"
		v-bind="$attrs"
		@loaded="loadedGroup = $event.group"
		@closed="closeHandler"
	>
		<template #title="{ isUpdateAction, item, itemName }">
			<div v-if="isUpdateAction && item.group !== loadedGroup">
				{{ $t('Groups.MT5.Crud.create-item', [item.group, itemName]) }}
			</div>
			<div v-else-if="isUpdateAction">
				{{ $t('Groups.MT5.Crud.update-item', [item.group, itemName]) }}
			</div>
		</template>
		<template #default="{ item, errors }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-2 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class=""
						>
							<v-form
								v-for="tab in tabs"
								ref="tabForm"
								:key="tab.value"
								v-model="tab.isValid.value"
							>
								<v-tabs-window-item
									:value="tab.value"
									:eager="isValidated"
								>
									<component
										:is="tab.component"
										:loaded-group="loadedGroup"
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-tabs-window-item>
							</v-form>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'

import Common from './Common.vue'
import Company from './Company.vue'
import NewsAndMail from './NewsAndMail.vue'
import Permissions from './Permissions.vue'
import Margin from './Margin.vue'
import Symbols from './Symbols.vue'
import Commissions from './Commissions.vue'
import Reports from './Reports.vue'
import type { Groups } from '~/types'
import Crud from '~/components/Crud.vue'
import type { DefaultItem, OpenOptions } from '~/components/Crud.vue'

const loadedGroup = ref<string | null>(null)

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem<Groups.MT5._Id.POSTRequest | Groups.MT5._Id.PUTRequest> = {
	group: '',
	server: 1,
	permissionsFlags: 2,
	authMode: 0,
	authPasswordMin: 8,
	authOTPMode: 0,
	company: 'Ingot Brokers (Australia) Pty Ltd.',
	companyPage: '',
	companyEmail: '',
	companySupportPage: 'https://www.mql5.com/[lang:en|ru|es|pt|zh|ja|de|ko|fr|it|tr]',
	companySupportEmail: '',
	companyCatalog: '',
	companyDepositPage: '',
	companyWithdrawalPage: '',
	currency: 'USD',
	currencyDigits: 2,
	reportsMode: 0,
	reportsFlags: 0,
	reportsSMTP: '',
	reportsSMTPLogin: '',
	reportsSMTPPass: '',
	reportsEmail: '',
	newsMode: 2,
	newsCategory: '',
	newsLangs: [],
	mailMode: 1,
	tradeFlags: 23 as Groups.MT5.EnTradeFlags,
	tradeTransferMode: 0,
	tradeInterestrate: 0,
	tradeVirtualCredit: 0,
	marginMode: 0,
	marginFlags: 0,
	marginFreeMode: 1,
	marginSOMode: 0,
	marginCall: 50,
	marginStopOut: 30,
	marginFloatingLeverage: '',
	demoLeverage: 0,
	demoDeposit: 0,
	demoTradesClean: 0,
	limitHistory: 0,
	limitOrders: 0,
	limitSymbols: 0,
	limitPositions: 0,
	commissions: [],
	symbols: [
		// {
		// 	path: '*',
		// 	tradeMode: **********,
		// 	execMode: **********,
		// 	fillFlags: **********,
		// 	expirFlags: **********,
		// 	orderFlags: 0,
		// 	spreadDiff: 2147483647,
		// 	spreadDiffBalance: 2147483647,
		// 	stopsLevel: 2147483647,
		// 	freezeLevel: 2147483647,
		// 	volumeMin: 18446744073709551615,
		// 	volumeMinExt: 18446744073709551615,
		// 	volumeMax: 18446744073709551615,
		// 	volumeMaxExt: 18446744073709551615,
		// 	volumeStep: 18446744073709551615,
		// 	volumeStepExt: 18446744073709551615,
		// 	volumeLimit: 18446744073709551615,
		// 	volumeLimitExt: 18446744073709551615,
		// 	marginCheckMode: **********,
		// 	marginFlags: **********,
		// 	marginFlags: **********,
		// 	marginInitial: 1.7976931348623157e+308,
		// 	marginMaintenance: 1.7976931348623157e+308,
		// 	marginRateLiquidity: 1.7976931348623157e+308,
		// 	marginHedged: 1.7976931348623157e+308,
		// 	marginRateCurrency: 1.7976931348623157e+308,
		// 	marginLong: 0,
		// 	marginShort: 0,
		// 	marginLimit: 0,
		// 	marginStop: 0,
		// 	marginStopLimit: 0,
		// 	swapMode: 0,
		// 	swapShort: 1.7976931348623157e+308,
		// 	swapLong: 1.7976931348623157e+308,
		// 	swap3Day: 2147483647,
		// 	reFlags: **********,
		// 	reTimeout: **********,
		// 	ieFlags: **********,
		// 	ieCheckMode: **********,
		// 	ieTimeout: **********,
		// 	ieSlipProfit: **********,
		// 	ieSlipLosing: **********,
		// 	ieVolumeMax: 18446744073709551615n,
		// 	// ieVolumeMaxExt: 18446744073709551615,
		// 	permissionsFlags: 1,
		// 	bookDepthLimit: 0,
		// },
	],
	marginFreeProfitMode: 0,
} satisfies DefaultItem<Groups.MT5._Id.POSTRequest | Groups.MT5._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs: Tab[] = ([
	{
		value: 'common',
		text: t('Groups.MT5.Crud.common'),
		component: Common,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[0]?.errors.length || 0),
	},
	{
		value: 'company',
		text: t('Groups.MT5.Crud.company'),
		component: Company,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[1]?.errors.length || 0),
	},
	{
		value: 'news-and-mail',
		text: t('Groups.MT5.Crud.news-and-mail'),
		component: NewsAndMail,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[2]?.errors.length || 0),
	},
	{
		value: 'permissions',
		text: t('Groups.MT5.Crud.permissions'),
		component: Permissions,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[3]?.errors.length || 0),
	},
	{
		value: 'margin',
		text: t('Groups.MT5.Crud.margin'),
		component: Margin,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[4]?.errors.length || 0),
	},
	{
		value: 'symbols',
		text: t('Groups.MT5.Crud.symbols'),
		component: Symbols,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[5]?.errors.length || 0),
	},
	{
		value: 'commissions',
		text: t('Groups.MT5.Crud.commissions'),
		component: Commissions,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[6]?.errors.length || 0),
	},
	{
		value: 'reports',
		text: t('Groups.MT5.Crud.reports'),
		component: Reports,
		isValid: ref(true),
		errorsCount: computed(() => tabForm.value?.[7]?.errors.length || 0),
	},
])

const tabModel = ref(tabs[0].value)

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error('Please fix the form errors and try again'))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs[0].value

	loadedGroup.value = null
}

type Expose = {
	create: (item: any, options?: OpenOptions) => void
	update: (item: any, options?: OpenOptions) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: (item: any, options?: OpenOptions) =>	crudRef.value?.create(item, options),
	update: (item: any, options?: OpenOptions) => crudRef.value?.update(item, options),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
