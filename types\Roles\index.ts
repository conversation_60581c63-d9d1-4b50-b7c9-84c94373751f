export namespace Roles {
	export namespace _Id {
		export const URL = (_Id: number | string) => `/roles/${_Id}`
		export interface GETResponse {
			id: number
			name: string
			displayName: string
			description: string
			createdAt: Date
			permissions: number[]
		}

		export interface POSTRequest {
			displayName: string
			description: string
			permissions: number[]
		}
		export interface PUTRequest {
			displayName: string
			description: string
			permissions: number[]
		}

	}

	export namespace Lookup {

		export const URL = '/roles/lookup'

		export interface SingleRecord {
			id: number
			name: string
			displayName: string
			description: string
			createdAt: Date
			permissions: number[]
		}

		export type GETResponse = SingleRecord[]
	}

}
