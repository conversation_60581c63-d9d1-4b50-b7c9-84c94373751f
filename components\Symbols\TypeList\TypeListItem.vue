<template>
	<v-lazy min-height="40">
		<v-list-item
			:title="item.title"
			prepend-icon="i-material-symbols:group-work-outline"
			:value="item.value"
			@click="selectType(item)"
		>
			<!-- <template #prepend>
				<v-icon icon="i-mdi:folder text-yellow-darken-1" />
			</template> -->
		</v-list-item>
	</v-lazy>
</template>

<script lang="ts" setup>
import type { Symbols } from '~/types'

type Props = {
	item: Symbols.MT4.Type.Lookup.SingleRecord
}

type Emit = {
	(e: 'select', value: Symbols.MT4.Type.Lookup.SingleRecord): void
}

defineProps<Props>()

const emit = defineEmits<Emit>()

const selectType = (value: Symbols.MT4.Type.Lookup.SingleRecord) => {
	emit('select', value)
}
</script>
