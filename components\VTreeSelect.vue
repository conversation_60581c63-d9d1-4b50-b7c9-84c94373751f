<template>
	<v-text-field v-bind="computedTextFieldProps">
		<div
			v-if="p.chips && modelValueArray.length"
			class="d-flex flex-wrap ga-1"
		>
			<v-chip
				v-for="chip in modelValueArray"
				:key="`chip-${chip?.value || chip}`"
				size="small"
				:text="chip"
				:closable="p.closableChips"
				@click:close="closeChip(chip)"
			/>
		</div>
		<v-menu
			v-model="menuModel"
			activator="parent"
			open-on-focus
			open-on-click
			:close-on-content-click="false"
		>
			<v-card class="position-relative">
				<v-list
					class="position-sticky top-0"
					style="z-index: 1;"
				>
					<v-list-item>
						<v-text-field
							v-model="searchModel"
							hide-details
							variant="solo-filled"
							elevation="1"
							single-line
							density="compact"
							clearable
							placeholder="Search"
							append-inner-icon="i-mdi:magnify"
							autofocus
							flat
						/>
					</v-list-item>
					<v-list-item>
						<v-row no-gutters>
							<v-col>
								<v-btn
									block
									variant="text"
									@click="openAll = !openAll"
								>
									<template v-if="openAll">
										Close All
									</template>
									<template v-else>
										Open All
									</template>
								</v-btn>
							</v-col>
							<v-divider
								vertical
								class="mx-3"
							/>
							<v-col>
								<v-btn
									block
									variant="text"
									@click="openSelected"
								>
									Open Selected
								</v-btn>
							</v-col>
						</v-row>
					</v-list-item>
				</v-list>
				<v-divider class="mx-4" />
				<div
					v-if="!p.items?.length"
					class="text-center mb-4"
				>
					{{ $t('$vuetify.noDataText') }}
				</div>
				<v-treeview
					v-else
					v-model:selected="modelValueArray"
					v-model:opened="openedModel"
					:search="searchModel"
					:elevation="0"
					selectable
					:open-all="openAll"
					:items="(p.items as any[])"
					v-bind="p.treeProps"
				/>
			</v-card>
		</v-menu>
	</v-text-field>
</template>

<script lang="ts" setup>
import { VChip, VTextField } from 'vuetify/components'
import { VSelect } from 'vuetify/components/VSelect'
import { VTreeview } from 'vuetify/labs/VTreeview'

type VSelectProps = Omit<VSelect['$props'], '$children'>

type VTextFieldProps = Omit<VTextField['$props'], '$children'>

type VTreeviewProps = Omit<VTreeview['$props'], '$children'>

interface Props extends /* @vue-ignore */ VSelectProps {
	treeProps?: VTreeviewProps | undefined
}

defineOptions({
	extends: VSelect,
})

const p = defineProps<Props>()

const searchModel = defineModel<string>('search', {
	default: '',
})

const menuModel = ref(false)

const modelValue = defineModel<string | any[]>({
	default: '',
})

const modelValueArray = computed<any[]>({
	get() {
		if (Array.isArray(modelValue.value)) {
			return modelValue.value
		}
		return [modelValue.value]
	},
	set(v: any[]) {
		if (Array.isArray(modelValue.value)) {
			modelValue.value = v
		} else {
			modelValue.value = v[0]
		}
	},
})

const openedModel = defineModel<any[]>('opened', {
	default: () => [],
})

const openAll = ref(false)

const { $vuetify } = useNuxtApp()

const computedTextFieldProps = computed<VTextFieldProps>(() => {
	const active = !!modelValueArray.value.length

	return {
		...p,
		modelValue: p.chips ? (active || undefined) : modelValue.value,
		label: p.label,
		type: p.chips ? 'hidden' : undefined,
		offsetY: true,
		appendInnerIcon: `v-select__menu-icon ${$vuetify.icons.aliases.dropdown}`,
		class: {
			'v-tree-select': true,
			'v-select': true,
			'v-select--active-menu': !!menuModel.value,
			'chips': p.chips,
		},
		focused: menuModel.value,
		active,
	}
})

const openSelected = () => {
	const opened: any[] = []

	// Recursive function to traverse items and their children
	const loop = (items: typeof p.items) => {
		if (!items) {
			return
		}
		for (const item of items) {
			// Check if the item is in modelValue or if it has children in modelValue
			const isInModelValue = modelValueArray.value.includes(item.value)

			if (isInModelValue) {
				opened.push(item.value)
			}

			if (item.children) {
				// Recursive call for children
				loop(item.children)

				// If any child is open, the parent should also be considered open
				if (item.children.some((child: any) => opened.includes(child.value))) {
					if (!opened.includes(item.value)) {
						opened.push(item.value)
					}
				}
			}
		}
	}

	loop(p.items)

	openedModel.value = opened
}

// CAUSES ISSUE: reopens when Crud is closed
// watch(modelValue, (v) => {
// 	nextTick(() => {
// 		// make sure the component is not yet destroyed
// 		if (!v.length && menuModel) {
// 			menuModel.value = true
// 		}
// 	})
// })

watch(openAll, (v) => {
	if (!v) {
		openedModel.value = []
	}
})

const closeChip = (chip: any) => {
	if (Array.isArray(modelValue.value)) {
		if (typeof chip === 'object') {
			modelValue.value = modelValue.value.filter(v => v !== chip.value)
		} else {
			modelValue.value = modelValue.value.filter(v => v !== chip)
		}
	} else {
		modelValue.value = ''
	}
}
</script>

<style lang=scss>
.v-tree-select.chips{
	input{
		height: 1px;
		width: 1px;
		opacity: 0;
	}
}
</style>
