<template>
	<v-btn
		icon
		rounded="xl"
	>
		<v-icon icon="i-mdi:dots-vertical" />
		<v-menu
			activator="parent"
			:close-on-content-click="false"
			open-on-hover
		>
			<v-list
				density="compact"
				min-width="250"
			>
				<v-list-item density="compact">
					<v-list-item-title class="me-8">
						{{ $t('SettingsBtn.bak.dark-theme') }}
					</v-list-item-title>
					<template #append>
						<v-switch
							v-model="themeCookie"
							density="compact"
							color="success"
							true-value="dark"
							false-value="light"
							true-icon="i-mdi:weather-night"
							false-icon="i-mdi:weather-sunny"
							hide-details
							@update:model-value="themeHandler($event as Theme)"
						/>
					</template>
				</v-list-item>
				<v-list-item class="hidden-md-and-up">
					<v-list-item-title>
						{{ $t('SettingsBtn.bak.server') }}
					</v-list-item-title>
					<template #append>
						<api-items
							v-slot="props"
							url="/my/trading-servers"
						>
							<v-select
								v-bind="props"
								v-model="tradingServerName"
								density="compact"
								flat
								variant="solo"
								item-title="name"
								item-value="name"
								hide-details

								:menu-props="{ minWidth: 550 }"
							>
								<template #item="{ props: selectProps, item }">
									<v-list-item
										lines="two"
										v-bind="selectProps"
									>
										<v-list-item-subtitle>
											{{ item.raw.ipAddress }}:{{ item.raw.port }}
										</v-list-item-subtitle>
										<template #append>
											{{ item.raw.tradingPlatform?.name }}
										</template>
									</v-list-item>
								</template>
							</v-select>
						</api-items>
					</template>
				</v-list-item>
			</v-list>
		</v-menu>
	</v-btn>
</template>

<script lang="ts" setup>
const themeCookie = useThemeCookie()

const prefStore = usePreferencesStore()

// const { $vuetify } = useNuxtApp()

const themeHandler = (theme: Theme) => {
	prefStore.updateKey('theme', theme)
}

const tradingServerName = computed({
	get: () => prefStore.getKey('tradingServerName'),
	set: value => prefStore.updateKey('tradingServerName', value),
})
</script>
