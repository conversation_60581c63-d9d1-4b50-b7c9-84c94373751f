<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.faceValue"
					:label="$t('Symbols.MT5.Crud.Bonds.face-value')"
					type="number"
					hide-spin-buttons
					:error-messages="errors.faceValue"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.accruedInterest"
					:label="$t('Symbols.MT5.Crud.Bonds.accured-interest')"
					type="number"
					hide-spin-buttons
					:error-messages="errors.accruedInterest"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
// import { Symbols } from '~/types'

withDefaults(defineProps<Props>(), defaults)
</script>
