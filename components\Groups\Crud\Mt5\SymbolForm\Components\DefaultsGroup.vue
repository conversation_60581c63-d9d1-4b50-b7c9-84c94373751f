<template>
	<v-checkbox
		v-model="defaultModel"
		:label="label"
		color="primary"
		:true-value="true"
		:false-value="false"
		hide-details
		@update:model-value="updateDefaultHandler"
	/>
	<v-defaults-provider
		:defaults="{
			global: {
				disabled: defaultModel,
			},
		}"
	>
		<slot v-bind="{ model: symbol }" />
	</v-defaults-provider>
</template>

<script lang="ts" setup>
import pick from 'lodash/pick'
import { defaultSymbol } from '../Shared'
import type { Groups } from '~/types'

type Props = {
	keys: (keyof Omit<Groups.MT5.Symbol, 'path' | 'permissionsBookdepth' | 'permissionsFlags'>)[]
	label?: string
}
const p = withDefaults(defineProps<Props>(), {
	label: 'Use Default',
})

const defaultModel = defineModel<boolean>({ required: true })

const symbol = defineModel<Groups.MT5.Symbol>('symbol', { required: true })

const updateDefaultHandler = (isCheckedAsDefault: boolean | null) => {
	console.log('🚀 ~ updateDefaultHandler ~ isCheckedAsDefault:', isCheckedAsDefault)
}

onMounted(() => {
	if (symbol.value) {
		if (defaultModel.value === false) {
			return
		}
		const mergedValues = ({
			...symbol.value,
			...pick(defaultSymbol, p.keys),
		})

		Object.assign(symbol.value, mergedValues)
	}
})
</script>
