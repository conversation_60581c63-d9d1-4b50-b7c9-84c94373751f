<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('Symbols.MT5.Crud.symbol')"
		item-identity="symbol"
		:navigation-drawer-props="{ width: 900 }"
		:url="Symbols.MT5._Id.URL(':symbol')"
		:validation-callback="validate"
		update-method="POST"
		:update-url="Symbols.MT5._Id.URL('')"
		v-bind="$attrs"
		@loaded="loadedSymbol = $event.symbol"
		@closed="closeHandler"
	>
		<template #title="{ isUpdateAction, item, itemName }">
			<div v-if="isUpdateAction && item.symbol !== loadedSymbol">
				{{ $t('Symbols.MT5.Crud.create-item-symbol', [item.symbol, itemName]) }}
			</div>
			<div v-else-if="isUpdateAction">
				{{ $t('Symbols.MT5.Crud.update-item-symbol-itemna', [item.symbol, itemName]) }}
			</div>
		</template>
		<template #default="{ item, errors }">
			<v-defaults-provider :defaults="{ global: { density: 'comfortable' } }">
				<v-sheet class="d-flex">
					<v-tabs
						v-model="tabModel"
						center-active
						color="accent"
						:items="tabs"
						direction="vertical"
						class="flex-shrink-0 pe-2 border-e me-0"
					>
						<v-badge
							v-for="tab in tabs"
							:key="tab.value"
							color="error"
							:offset-y="8"
							:offset-x="8"
							bordered
							:content="tab.errorsCount?.value"
							:model-value="tab.isValid.value === false"
						>
							<v-tab
								class="flex-grow-1"
								:value="tab.value"
							>
								{{ tab.text }}
							</v-tab>
						</v-badge>
					</v-tabs>
					<v-sheet
						max-height="calc(100dvh - 132px)"
						class="flex-grow-1 overflow-y-auto"
					>
						<v-tabs-window
							v-model="tabModel"
							class=""
						>
							<v-tabs-window-item
								v-for="tab in tabs"
								:key="tab.value"
								:value="tab.value"
								:eager="isValidated"
							>
								<v-form
									ref="tabForm"
									v-model="tab.isValid.value"
								>
									<component
										:is="tab.component"
										:loaded-symbol="loadedSymbol"
										:item="item"
										:errors="errors"
										@update:item="updateHandler($event, item)"
									/>
								</v-form>
							</v-tabs-window-item>
						</v-tabs-window>
					</v-sheet>
				</v-sheet>
			</v-defaults-provider>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import Common from './Common.vue'
import Currency from './Currency.vue'
import Execution from './Execution.vue'
import Margin from './Margin.vue'
import MarginRates from './MarginRates.vue'
import Quotes from './Quotes.vue'
import Swaps from './Swaps.vue'
import Trade from './Trade.vue'
import Options from './Options.vue'
import Futures from './Futures.vue'
import Bonds from './Bonds.vue'
import Sessions from './Sessions.vue'

import { Symbols } from '~/types'
import Crud from '~/components/Crud.vue'
import type { DefaultItem } from '~/components/Crud.vue'

const loadedSymbol = ref<string | null>(null)

const tabForm = ref<InstanceType<typeof VForm>[] | null>(null)

const defaultItem: DefaultItem<Symbols.MT5._Id.POSTRequest | Symbols.MT5._Id.PUTRequest> = {
	symbol: '',
	path: '',
	isin: '',
	description: '',
	international: '',
	basis: '',
	source: '',
	page: '',
	currencyBase: '',
	currencyBaseDigits: 2,
	currencyProfit: '',
	currencyProfitDigits: 2,
	currencyMargin: '',
	currencyMarginDigits: 2,
	color: 4278190080, // unknown key
	colorBackground: 4278190080,
	digits: 4,
	point: 0.0001,
	multiply: 10000,
	tickFlags: Symbols.MT5.TickFlags.TICK_REALTIME,
	tickBookDepth: 0,
	tickChartMode: Symbols.MT5.ChartMode.CHART_MODE_BID_PRICE,
	filterSoft: 100,
	filterSoftTicks: 10,
	filterHard: 500,
	filterHardTicks: 10,
	filterDiscard: 500,
	filterSpreadMax: 0,
	filterSpreadMin: 0,
	filterGap: 0,
	filterGapTicks: 0,
	tradeMode: Symbols.MT5.TradeMode.TRADE_FULL,
	tradeFlags: Symbols.MT5.TradeFlags.ALLOW_SIGNALS,
	calcMode: Symbols.MT5.CalcMode.TRADE_MODE_FOREX,
	execMode: Symbols.MT5.ExecutionMode.EXECUTION_INSTANT,
	gtcMode: Symbols.MT5.GTCMode.ORDERS_GTC,
	fillFlags: Symbols.MT5.FillingFlags.FILL_FLAGS_FOK,
	orderFlags: Symbols.MT5.OrderFlags.ALL,
	expirFlags: Symbols.MT5.ExpirationFlags.TIME_FLAGS_ALL,
	spread: 0,
	spreadBalance: 0,
	spreadDiff: 0,
	spreadDiffBalance: 0,
	tickValue: 0,
	tickSize: 0,
	contractSize: 100000,
	stopsLevel: 5,
	freezeLevel: 0,
	quotesTimeout: 600,
	volumeMin: 0,
	volumeMinExt: 1,
	volumeMax: 100000000000,
	volumeMaxExt: 10000000000000000,
	volumeStep: 0,
	volumeStepExt: 1,
	volumeLimit: 0,
	volumeLimitExt: 0,
	// marginCheckMode: Symbols.MT5.MarginFlags.MARGIN_FLAGS_NONE,
	marginFlags: Symbols.MT5.MarginFlags.MARGIN_FLAGS_NONE,
	marginInitial: 0,
	marginInitialBuy: 1,
	marginInitialSell: 1,
	marginInitialBuyLimit: 0,
	marginInitialSellLimit: 0,
	marginInitialBuyStop: 0,
	marginInitialSellStop: 0,
	marginInitialBuyStopLimit: 0,
	marginInitialSellStopLimit: 0,
	marginMaintenance: 0,
	marginMaintenanceBuy: 0,
	marginMaintenanceSell: 0,
	marginMaintenanceBuyLimit: 0,
	marginMaintenanceSellLimit: 0,
	marginMaintenanceBuyStop: 0,
	marginMaintenanceSellStop: 0,
	marginMaintenanceBuyStopLimit: 0,
	marginMaintenanceSellStopLimit: 0,
	marginLiquidity: 0,
	marginHedged: 100000,
	marginCurrency: 0,
	// marginLong: 0,
	// marginShort: 0,
	// marginLimit: 0,
	// marginStop: 0,
	// marginStopLimit: 0,
	swapMode: Symbols.MT5.SwapMode.SWAP_DISABLED,
	swapLong: 0,
	swapShort: 0,
	swap3Day: 3,
	swapYearDay: 360,
	swapRateSunday: 0,
	swapRateMonday: 1,
	swapRateTuesday: 1,
	swapRateWednesday: 3,
	swapRateThursday: 1,
	swapRateFriday: 1,
	swapRateSaturday: 0,
	timeStart: 0,
	timeExpiration: 0,
	sessionsQuotes: [
		[],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[],
	],
	sessionsTrades: [
		[],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[
			{
				open: 0,
				openHours: 0,
				close: 1440,
				closeHours: 0,
			},
		],
		[],
	],
	reFlags: Symbols.MT5.RequestFlags.REQUEST_FLAGS_NONE,
	reTimeout: 7,
	ieCheckMode: Symbols.MT5.InstantMode.INSTANT_CHECK_NORMAL,
	ieTimeout: 5,
	ieSlipProfit: 2,
	ieSlipLosing: 2,
	ieVolumeMax: 0,
	ieVolumeMaxExt: 0,
	priceSettle: 0,
	priceLimitMax: 0,
	priceLimitMin: 0,
	priceStrike: 0,
	optionsMode: Symbols.MT5.OptionMode.EUROPEAN_CALL,
	faceValue: 0,
	accruedInterest: 0,
	spliceType: Symbols.MT5.SpliceType.NONE,
	spliceTimeType: Symbols.MT5.SpliceTimeType.EXPIRATION,
	spliceTimeDays: 0,
	ieFlags: Symbols.MT5.TradeInstantFlags.INSTANT_FLAGS_NONE,
	category: '',
	exchange: '',
	cfi: '',
	sector: Symbols.MT5.Sectors.SECTOR_UNDEFINED,
	industry: Symbols.MT5.Industries.UNDEFINED,
	country: '',
	subscriptionsDelay: 15,
	swapFlags: Symbols.MT5.SwapFlags.SWAP_FLAGS_NONE,
} satisfies DefaultItem<Symbols.MT5._Id.POSTRequest | Symbols.MT5._Id.PUTRequest>

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const tabModel = ref('common')

const isValidated = ref(false)

type Tab = {
	value: string
	text: string
	component: any
	isValid: Ref<boolean>
	errorsCount?: ComputedRef<number>
}

const { t } = useI18n()

const tabs = computed<Tab[]>(() => [
	{
		value: 'common',
		text: t('Symbols.MT5.Crud.common'),
		component: Common,
		isValid: ref(true),
	},
	{
		value: 'currency',
		text: t('Symbols.MT5.Crud.currency'),
		component: Currency,
		isValid: ref(true),
	},
	{
		value: 'quotes',
		text: t('Symbols.MT5.Crud.quotes'),
		component: Quotes,
		isValid: ref(true),
	},
	{
		value: 'trade',
		text: t('Symbols.MT5.Crud.trade'),
		component: Trade,
		isValid: ref(true),
	},
	{
		value: 'options',
		text: t('Symbols.MT5.Crud.options'),
		component: Options,
		isValid: ref(true),
	},
	{
		value: 'futures',
		text: t('Symbols.MT5.Crud.futures'),
		component: Futures,
		isValid: ref(true),
	},
	{
		value: 'bonds',
		text: t('Symbols.MT5.Crud.bonds'),
		component: Bonds,
		isValid: ref(true),
	},
	{
		value: 'execution',
		text: t('Symbols.MT5.Crud.execution'),
		component: Execution,
		isValid: ref(true),
	},
	{
		value: 'margin',
		text: t('Symbols.MT5.Crud.margin'),
		component: Margin,
		isValid: ref(true),
	},
	{
		value: 'margin-rates',
		text: t('Symbols.MT5.Crud.margin-rates'),
		component: MarginRates,
		isValid: ref(true),
	},
	{
		value: 'swaps',
		text: t('Symbols.MT5.Crud.swaps'),
		component: Swaps,
		isValid: ref(true),
	},
	{
		value: 'sessions',
		text: t('Symbols.MT5.Crud.sessions'),
		component: Sessions,
		isValid: ref(true),
	},
]
	.filter((tab) => {
		//
		const calcMode = (crudRef.value?.$defaultSlot.item as Symbols.MT5._Id.POSTRequest).calcMode
		if (tab.value === 'futures' && ![Symbols.MT5.CalcMode.TRADE_MODE_FUTURES, Symbols.MT5.CalcMode.TRADE_MODE_EXCH_FUTURES, Symbols.MT5.CalcMode.TRADE_MODE_EXCH_FUTURES_FORTS].includes(calcMode)) {
			return false
		}

		if (tab.value === 'options' && ![Symbols.MT5.CalcMode.TRADE_MODE_EXCH_OPTIONS, Symbols.MT5.CalcMode.TRADE_MODE_EXCH_OPTIONS].includes(calcMode)) {
			return false
		}

		if (tab.value === 'bonds' && ![Symbols.MT5.CalcMode.TRADE_MODE_EXCH_BONDS, Symbols.MT5.CalcMode.TRADE_MODE_EXCH_BONDS_MOEX].includes(calcMode)) {
			return false
		}
		return true
	})
	.map((tab: Tab, i) => {
		tab.errorsCount = computed(() => tabForm.value?.[i]?.errors.length || 0)
		return tab
	}))

const validate = () => {
	return new Promise<true>((resolve, reject) => {
		isValidated.value = true
		nextTick(async () => {
			const validationResults = []

			if (tabForm.value) {
				for (const form of tabForm.value) {
					const result = await form.validate()
					validationResults.push(result.valid)
				}
			}

			const isValid = validationResults.every(item => item)

			if (!isValid) {
				reject(new Error('Please fix the form errors and try again'))
			} else {
				resolve(true)
			}
		})
	})
}

const updateHandler = (newItem: any, item: any) => {
	for (const key in newItem) {
		item[key] = newItem[key]
	}
}

const closeHandler = () => {
	// isValidated.value = false

	// reset validation
	nextTick(() => {
		tabForm.value?.forEach(form => form.resetValidation())
		tabs.value.forEach(tab => (tab.isValid.value = true))
	})

	tabModel.value = tabs.value[0].value

	loadedSymbol.value = null
}

type Expose = {
	create: (item: DefaultItem<Partial<Symbols.MT5._Id.POSTRequest> | Partial<Symbols.MT5._Id.PUTRequest>> | undefined) => void
	update: (item: any) => void
	delete: (item: any) => void
}

defineExpose<Expose>({
	create: (item) => {
		if (item) {
			for (const key in item) {
				if (key in defaultItem) {
					(defaultItem as any)[key] = (item as any)[key]
				}
			}
		}

		crudRef.value?.create()
	},
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
