<template>
	<v-card
		height="100%"
		flat
		border
	>
		<v-card-title class="d-flex align-center">
			<div class="text-subtitle-1 text-wrap">
				Broker PnL (Hedging accounts)
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text>
			<v-list-item
				title="Daily"
				class="mb-2 bg-background"
			>
				<template #append>
					<v-fade-transition leave-absolute>
						<span
							:key="data.Daily"
							class="text-success text-no-wrap"
						>+{{ money(data.Daily) }}</span>
					</v-fade-transition>
				</template>
			</v-list-item>
			<v-list-item
				title="Weekly"
				class="mb-2 bg-background"
			>
				<template #append>
					<span class="text-success">+{{ money(data.Weekly) }}</span>
				</template>
			</v-list-item>
			<v-list-item
				title="Monthly"
				class="mb-2 bg-background"
			>
				<template #append>
					<span class="text-success">+{{ money(data.Monthly) }}</span>
				</template>
			</v-list-item>
		</v-card-text>
	</v-card>
</template>

<script lang="ts" setup>
import type { WidgetItemProps } from '../../types'
import type { SocketData } from '~/services/WebSocket/PnlWithHedging'
import { PnlWithHedging } from '~/services/WebSocket/PnlWithHedging'

type Props = WidgetItemProps & {}

const p = defineProps<Props>()

const pnlWithHedgingInstance = new PnlWithHedging()

const previewData = ref<SocketData>({
	Daily: 1234.21,
	Weekly: 3216.25,
	Monthly: 12588.78,
})

const realData = ref<SocketData>({} as SocketData)

const data = computed<SocketData>(() => {
	if (p.isPreview) {
		return previewData.value
	}
	return realData.value!
})

watch(() => p.isPreview, (isPreview) => {
	if (!isPreview) {
		pnlWithHedgingInstance.connect().onUpdate((data) => {
			realData.value = data
		})
	}
}, { immediate: true, once: true })

// onMounted(() => {
// 	setTimeout(() => {
// 		pnlWithHedgingInstance.connect().onUpdate((data) => {
// 			realData.value = data
// 		})
// 	}, 2000)
// })
</script>
