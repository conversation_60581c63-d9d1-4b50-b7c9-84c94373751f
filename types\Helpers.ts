import type { AllowedComponentProps, Component, VNodeProps } from 'vue'

export type ComponentProps<C extends Component> = C extends new (...args: any) => any
	? Omit<InstanceType<C>['$props'], keyof VNodeProps | keyof AllowedComponentProps | 'Symbol' | `$${string}`>
	: never

export type Nullable<T> = {
	[P in keyof T]: T[P] | null;
}

export type Defaultable<T> = {
	[P in keyof T]: T[P] | 'default';
}

export type UnionToIntersection<U> =
	(U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never

export type ElementOf<T extends any[]> = UnionToIntersection<T[number]>

export type CombinationOf<T> = T extends number ? number : never

export type Pagination<T, F = undefined> = {
	items: T[]
	total: number
	page: number
	pageSize: number
	hasNext: boolean
	hasPrevious: boolean
	filters?: F
}
