import type { Dayjs } from 'dayjs'

type AdapterType = 'date' | 'dayjs'

export const useNow = (adapter: AdapterType = 'date') => {
	const dayjs = useDayjs()
	const now = ref<Date | Dayjs>(adapter === 'date' ? new Date() : dayjs())

	const interval = setInterval(() => {
		now.value = adapter === 'date' ? new Date() : dayjs()
	}, 1000)

	onUnmounted(() => {
		clearInterval(interval)
	})

	return now
}
