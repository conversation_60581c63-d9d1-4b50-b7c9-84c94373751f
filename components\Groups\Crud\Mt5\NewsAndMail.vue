<template>
	<v-container>
		<v-sheet max-width="500">
			<v-select
				v-model="item.newsMode"
				:items="newsItems"
				:label="$t('Groups.MT5.Crud.NewsAndMail.news')"
			/>
			<v-combobox
				v-model="item.newsCategory"
				:items="['Trading Central\\Analyst Views']"
				:label="$t('Groups.MT5.Crud.NewsAndMail.news-categories')"
			/>
			<v-select
				v-model="item.newsLangs"
				multiple
				:items="newsLanguagesItems"
				:rules="rules({ type: 'array', max: 8 })"
				:label="$t('Groups.MT5.Crud.NewsAndMail.news-languages')"
				chips
				closable-chips
			/>
			<v-checkbox
				v-model="item.mailMode"
				:true-value="Groups.MT5.EnMailMode.MAIL_MODE_FULL"
				:false-value="Groups.MT5.EnMailMode.MAIL_MODE_DISABLED"
				color="primary"
				:label="$t('Groups.MT5.Crud.NewsAndMail.enable-internal-mail-syst')"
			/>
		</v-sheet>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const newsItems = enumToItems(Groups.MT5.EnNewsMode, 'Groups.MT5.EnNewsMode')

const newsLanguagesItems = enumToItems(Groups.MT4.NewsLanguage, 'Groups.MT4.NewsLanguage')
</script>
