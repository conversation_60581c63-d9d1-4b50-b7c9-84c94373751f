<template>
	<page :title="$t('prices.historical-prices')">
		<template #actions>
			<v-btn
				download
				color="primary"
				prepend-icon="i-mdi:download"
				href="/historical-prices-template.xlsx"
			>
				{{ $t('prices.download-template') }}
			</v-btn>
		</template>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<div class="text-subtitle-2 ">
					{{ $t('prices.how-to-get-the-historical-prices-of-a-stock') }}
					<ol class="instructions-list">
						<li>{{ $t('prices.download-the-template-file-from-upper-right-corner') }}</li>
						<li>{{ $t('prices.fill-the-template-with-the-stock-symbols-you-want-') }}</li>
						<li>{{ $t('prices.upload-the-filled-template-file') }}</li>
						<li>{{ $t('prices.you-will-be-redirected-to-the-report-page') }}</li>
					</ol>
				</div>
			</v-col>
			<v-col>
				<drop-zone
					v-model="uploadedFile"
					:loading="isLoading"
					:data-types="['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']"
					@update:model-value="uploadExcelTemplate"
				/>
			</v-col>
		</v-row>
	</page>
</template>

<script lang="ts" setup>
import type { Prices } from '~/types'

definePageMeta({
	permission: 'reports.prices',
})

const uploadedFile = ref<File | null>(null)

const { $api } = useNuxtApp()

const { errorSnackbar } = useSnackbar()

const router = useRouter()

const isLoading = ref(false)

const uploadExcelTemplate = (files: any) => {
	const formData = new FormData()
	if (files.length) {
		formData.append('file', files[0])
		isLoading.value = true
		$api<Prices.Generate.POSTResponse>('/report/prices', {
			method: 'POST',
			body: formData,
			timeout: 0,
		}).then((res) => {
			router.push({ name: 'reports-prices-id', params: { id: res.id } })
		}).catch((err) => {
			errorSnackbar(err.message)
		}).finally(() => {
			isLoading.value = false
		})
	}
}
</script>

<style scoped lang="scss">
	.instructions-list{
		list-style-type: decimal;
		margin-inline-start: 20px;
		li{
			margin-block: 12px;
			color: rgba(var(--v-theme-on-background), var(--v-medium-emphasis-opacity)) !important;
		}
	}
</style>
