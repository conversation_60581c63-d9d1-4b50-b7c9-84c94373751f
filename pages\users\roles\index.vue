<template>
	<div>
		<page :title="$t('roles.roles')">
			<template #actions>
				<v-btn
					v-if="can('roles.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('roles.create-role') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/roles"
				:headers="headers"
				search-key="name"
			>
				<template #item.avatar="{ value }">
					<v-avatar :image="value" />
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('roles.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('roles.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<roles-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type RolesCrud from '~/components/RolesCrud.vue'

definePageMeta({
	permission: 'roles.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof RolesCrud | null>(null)
const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	// {
	// 	title: 'Name',
	// 	value: 'name',
	// },
	{
		title: t('roles.display-name'),
		value: 'displayName',
	},
	{
		title: t('roles.description'),
		value: 'description',
	},
	{
		title: t('roles.date'),
		value: 'timestamp',
	},
	{
		// title: 'Actions',
		value: 'actions',
	},

]
</script>
