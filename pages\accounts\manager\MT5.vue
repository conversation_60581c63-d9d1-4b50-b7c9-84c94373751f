<template>
	<page :title="$t('accounts.manager')">
		<template #actions>
			<v-btn
				variant="text"
				prepend-icon="i-mdi:export-variant"
				color="primary"
				@click="exportPermissions"
			>
				Export
			</v-btn>
			<v-btn
				v-if="can('manager.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="managerCrud?.create()"
			>
				{{ $t('accounts.create-manger') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/manager/mt5"
			:headers="headers"
			search-key="search"
			item-value="login"
			:default-model="{ name: '' }"
			v-bind="props"
		>
			<!-- <template #filter="{model}">
				<api-items v-slot="props" :url="Groups.Lookup.URL">
					<v-autocomplete v-model="model.group" :label="$t('accounts.group')" v-bind="props" clearable />
				</api-items>
			</template> -->
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:loading="isClearingCache"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>

			<template #toolbar.middle-end>
				<v-btn
					v-if="selectedModel.length"
					prepend-icon="i-mdi:content-copy"
				>
					{{ $t('accounts.copy-as') }}
					<v-menu
						activator="parent"
						offset-y
					>
						<v-list
							:items="copyMenuItems"
							density="compact"
						/>
					</v-menu>
				</v-btn>
			</template>

			<template #item.login="{ value, item }">
				<manager-icon
					platform="MT5"
					:manager="item"
					start
				/>
				{{ value }}
			</template>
			<template #item.name="{ value, item }">
				<accounts-mt5-info-tooltip
					:login="item.login"
					:create="!value"
					@update="accountsCrud?.update"
					@create="accountsCrud?.create({}, { override: $event })"
				>
					{{ value || 'N/A' }}
				</accounts-mt5-info-tooltip>
			</template>
			<template #item.groups="{ value, item }">
				<v-chip-group column>
					<v-chip
						v-for="group in value"
						:key="item.login+'-'+group"
						density="compact"
						color="primary"
						:text="group"
					/>
				</v-chip-group>
			</template>
			<!-- <template #item.data-table-expand="{toggleExpand,internalItem,isExpanded}">
				<v-btn :prepend-icon="`i-mdi:chevron-${isExpanded(internalItem)? 'up':'down'}`" variant="text" size="small" @click="toggleExpand(internalItem)">
					{{ isExpanded(internalItem)? t('accounts.hide-rights') : t('accounts.show-rights') }}
				</v-btn>
			</template> -->

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('manager.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="managerCrud?.update(item)"
					/>
					<v-btn
						v-if="can('manager.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="managerCrud?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<manager-crud-mt5
		ref="managerCrud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
	<accounts-crud-mt5
		ref="accountsCrud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type ManagerCrud from '@/components/Manager/Crud/Mt5/index.vue'
import type AccountsCrud from '@/components/Accounts/Crud/Mt5/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import type { Manager } from '~/types'

const table = ref <InstanceType<typeof Datatable> | null>(null)

const managerCrud = ref<InstanceType<typeof ManagerCrud> | null>(null)

const accountsCrud = ref<InstanceType<typeof AccountsCrud> | null>(null)

const isClearingCache = ref(false)

const { t } = useI18n()

const headers: Headers = [
	{
		title: t('accounts.login'),
		value: 'login',
		nowrap: true,
	},
	{
		title: t('accounts.name'),
		value: 'name',
		nowrap: true,
	},
	{
		title: t('accounts.mailbox'),
		value: 'mailbox',
		nowrap: true,
	},
	{
		title: t('accounts.groups'),
		value: 'groups',
		nowrap: true,
	},
	{
		value: 'actions',
	},
]

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/accounts/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
	}).finally(() => {
		isClearingCache.value = false
	})
}

const { props, model: selectedModel } = useDatatableSelection<Manager.MT5._Id.GETResponse>()

const { copy } = useClipboard()

const { infoSnackbar, errorSnackbar } = useSnackbar()

const copyAsLogins = () => {
	const data = selectedModel.value.map((item: any) => item.login).join(',')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyAsRows = () => {
	const itemKeys = headers.map(header => header.value) as (keyof Manager.MT5._Id.GETResponse)[]

	const data = selectedModel.value.map(item =>
		itemKeys.map(key => item[key]).join(','),
	).join('\n')

	copy(data)

	selectedModel.value = []

	infoSnackbar(t('accounts.copied'))
}

const copyMenuItems = [
	{
		title: t('accounts.logins'),
		props: {
			onClick: copyAsLogins,
		},
	},
	{
		title: t('accounts.rows'),
		props: {
			onClick: copyAsRows,
		},
	},
]

const exportPermissions = () => {
	const { $api } = useNuxtApp()

	const server = useCurrentMtServer()

	$api<Blob>('/manager/mt5/export', { method: 'POST' })
		.then((excelFile) => {
			downloadFileFromApi(excelFile, `${server.value?.displayName} - Managers Permissions - ${new Date().toISOString()}.xlsx`)
		})
		.catch((e) => {
			errorSnackbar(e.message)
		})
}
</script>
