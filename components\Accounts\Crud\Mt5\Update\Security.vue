<template>
	<v-container>
		<fieldset>
			<legend>
				{{ $t('Accounts.MT5.Crud.master-password') }}
			</legend>

			<p class="text-medium-emphasis mb-4">
				{{ $t('Accounts.MT5.Crud.master-password-is-used-for') }}
			</p>

			<v-form
				v-slot="{ isValid }"
				autocomplete="off"
			>
				<v-row
					dense
					align="stretch"
				>
					<v-col
						cols="12"
						md="6"
					>
						<password
							v-model="passwordModel.masterPassword.value"
							v-model:strength="passwordModel.masterPassword.strength"
							show-strength
							autocomplete="new-password"
							:rules="rules({ required: true })"
							:error-messages="passwordModel.masterPassword.errors"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value"
							:loading="passwordModel.masterPassword.isChecking"
							@click="checkPassword('masterPassword')"
						>
							{{ $t('Accounts.MT5.Crud.check') }}
						</v-btn>

						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value || passwordModel.masterPassword.strength < 100"
							:loading="passwordModel.masterPassword.isChanging"
							@click="changePassword('masterPassword')"
						>
							{{ $t('Accounts.MT5.Crud.change') }}
						</v-btn>
						<!-- <v-menu :disabled="!isValid.value">
							<template #activator="{props}">
								<v-btn variant="text" icon="i-mdi-dots-vertical" v-bind="props" :loading="passwordModel.masterPassword.isChanging" />
							</template>.value
							<v-list>
								<v-list-item title="Change Password" @click="changePassword('masterPassword')" />
							</v-list>
						</v-menu> -->
					</v-col>
				</v-row>
			</v-form>
		</fieldset>

		<fieldset>
			<legend>
				{{ $t('Accounts.MT5.Crud.investor-password') }}
			</legend>

			<p class="text-medium-emphasis mb-4">
				{{ $t('Accounts.MT5.Crud.investor-password-is-used-for') }}
			</p>

			<v-form v-slot="{ isValid }">
				<v-row
					dense
					align="stretch"
				>
					<v-col
						cols="12"
						md="6"
					>
						<password
							v-model="passwordModel.investorPassword.value"
							v-model:strength="passwordModel.investorPassword.strength"
							autocomplete="new-password"
							show-strength
							:rules="rules({ required: true })"
							:error-messages="passwordModel.investorPassword.errors"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value"
							:loading="passwordModel.investorPassword.isChecking"
							@click="checkPassword('investorPassword')"
						>
							{{ $t('Accounts.MT5.Crud.check') }}
						</v-btn>

						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value || passwordModel.investorPassword.strength < 100"
							:loading="passwordModel.investorPassword.isChanging"
							@click="changePassword('investorPassword')"
						>
							{{ $t('Accounts.MT5.Crud.change') }}
						</v-btn>
					</v-col>
				</v-row>
			</v-form>
		</fieldset>

		<fieldset class="mb-4">
			<legend>
				{{ $t('Accounts.MT5.Crud.api-password') }}
			</legend>

			<p class="text-medium-emphasis mb-4">
				{{ $t('Accounts.MT5.Crud.api-password-is-used-for') }}
			</p>

			<v-form v-slot="{ isValid }">
				<v-row
					dense
					align="stretch"
				>
					<v-col
						cols="12"
						md="6"
					>
						<password
							v-model="passwordModel.apiPassword.value"
							v-model:strength="passwordModel.apiPassword.strength"
							autocomplete="new-password"
							show-strength
							:rules="rules({ required: true })"
							:error-messages="passwordModel.apiPassword.errors"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value"
							:loading="passwordModel.apiPassword.isChecking"
							@click="checkPassword('apiPassword')"
						>
							{{ $t('Accounts.MT5.Crud.check') }}
						</v-btn>

						<v-btn
							variant="text"
							height="48"
							:disabled="!isValid.value || passwordModel.apiPassword.strength < 100"
							:loading="passwordModel.apiPassword.isChanging"
							@click="changePassword('apiPassword')"
						>
							{{ $t('Accounts.MT5.Crud.change') }}
						</v-btn>
					</v-col>
				</v-row>
			</v-form>
		</fieldset>
		<fieldset>
			<legend>
				{{ $t('Accounts.MT5.Crud.phone-password-and-otp') }}
			</legend>
			<v-row dense>
				<v-col
					cols="12"
					md="6"
				>
					<password
						v-model="item.phonePassword"
						:label="$t('Accounts.MT5.Crud.phone-password')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<password
						v-model="item.phonePassword"
						:label="$t('Accounts.MT5.Crud.otp-secret-key')"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import type { FetchError } from 'ofetch'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const { t } = useI18n()

const { $api } = useNuxtApp()

const { successSnackbar } = useSnackbar()

type Model = {
	value: string
	errors: string | string[]
	strength: number
	type: Accounts.MT5.EnUsersPasswords
	isChecking: boolean
	isChanging: boolean
}

type Variance = {
	masterPassword: Model
	investorPassword: Model
	apiPassword: Model
}

const passwordModel = reactive<Variance>({
	masterPassword: {
		value: '',
		errors: [],
		strength: 0,
		type: Accounts.MT5.EnUsersPasswords.USER_PASS_MAIN,
		isChecking: false,
		isChanging: false,
	},
	investorPassword: {
		value: '',
		errors: [],
		strength: 0,
		type: Accounts.MT5.EnUsersPasswords.USER_PASS_INVESTOR,
		isChecking: false,
		isChanging: false,
	},
	apiPassword: {
		value: '',
		errors: [],
		strength: 0,
		type: Accounts.MT5.EnUsersPasswords.USER_PASS_API,
		isChecking: false,
		isChanging: false,
	},
})

const checkPassword = async (key: keyof Variance) => {
	if (passwordModel[key].isChecking) {
		return
	}

	passwordModel[key].errors = []

	passwordModel[key].isChecking = true

	const body: Accounts.MT5.CheckPassword.POSTRequest = {
		login: p.item.login,
		password: passwordModel[key].value,
		passwordType: passwordModel[key].type,
	}

	await $api(Accounts.MT5.CheckPassword.URL, {
		method: 'POST',
		body,
	})
		.then(() => {
			successSnackbar(t('Accounts.MT4.Crud.Security.password-verified'))
		})
		.catch((e: FetchError) => {
			if (e.statusCode === 400) {
				passwordModel[key].errors = [t('Accounts.MT5.Crud.Security.password-incorrect')]
			} else if (e.message) {
				passwordModel[key].errors = [e.message]
			}

			const unwatch = watch(() => passwordModel[key].value, () => {
				passwordModel[key].errors = []
				unwatch()
			})
		})
		.finally(() => {
			passwordModel[key].isChecking = false
		})
}

const changePassword = async (key: keyof Variance) => {
	if (passwordModel[key].isChanging) {
		return
	}

	const { errors, handler: checkPasswordHandler } = useFormHandler({ [key]: passwordModel[key].value })

	passwordModel[key].errors = []

	passwordModel[key].isChanging = true

	const body: Accounts.MT5.ChangePassword.POSTRequest = {
		login: p.item.login,
		password: passwordModel[key].value,
		passwordType: passwordModel[key].type,
	}

	await $api(Accounts.MT5.ChangePassword.URL, {
		method: 'POST',
		body,
	})
		.then(() => {
			successSnackbar(t('Accounts.MT4.Crud.Security.password-changed'))
		})
		.catch((e) => {
			checkPasswordHandler(e)
			if (errors[key]) {
				passwordModel[key].errors = errors[key]
			}
		})
		.finally(() => {
			passwordModel[key].isChanging = false
		})
}
</script>
