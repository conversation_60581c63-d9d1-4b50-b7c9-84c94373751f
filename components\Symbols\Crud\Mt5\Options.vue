<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="optionType"
					:label="$t('Symbols.MT5.Crud.Options.option-type')"
					:items="optionsTypeItems"
					:error-messages="errors.optionsMode"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="optionStyle"
					:label="$t('Symbols.MT5.Crud.Options.option-style')"
					:items="optionsStyleItems"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<v-text-field
					v-model.number="item.priceStrike"
					:label="$t('Symbols.MT5.Crud.Options.strike-price')"
					:error-messages="errors.spliceTimeDays"
				/>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Symbols } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const optionsTypeItems = [
	{ title: 'European', value: 0 },
	{ title: 'American', value: 1 },
]

const optionsStyleItems = [
	{ title: 'Call', value: 0 },
	{ title: 'Put', value: 1 },
]

const optionType = ref(0)

const optionStyle = ref(0)

watch(() => p.item.optionsMode, (value) => {
	switch (value) {
		case Symbols.MT5.OptionMode.EUROPEAN_CALL:
			optionType.value = 0
			optionStyle.value = 0
			break
		case Symbols.MT5.OptionMode.EUROPEAN_PUT:
			optionType.value = 0
			optionStyle.value = 1
			break
		case Symbols.MT5.OptionMode.AMERICAN_CALL:
			optionType.value = 1
			optionStyle.value = 0
			break
		case Symbols.MT5.OptionMode.AMERICAN_PUT:
			optionType.value = 1
			optionStyle.value = 1
			break
	}
}, {
	immediate: true,
})

watch(() => [optionType.value, optionStyle.value], ([type, style]) => {
	let optionsMode: Symbols.MT5.OptionMode = 0
	switch (type) {
		case 0: // European
			switch (style) {
				case 0: // Call
					optionsMode = Symbols.MT5.OptionMode.EUROPEAN_CALL
					break
				case 1: // Put
					optionsMode = Symbols.MT5.OptionMode.EUROPEAN_PUT
					break
			}
			break
		case 1: // American
			switch (style) {
				case 0:
					optionsMode = Symbols.MT5.OptionMode.AMERICAN_CALL
					break
				case 1:
					optionsMode = Symbols.MT5.OptionMode.AMERICAN_PUT
					break
			}
			break
	}

	emit('update:item', {
		optionsMode,
	})
})
</script>
