<template>
	<v-skeleton-loader
		:loading="pending"
		type="list-item-two-line@4"
		width="100%"
		min-width="372"
	>
		<v-timeline
			align="start"
			side="end"
			:truncate-line="truncateLine"
			class="flex-grow-1"
		>
			<workflow-event
				v-for="item, i in items"
				:key="item.id"
				:item="item"
				:prev-event="i > 0 ? items[i - 1]: undefined"
			/>
		</v-timeline>
	</v-skeleton-loader>
</template>

<script lang="ts" setup>
import { Transactions } from '~/types/Transactions'

type Props = {
	id: number
}
const p = withDefaults(defineProps<Props>(), {

})

const { data, pending, refresh } = useApi<Transactions._Id.Workflow.GETResponse>(Transactions._Id.Workflow.URL(p.id))

const items = computed(() => {
	if (!data.value) {
		return []
	}
	return data.value
})

const hasEnded = computed(() => {
	// check if last item has status > 0 and items.length > 1
	if (items.value.length > 1) {
		return items.value[items.value.length - 1].status > 0
	}

	return false
})

const truncateLine = computed(() => {
	if (hasEnded.value) {
		return 'both'
	}

	return 'start'
})

watch(() => p.id, () => {
	refresh()
})
</script>
