<template>
	<div class="position-relative">
		<div class="position-sticky top-0 z-1">
			<v-toolbar
				density="compact"
				color="surface"
			>
				<v-toolbar-title class="text-subtitle-1 font-weight-medium">
					{{ $t('symbols.path') }}
				</v-toolbar-title>
				<v-spacer />
				<v-btn
					v-tooltip:top="$t('symbols.expand-all')"
					icon="i-mdi:expand-all"
					class="ms-1"
					size="small"
					@click="expandAll"
				/>
				<v-btn
					v-tooltip:top="$t('symbols.collapse-all')"
					icon="i-mdi:collapse-all"
					class="ms-1"
					size="small"
					@click="collapseAll"
				/>
				<v-btn
					v-if="can('symbol.create')"
					v-tooltip:top="$t('symbols.add-root-directory')"
					icon="i-mdi:folder-plus-outline"
					class="ms-1"
					size="small"
					@click="addDirHandler({ value: '' } as Path)"
				/>
			</v-toolbar>
			<!-- <v-divider  /> -->
			<div
				class="mx-2"
			>
				<v-progress-linear
					:indeterminate="status === 'pending' && hasData"
					:active="true"
					height="2"
				/>
			</div>
		</div>
		<v-list
			v-model:opened="openedModel"
			class="position-relative pt-0"
			width="100%"
			slim
			density="compact"
		>
			<!-- <template #prepend> -->
			<v-skeleton-loader
				type="list-item@10"
				:loading="status === 'pending' && !hasData"
			>
				<div class="flex-grow-1">
					<PathTreeItem
						v-for="item in computedPaths"
						:key="item.value"
						:item="item"
						@path-select="pathSelectHandler"
						@add-dir="addDirHandler"
						@remove-dir="removeDirHandler"
					/>
					<!-- <div class="show-on-hover-itemg text-caption">
						<v-divider class="ma-2" />

					</div> -->
					<div v-if="!hasPaths">
						<v-empty-state :text="$t('symbols.no-path-found', [searchPathModel])">
							<template #media>
								<v-icon
									class="mb-3"
									size="x-large"
									icon="i-mdi:folder-off-outline"
								/>
							</template>
						</v-empty-state>
					</div>
				</div>
			</v-skeleton-loader>
		</v-list>
		<v-dialog
			v-model="addDirModel"
			max-width="400"
			@update:model-value="closeAddDir"
		>
			<v-form
				v-slot="{ isValid }"
				@submit.prevent="saveDir"
			>
				<v-card :title="$t('symbols.add-directory')">
					<template #text>
						<div class="mb-2" />
						<v-text-field
							v-model.trim="dirNameModel"
							autofocus
							:prefix="currentAddDirItem?.value ? `${currentAddDirItem.value}\\` : ''"
							:placeholder="$t('symbols.directory-name')"
							density="comfortable"
							persistent-placeholder
							single-line
							:rules="rules({ required: true, whitespace: true })"
						/>
					</template>
					<template #actions="">
						<v-spacer />
						<v-btn @click="closeAddDir(false)">
							{{ $t('symbols.cancel') }}
						</v-btn>
						<v-btn
							:disabled="!isValid.value"
							:loading="isSaving"
							@click="saveDir()"
						>
							{{ $t('symbols.save') }}
						</v-btn>
					</template>
				</v-card>
			</v-form>
		</v-dialog>
	</div>
</template>

<script lang="ts" setup>
// import { VTreeview } from 'vuetify/labs/VTreeview'
import { VTextField } from 'vuetify/components'
import type { FetchError } from 'ofetch'
import PathTreeItem from './PathTreeItem.vue'
import { Symbols } from '~/types'

defineOptions({
	inheritAttrs: false,
})

const { t } = useI18n()

type Path = Symbols.MT5.Paths.Lookup.SingleRecord

type Emit = {
	(e: 'loaded', value: Symbols.MT5.Paths.Lookup.GETResponse): void
	(e: 'deleted', value: string): void
}

const emit = defineEmits<Emit>()

const model = defineModel('modelValue', {
	type: String,
	default: '',
})

const searchPathModel = defineModel('search', {
	type: String,
	default: '',
})

const extractPathArray = (path: string) => {
	const parts = path.split('\\')
	return parts.reduce((acc, part) => {
		// only add \\ if not first one
		const path = (acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part
		acc.push(path)
		return acc
	}, [''])
		.filter(part => !!part)
}

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const paths = ref<Path[]>([])

const openedModel = defineModel('opened', {
	type: Array,
	default: [],
})

openedModel.value = extractPathArray(model.value)

const menuModel = ref(false)

const currentAddDirItem = ref<Path | null>(null)

const addDirModel = ref(false)

const dirNameModel = ref('')

const isSaving = ref(false)

const { data, status, refresh } = useApi<Symbols.MT5.Paths.Lookup.GETResponse>(Symbols.MT5.Paths.Lookup.URL, {
	key: 'pathTree',
})

watch(() => data.value, (value) => {
	if (value) {
		paths.value = value
		emit('loaded', value)
	}
})

watch(() => model.value, (value) => {
	openedModel.value = extractPathArray(value)
})

const computedPaths = computed(() => {
	if (!debouncedSearchPathModel.value) {
		return paths.value
	}
	// filter the data based on the debouncedSearchPathModel model, find the item.title recursively in all children and keep the parent folder
	const filterData = (pathsData: Symbols.MT5.Paths.Lookup.GETResponse, search: string) => {
		return pathsData.slice().filter((item) => {
			if (item.children.length) {
				const children = filterData(item.children, search)
				if (children.length) {
					item.children = children
					return true
				}
			}
			return item.title.toLowerCase().includes(search.toLowerCase())
		})
	}
	return filterData(paths.value, debouncedSearchPathModel.value)
})

const hasPaths = computed(() => !!computedPaths.value.length)

const pathSelectHandler = (path: string) => {
	model.value = path
	menuModel.value = false
}

const addDirHandler = (item: Path) => {
	currentAddDirItem.value = item
	addDirModel.value = true
}

const removeDirHandler = (item: Path) => {
	const { $api, $confirm } = useNuxtApp()

	const { errorSnackbar } = useSnackbar()

	// const {} =

	$confirm({
		async: true,
		text: t('symbols.are-you-sure-you-want-to-delete', [item.totalItems]),
	})
		.then(({ loading, hide }) => {
			loading.value = true
			$api(Symbols.MT5.Paths.Delete.URL(`${item.value}`), {
				method: 'DELETE',
			}).then(() => {
				refresh()
				emit('deleted', item.value)
			})
				.catch((e: FetchError) => {
					errorSnackbar(e.message || t('symbols.failed-to-delete-directory'))
				})
				.finally(() => {
					loading.value = false
					hide()
				})
		})
		.catch(() => {})
}

const saveDir = () => {
	if (!currentAddDirItem.value) {
		return
	}
	const parentPath = currentAddDirItem.value.value
	const dirName = dirNameModel.value
	const path = `${parentPath}\\${dirName}`

	isSaving.value = true

	const { $api } = useNuxtApp()

	$api(Symbols.MT5.Paths.Create.URL, {
		method: 'POST',
		body: {
			path,
		},
	}).then(() => {
		refresh()
		pathSelectHandler(path)
		closeAddDir(false)
	}).finally(() => {
		isSaving.value = false
	})

	// find the correct location and push the item to the paths array
	// const findAndPush = (node: Path) => {
	// 	if (node.value === parentPath) {
	// 		node.children.push({
	// 			title: dirName,
	// 			value: path,
	// 			children: [],
	// 			totalItems: 0,
	// 			// temporary: true,
	// 		})
	// 		return
	// 	}

	// 	if (node.children && node.children.length > 0) {
	// 		node.children.forEach(child => findAndPush(child))
	// 	}
	// }

	// paths.value.forEach(item => findAndPush(item))
	// // if the path is level1\level2\level3, then open all nodes by pushing level1 & level1\level2 & level1\level2\level3
	// const pathParts = path.split('\\')
	// pathParts.reduce((acc, part) => {
	// 	// only add \\ if not first one

	// 	acc.push((acc[acc.length - 1] ? acc[acc.length - 1] + '\\' : acc[acc.length - 1]) + part)
	// 	return acc
	// }, ['']).forEach((part) => {
	// 	if (part && !openedModel.value.includes(part)) {
	// 		openedModel.value.unshift(part)
	// 	}
	// })
}

const closeAddDir = (value: boolean) => {
	if (value) {
		return
	}
	addDirModel.value = false
	dirNameModel.value = ''
}

const expandAll = () => {
	openedModel.value = paths.value.reduce((acc: string[], item) => {
		acc.push(item.value)
		if (item.children.length) {
			const expandChildren = (children: Path[]) => {
				children.forEach((child) => {
					acc.push(child.value)
					if (child.children.length) {
						expandChildren(child.children)
					}
				})
			}
			expandChildren(item.children)
		}
		return acc
	}, [])
}

const collapseAll = () => {
	openedModel.value = []
}

const hasData = computed(() => computedPaths.value.length !== 0)

type Exposed = {
	extractPathArray: typeof extractPathArray
	refresh: typeof refresh
}

defineExpose<Exposed>({
	extractPathArray,
	refresh,
})
</script>
