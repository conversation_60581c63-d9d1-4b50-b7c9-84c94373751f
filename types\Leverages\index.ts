export namespace Leverages {

	// export namespace _Id {

	// 	export const URL = (_Id:number|string) => `/leverages/${_Id}`

	// 	type BaseGETResponse = {
	// 		id: number;
	// 		name: string;
	// 		displayName: string;
	// 		webLogin: number;
	// 		webPassword?: string | undefined;
	// 		tradingPlatformId: number;
	// 		tradingPlatform: TradingPlatform;
	// 		isActive: boolean;
	// 		createdAt: Date;
	// 		updatedAt: Date;
	// 	}

	// 	type ManagerAccountResponse = BaseGETResponse & {
	// 			integrationType: IntegrationType.MANAGER_ACCOUNT;
	// 			ipAddress: string;
	// 			port: number;
	// 	}

	// 	type RestAPIResponse = BaseGETResponse & {
	// 			integrationType: IntegrationType.REST_API;
	// 			apiUrl: string;
	// 	}

	// 	export type GETResponse = ManagerAccountResponse | RestAPIResponse;

	// 	export type PUTRequest = {
	// 		name: string;
	// 		displayName: string;
	// 		ipAddress?: string;
	// 		port?: number;
	// 		apiUrl?: string;
	// 		webLogin: number;
	// 		webPassword?: string;
	// 		tradingPlatformId: number;
	// 		isActive: boolean;
	// 		integrationType: IntegrationType
	// 	}

	// 	export type POSTRequest = {
	// 		name: string;
	// 		displayName: string;
	// 		ipAddress?: string;
	// 		port?: number;
	// 		apiUrl?: string;
	// 		webLogin: number;
	// 		webPassword?: string;
	// 		tradingPlatformId: number;
	// 		isActive: boolean;
	// 		integrationType: IntegrationType
	// 	}

	// }
	export namespace Lookup {

		export const URL = '/leverage/lookup'

		export interface SingleRecord {
			name: string
			index: number
		}

		export type GETResponse = SingleRecord[]

	}

}
