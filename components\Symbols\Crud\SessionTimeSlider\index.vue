<template>
	<v-input
		:disabled="p.disabled"
		class="session-time-slider"
	>
		<div class="d-flex flex-column w-100">
			<div>
				<v-label
					v-if="label"
					class="me-2"
				>
					{{ label }}
				</v-label>
				<v-btn
					:disabled="disabled"
					icon="i-mdi:plus"
					variant="text"
					size="small"
					@click="addRange"
				/>
			</div>
			<div class="flex-grow-1">
				<!-- {{ model }} -->

				<VSliderComponent
					ref="sliderRef"
					v-model.lazy="(model as unknown as Value[])"
					lazy
					:enable-cross="false"
					:tooltip-formatter="format"
					:interval="5"
					:max-range="24*60"
					class="minutes-ticks"
					:marks="marksFormatter"
					:min="0"
					:max="60 * 24 *2"
					:process="processor"
					adsorb
					:disabled="disabled"
					@drag-start="dragstartHandler"
				>
					<template #dot="dot">
						<div
							class="dot"
							:class="{ focused: dot.focus }"
						/>
					</template>
					<!-- <template #thumb-label="{modelValue: value}">
						{{ format( value) }}
					</template>
					<template #tick-label="{tick}">
						<span class="text-caption">{{ tickFormatter(tick.value) }}</span>
					</template> -->
				</VSliderComponent>
			</div>
		</div>
	</v-input>
</template>

<script lang="ts" setup>
import type { ProcessFunc, MarksFunction, Value } from './VSliderComponent/typings/index.d'
import VSliderComponent from './VSliderComponent'
import type { Symbols } from '~/types'

type Session = Symbols.MT5.MTConSymbolSession | Symbols.MT4.ConSession

const confirm = useNuxtApp().$confirm

type Emit = {
	(e: 'update:modelValue', value: Session[]): void
}

const emit = defineEmits<Emit>()

const sliderRef = ref<InstanceType<typeof VSliderComponent> | null>(null)

const lastDraggedDotIndex = ref<number | null>(null)

const p = defineProps<{
	modelValue: Session[]
	label?: string
	disabled?: boolean
	type: 'MT4' | 'MT5'
}>()

const isZeroDuration = (session: Session) => {
	if (p.type === 'MT4') {
		session = session as Symbols.MT4.ConSession
		return session.closeHour - session.openHour === 0 && session.closeMin - session.openMin === 0
	} else {
		session = session as Symbols.MT5.MTConSymbolSession
		return session.close - session.open === 0
	}
}
const model = defineModel<Session[]>('modelValue', {
	// @ts-ignore
	get(v) {
		return v.filter(item => !isZeroDuration(item))
			.map((item) => {
				if (p.type === 'MT4') {
					item = item as Symbols.MT4.ConSession
					const open = (item.openHour * 60) + item.openMin
					const close = (item.closeHour * 60) + item.closeMin
					return [open, close]
				} else {
					item = item as Symbols.MT5.MTConSymbolSession
					const open = item.open
					const close = item.close
					return [open, close]
				}
			}).flat()
	},
	set(v: any) {
		const max = Math.max(...v)
		const min = Math.min(...v)

		const duration = max - min

		const MAX_LIMIT = 24 * 60 // 1440 minutes

		if (duration > MAX_LIMIT) {
			// determine the index of the position changed using the last position
			(v as number[]).forEach((_value, valueIndex) => {
				if (lastDraggedDotIndex.value === valueIndex && valueIndex === v.length - 1) {
					// if last item
					v[valueIndex] = min + MAX_LIMIT
				} else if (lastDraggedDotIndex.value === valueIndex && valueIndex === 0) {
					// if first item
					// alert('You can only add 24 hours of session time')
					v[valueIndex] = max - MAX_LIMIT
				} else {
					// v[valueIndex] = lastValues.value[valueIndex]
				}
			})
		} else {
			lastValues.value = v
		}

		const result: Session[] = []

		if (p.type === 'MT4') {
			for (let i = 0; i < v.length; i += 2) {
				if (v[i] === v[i + 1]) {
					// skip zero duration
					continue
				}
				(result as Symbols.MT4.ConSession[]).push({
					openHour: toHours(v[i]),
					openMin: v[i] % 60,
					closeHour: toHours(v[i + 1]),
					closeMin: v[i + 1] % 60,
				})
			}
		} else {
			for (let i = 0; i < v.length; i += 2) {
				if (v[i] === v[i + 1]) {
					// skip zero duration
					continue
				}
				result.push({
					open: v[i],
					close: v[i + 1],
					openHours: toHours(v[i]),
					closeHours: toHours(v[i + 1]),
				})
			}
		}

		return result
	},
	required: true,
})
const lastValues = ref<number[]>([])
const processor: ProcessFunc = (pos) => {
	const result: ReturnType<ProcessFunc> = []

	for (let i = 0; i < pos.length; i += 2) {
		const start = pos[i]
		const end = pos[i + 1]

		result.push([start, end])
	}

	return result
}

const addRange = () => {
	// @ts-ignore
	if (p.type === 'MT4' && model.value.length / 2 >= 3) {
		confirm({
			title: 'Notice',
			text: 'You can only add 3 session times',
			cancelBtn: false,
		}).catch(() => {})
		return
	}

	const ranges = useCloned(p.modelValue).cloned.value

	let min, max
	if (p.type === 'MT4') {
		min = Math.min(...ranges.map(range => ((range as Symbols.MT4.ConSession).openHour * 60) + (range as Symbols.MT4.ConSession).openMin))

		max = Math.max(...ranges.map(range => ((range as Symbols.MT4.ConSession).closeHour * 60) + (range as Symbols.MT4.ConSession).closeMin))
	} else {
		min = Math.min(...ranges.map(range => (range as Symbols.MT5.MTConSymbolSession).open))

		max = Math.max(...ranges.map(range => (range as Symbols.MT5.MTConSymbolSession).close))
	}

	if (max - min >= 24 * 60) {
		confirm({
			title: 'Notice',
			text: 'You can only add 24 hours of session time. Please reduce your sessions first then try again',
			cancelBtn: false,
		}).catch(() => {})
		return
	}

	const open = max

	const close = max + ((24 * 60) - (max - min))
	if (p.type === 'MT4') {
		ranges.push({
			openHour: toHours(open),
			openMin: open % 60,
			closeHour: toHours(close),
			closeMin: close % 60,
		})
	} else {
		ranges.push({
			open,
			close,
			openHours: toHours(open),
			closeHours: toHours(close),
		})
	}

	emit('update:modelValue', ranges)
}

const format = (time: number) => {
	const hours = Math.floor(time / 60)
	const minutes = time % 60
	return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
}

const toHours = (time: number) => {
	return Math.floor(time / 60)
}

const marksFormatter: MarksFunction = (value: Value) => {
	// format number of minutes into 2 days format

	const time = (Number(value) % (24 * 60))
	const formatted = Math.floor(time / 60)
	return {
		label: formatted === 0 && !!value ? '24' : String(formatted),
		style: {
			backgroundColor: '#ccc',
			width: '1px',
		},
		activeStyle: {
			backgroundColor: '#3498db',
		},
		labelActiveStyle: {
			color: '#3498db',

		},
		// labelStyle: {
		// 	display: value % 120 === 0 ? 'none' : undefined,
		// },
	}
}

// const marks = computed(() => {
// 	const result:any[] = []
// 	const ticks = 24 * 60 * 2
// 	for (let i = 0; i < ticks; i++) {
// 		const value = i
// 		const time = (value % (24 * 60))
// 		const formatted = Math.floor(time / 60)
// 		// console.log('🚀 ~ marks ~ formatted:', formatted)
// 		result.push(result.includes(formatted) ? '' : formatted)
// 	}

// 	return result
// })
const dragstartHandler = (index: number) => {
	lastDraggedDotIndex.value = index
}
</script>

<style lang="scss">
.session-time-slider {
	.dot{
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, 0px);
			width: 2px;
			height: 12px;
			background-color: rgb(var(--v-theme-surface-variant));
			cursor: col-resize;
		}
		&.focused::before {
			background-color: rgb(var(--v-theme-success));
		}
	}
	.minutes-ticks {
		.vue-slider-mark {
			height: 4px;
			transform: translateY(-2px);
			width: 1px;
			// Hide all ticks initially
			display: none;

			&.v-slider-track__tick--filled {
				background-color: rgb(var(--v-theme-surface-variant));
				z-index: -1;
			}

			// Show only each 60th tick
			&:nth-of-type(6n + 1) {
				// display: block;
			}

			&:nth-of-type(12n + 1) {
				display: block;
				height: 8px;
				/* Adjusted height for visibility */
				transform: translateY(-4px);
			}
		}

		.vue-slider-mark-label {
			font-size: 8px;
		}
	}
}
</style>
