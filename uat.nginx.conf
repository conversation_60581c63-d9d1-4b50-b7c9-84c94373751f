server {
    listen 80;
    listen [::]:80;
    server_name wwww.ingotpilot.com ingotpilot.app;
    location / {
        return 301 https://www.ingotpilot.app/$1;
    }
}
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name wwww.ingotpilot.app;

    ssl_certificate /etc/ssl/nginx/ssl.crt;
    ssl_certificate_key /etc/ssl/nginx/ssl.key;

    charset utf-8;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header Access-Control-Allow-Origin "*";
    #add_header Cache-Control "no-cache";
    location /api {
        proxy_http_version 1.1;
        proxy_pass https://api.ingotpilot.app/;
        proxy_redirect off;
        proxy_pass_request_headers on;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Request-Id $request_id;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Access-Control-Allow-Methods "GET,HEAD,POST,OPTIONS";
    }
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
    }
}
