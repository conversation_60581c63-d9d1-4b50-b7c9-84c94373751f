import { defineStore } from 'pinia'
import type { FetchError } from 'ofetch'
import { Auth } from '~/types/Auth'

export enum VerifyRejection {
	TWO_FA_Required = 406,
	INVALID_Tokens = 401,
}

export interface Role {
	id: number
	name: string
	permissions: string[]
}
export interface User extends Auth.Me.GETResponse {}

declare module '#GlobalEvents' {
	interface Events {
		'auth:invalidated': undefined
		'auth:invalidating': undefined
		'auth:user': User
		'auth:login': Auth.Login.POSTResponse
		'auth:logout': undefined
	}
}
/**
 * @description This events is triggered when user data retrieved from the server
 */
// export const UserEvent: EventBusKey<User> = Symbol('auth:user')

/**
 * @description This events is triggered when user access data invalidated
 */
// export const InvalidatedEvent: EventBusKey<undefined> = Symbol('auth:invalidated')

/**
 * @description This events is triggered when user login successfully
 */
// export const LoginEvent: EventBusKey<Auth.Login.POSTResponse> = Symbol('auth:login')

type twoFAMethods = 'app' // | 'sms' | 'email'
interface State {
	user: User | null
	accessToken: string
	refreshToken: string
	accessTokenExpiry: string
	refreshTokenExpiry: string
	loading: boolean
	nextRefresh: string
	tokenRefresher: ReturnType<typeof setTimeout> | null
	loginPath: string
	gen2FaRemainingTime: number
	twoFactorEnabled: boolean
	is2FAVerified: boolean
	LoginMode: Auth.LoginMode.Modes | null
}

const log = useLog('Auth')
export const useAuthStore = defineStore({
	persist: true,
	id: 'authStore',
	state: (): State => ({
		accessToken: useCookie('accessToken').value || '',
		refreshToken: useCookie('refreshToken').value || '',
		accessTokenExpiry: useCookie('accessTokenExpiry').value || '',
		refreshTokenExpiry: useCookie('refreshTokenExpiry').value || '',
		user: null,
		loading: false,
		tokenRefresher: null,
		nextRefresh: '',
		loginPath: ('auth-login'),
		gen2FaRemainingTime: 0,
		twoFactorEnabled: JSON.parse(useCookie('twoFactorEnabled').value || 'false'),
		is2FAVerified: JSON.parse(useCookie('is2FAVerified').value || 'false'),
		LoginMode: null,
	}),
	getters: {
		hasToken(): boolean {
			return !!this.accessToken
		},
		hasRefreshToken(): boolean {
			return !!this.refreshToken
		},
		isLoggedIn(): boolean {
			return this.hasToken && (this.twoFactorEnabled ? this.is2FAVerified : true)
		},
		isAccessTokenExpired(): boolean {
			const dayjs = useDayjs()
			return dayjs(this.accessTokenExpiry).isBefore(dayjs())
		},
		isRefreshTokenExpired(): boolean {
			const dayjs = useDayjs()
			return dayjs(this.refreshTokenExpiry).isBefore(dayjs())
		},
		permissions(): User['permissionNames'] {
			return this.user?.permissionNames || []
		},
		canGenerate2FA(): boolean {
			// 30s at least should have been passed since the last 2FA generation
			return true // this.gen2FaRemainingTime <= 0
		},
	},
	actions: {
		async forget(body: Auth.ForgotPassword.POSTRequest) {
			const { $api } = useNuxtApp()
			return await new Promise((resolve, reject) => {
				this.$state.loading = true
				$api<Auth.ForgotPassword.POSTResponse>(Auth.ForgotPassword.URL, {
					body,
					method: 'POST',
				}).then((resp) => {
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				}).finally(() => {
					this.$state.loading = false
				})
			})
		},
		async resetPassword(body: Auth.ResetPassword.POSTRequest) {
			const { $api } = useNuxtApp()
			return await new Promise((resolve, reject) => {
				this.$state.loading = true
				$api<Auth.ResetPassword.POSTResponse>(Auth.ResetPassword.URL, {
					body,
					method: 'POST',
				}).then((resp) => {
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				}).finally(() => {
					this.$state.loading = false
				})
			})
		},

		async login(body: Auth.Login.POSTRequest) {
			const { $api } = useNuxtApp()
			this.invalidateTokens() // to prevent sending the previous tokens
			return await new Promise<Auth.Login.POSTResponse>((resolve, reject) => {
				this.$state.loading = true
				$api<Auth.Login.POSTResponse>(Auth.Login.URL, {
					body,
					method: 'POST',
				}).then((resp) => {
					this.setData(resp)
					// this.setTokenRefresher() it should be triggered in verifyUserCredentials
					useGlobalEvents().emit('auth:login', resp)
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				}).finally(() => {
					this.$state.loading = false
				})
			})
		},
		async verify2FA(code: string, _method: twoFAMethods) {
			const { $api } = useNuxtApp()
			return await new Promise((resolve, reject) => {
				$api<Auth.Login.POSTResponse>(Auth.TwoFactor.App.Verify.URL, {
					body: {
						code,
					},
					method: 'POST',
				}).then((resp) => {
					this.set2FAVerificationStatus(true)
					this.set2FAEnabledStatus(true)
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				})
			})
		},
		set2FAVerificationStatus(status: boolean) {
			this.is2FAVerified = status
			useCookie('is2FAVerified', {
				expires: this.accessTokenExpiry ? new Date(this.accessTokenExpiry) : new Date(Date.now() + 1000 * 60 * 60 * 24 * 365),
				sameSite: 'lax',
				priority: 'high',
			}).value = JSON.stringify(status)
		},
		set2FAEnabledStatus(status: boolean) {
			this.twoFactorEnabled = status
			useCookie('twoFactorEnabled', {
				expires: new Date(this.accessTokenExpiry),
				sameSite: 'lax',
				priority: 'high',
			}).value = JSON.stringify(status)
		},
		async generate2FA(_method: 'app') {
			const { $api } = useNuxtApp()
			return await new Promise<Auth.TwoFactor.App.Generate.GETResponse>((resolve, reject) => {
				$api<Auth.TwoFactor.App.Generate.GETResponse>(Auth.TwoFactor.App.Generate.URL, {
					method: 'GET',
				}).then((resp) => {
					this.resetGen2FaRemainingTime()
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				})
			})
		},
		resetGen2FaRemainingTime() {
			this.gen2FaRemainingTime = 0// 30
			// set timer
			const interval = setInterval(() => {
				this.gen2FaRemainingTime--
				if (this.gen2FaRemainingTime <= 0) {
					clearInterval(interval)
				}
			}, 1000)
		},
		async getUser() {
			const { $api } = useNuxtApp()
			return await new Promise<User>((resolve, reject) => {
				$api<User>('/auth/me', {
					method: 'GET',
				}).then((resp) => {
					this.user = resp
					if (resp.twoFactorEnabled) {
						// since there is a response, it means 2FA is verified already
						this.set2FAVerificationStatus(true)
					}
					useGlobalEvents().emit('auth:user', resp)
					resolve(resp)
				}).catch((err: FetchError) => {
					console.error(err)
					reject(err)
				})
			})
		},
		async refresh() {
			const { $api } = useNuxtApp()
			log('Refreshing tokens')
			return await new Promise((resolve, reject) => {
				$api<Auth.RefreshToken.POSTResponse>(Auth.RefreshToken.URL, {
					method: 'POST',
					body: {
						refreshToken: this.refreshToken,
					},
				}).then((resp) => {
					this.setData(resp)
					this.setTokenRefresher()
					resolve(resp)
				}).catch((err) => {
					console.error(err)
					reject(err)
				})
			})
		},
		setData(data: Auth.Login.POSTResponse | Auth.RefreshToken.POSTResponse) {
			this.accessToken = data.accessToken
			this.refreshToken = data.refreshToken
			this.accessTokenExpiry = data.accessTokenExpiry
			this.refreshTokenExpiry = data.refreshTokenExpiry
			this.set2FAEnabledStatus(data.twoFactorEnabled)
			useCookie('accessToken', {
				expires: new Date(data.accessTokenExpiry),
				sameSite: 'lax',
				priority: 'high',
			}).value = data.accessToken

			useCookie('accessTokenExpiry', {
				expires: new Date(data.accessTokenExpiry),
				sameSite: 'lax',
				priority: 'high',
			}).value = data.accessTokenExpiry

			useCookie('refreshTokenExpiry', {
				expires: new Date(data.refreshTokenExpiry),
				sameSite: 'lax',
				priority: 'high',
			}).value = data.refreshTokenExpiry

			useCookie('refreshToken', {
				expires: new Date(data.refreshTokenExpiry),
				sameSite: 'lax',
				priority: 'high',
			}).value = data.refreshToken
		},
		setTokenRefresher() {
			const dayjs = useDayjs()
			if (this.tokenRefresher) {
				clearTimeout(this.tokenRefresher)
			}
			this.nextRefresh = dayjs(this.accessTokenExpiry).subtract(1, 'minute').toISOString()
			log('Next Refresh at', this.nextRefresh, `(${since(this.nextRefresh)})`)
			this.tokenRefresher = setTimeout(async () => {
				if (!this.isRefreshTokenExpired && !!this.refreshToken) {
					await this.refresh()
				} else {
					console.error('Refresh token expired')
				}
			}, dayjs(this.nextRefresh).diff(dayjs(), 'millisecond'))
		},
		logout() {
			useGlobalEvents().emit('auth:logout')
			const { $api } = useNuxtApp()
			if (this.isLoggedIn) {
				return $api(Auth.Logout.URL, {
					method: 'POST',
				})
					.catch(() => {})
					.finally(() => {
						this.invalidateTokens()
					})
			}
		},
		invalidateRefreshToken() {
			this.refreshToken = ''
			this.refreshTokenExpiry = ''
			useCookie('refreshToken').value = null
			if (this.tokenRefresher) {
				clearTimeout(this.tokenRefresher)
			}
		},
		invalidateAccessToken() {
			this.set2FAVerificationStatus(false)

			useCookie('accessTokenExpiry').value = null
			useCookie('accessToken').value = null

			this.accessToken = ''
			this.accessTokenExpiry = ''

			this.user = null
			useGlobalEvents().emit('auth:invalidated')
		},
		invalidateTokens() {
			this.invalidateAccessToken()
			this.invalidateRefreshToken()
		},
		verifyTokens() {
			const { $api } = useNuxtApp()
			return $api(Auth.CheckTokensValidity.URL, {
				method: 'POST',
				body: {
					accessToken: this.accessToken,
					refreshToken: this.refreshToken,
				},
			})
		},
		verifyUserCredentials() {
			return new Promise((resolve, reject) => {
				this.loading = true

				// since we have cookies, it means its not expired and most probably still active. will assume that they are still valid.
				if (this.hasToken) {
					log('Has Access Token')

					if (this.isLoggedIn) {
						this.getUser()
							.then(() => {
								log('User verified')
								// allow user to proceed and continue using the app
								// this.refresh()
								this.setTokenRefresher()
								resolve(true)
							})
							.catch((err: FetchError) => {
								switch (err.status) {
									case 406:
										log('2FA Required')
										reject(VerifyRejection.TWO_FA_Required)
										break
									case 401:
										log('Access Token Expired')
										this.invalidateAccessToken()
										this.verifyUserCredentials()
											.catch(reject)
										break
								}
								log('Error getting user')
								this.invalidateAccessToken()
								this.verifyUserCredentials()
									.catch(reject)
							})
					} else {
						reject(VerifyRejection.TWO_FA_Required)
					}
				} else if (this.hasRefreshToken) {
					log('Has Refresh Token')
					this.refresh()
						.then(() => {
							// await this.getUser()
							// this.setTokenRefresher()
							// resolve(true)
							this.verifyUserCredentials()
								.then(resolve)
								.catch(reject)
						})
						.catch(() => {
							log('Error refreshing tokens')
							this.invalidateTokens()
							this.verifyUserCredentials().catch(reject)
						})
				} else {
					this.invalidateTokens()
					this.loading = false
					reject(VerifyRejection.INVALID_Tokens)
				}
			})
		},
		async verifyLoginMode(): Promise<Auth.LoginMode.Modes> {
			if (this.LoginMode) {
				return this.LoginMode
			}
			const { $api } = useNuxtApp()
			return await $api<Auth.LoginMode.GETResponse>(Auth.LoginMode.URL, {
				method: 'GET',
			}).then((resp) => {
				this.LoginMode = resp.loginMode
				return this.LoginMode
			})
		},

		async changePassword(body: Auth.ChangePassword.POSTRequest): Promise<Auth.ChangePassword.POSTResponse> {
			const { $api } = useNuxtApp()
			return await $api<Auth.ChangePassword.POSTResponse>(Auth.ChangePassword.URL, {
				method: 'POST',
				body,
			})
		},
	},
})
