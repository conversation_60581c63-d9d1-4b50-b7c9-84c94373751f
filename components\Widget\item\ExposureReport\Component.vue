<template>
	<v-card
		ref="card"
		height="100%"
		flat
		border
	>
		<v-card-title class="d-flex align-center">
			<div class="text-subtitle-1 text-wrap">
				Exposure Report
			</div>
			<v-spacer />
			<v-btn
				color="background"
				text="Export"
				prepend-icon="i-material-symbols-light:export-notes-outline"
				border
			/>
		</v-card-title>
		<v-card-text>
			<PieChart
				:chart-data="chartData"
				:options="options"
				:height="170"
			/>
		</v-card-text>
	</v-card>
</template>

<script lang="ts" setup>
import { PieChart } from 'vue-chart-3'
import type { ChartData, ChartOptions } from 'chart.js'
import type { VCard } from 'vuetify/components'
import type { WidgetItemProps } from '../../types'
import type { SocketData } from '~/services/WebSocket/ExposureReport'
import { ExposureReport } from '~/services/WebSocket/ExposureReport'

type Props = WidgetItemProps & {}

const card = ref<InstanceType<typeof VCard> | null>(null)

const cardWidth = ref(170)

const widthHandler = () => {
	if (card.value) {
		cardWidth.value = card.value.$el.clientWidth
	}
}

useResizeObserver(card, widthHandler)

const theme = useTheme()

const options = computed<ChartOptions<'pie'>>(() => ({
	color: theme.global.current.value.colors['on-background'],
	plugins: {
		legend: {
			position: 'right',
			display: cardWidth.value > 500,
			labels: {
				usePointStyle: true,
				pointStyle: 'rect',
				font: {
					size: 12,
				},
			},
		},
		tooltip: {
			boxPadding: 4,
			callbacks: {
				title(tooltipItems) {
					return tooltipItems[0].label
				},
				label(tooltipItem) {
					const category = tooltipItem.label
					const categoryData = data.value[category]

					// Format values as needed
					const percentage = tooltipItem.parsed + '%'
					const exposure = money(categoryData.TotalExposureValue, 'USD', {
						notation: 'compact',
						maximumFractionDigits: 2,
					})
					const profit = money(categoryData.CurrentUnrealizedProfit, 'USD', {
						notation: 'compact',
						maximumFractionDigits: 2,
					})

					return [
						`Exposure: ${exposure}`,
						`Unrealized Profit: ${profit}`,
						`Ratio: ${percentage}`,
					]
				},
			},
		},
	},
}))

const chartData = computed<ChartData<'pie'>> (() => ({
	labels: categoriesArray.value,
	datasets: [
		{
			data: categoriesPercentage.value,
			backgroundColor: [
				'#059bff', // Bright Blue
				'#ff5b5b', // Red
				'#ffc859', // Yellow
				'#4cd97b', // Green
				'#9b59b6', // Purple
				'#f39c12', // Orange
				'#1abc9c', // Turquoise
				'#3498db', // Light Blue
				'#c0392b', // Dark Red
				'#e74c3c', // Coral
			],
			borderAlign: 'inner',
			type: 'pie',
			hoverBorderWidth: 2,
			borderWidth: 0,

		},
	],

}))

const categoriesArray = computed(() => Object.keys(data.value))

const categoriesPercentage = computed(() => Object.keys(data.value).map(key => data.value[key].Ratio))

const p = defineProps<Props>()

const exposureReportInstance = new ExposureReport()

const previewData = ref<SocketData>({
	'FX': {
		TotalVolumeBuy: 96451700,
		TotalVolumeSell: 7473900,
		TotalExposureLots: 88977800,
		TotalExposureValue: 3370816170.971003,
		CurrentUnrealizedProfit: -1215995230899.6606,
		TotalClosedProfitForDay: 1467726.4,
		Ratio: 20,
	},
	'Metals': {
		TotalVolumeBuy: 64842300,
		TotalVolumeSell: 21115900,
		TotalExposureLots: 43726400,
		TotalExposureValue: 130941052899.2,
		CurrentUnrealizedProfit: 665634186460543,
		TotalClosedProfitForDay: 26466090.58,
		Ratio: 10,
	},
	'Indices': {
		TotalVolumeBuy: 115411500,
		TotalVolumeSell: 134022500,
		TotalExposureLots: -18611000,
		TotalExposureValue: 381057432755,
		CurrentUnrealizedProfit: -7506830524.000439,
		TotalClosedProfitForDay: 1166458.1699999997,
		Ratio: 10,
	},
	'Energies': {
		TotalVolumeBuy: 5056100,
		TotalVolumeSell: 7264400,
		TotalExposureLots: -2208300,
		TotalExposureValue: -135090966.9000003,
		CurrentUnrealizedProfit: 27016038977.59993,
		TotalClosedProfitForDay: 44228.520000000004,
		Ratio: 10,
	},
	'UK Shares': {
		TotalVolumeBuy: 616000,
		TotalVolumeSell: 1000,
		TotalExposureLots: 615000,
		TotalExposureValue: 1130305730,
		CurrentUnrealizedProfit: -66065660.000000015,
		TotalClosedProfitForDay: 500030.56,
		Ratio: 10,
	},
	'Germany Shares': {
		TotalVolumeBuy: 311000,
		TotalVolumeSell: 0,
		TotalExposureLots: 311000,
		TotalExposureValue: 27260240,
		CurrentUnrealizedProfit: -1809930.0000000002,
		TotalClosedProfitForDay: 0,
		Ratio: 10,
	},
	'France Shares': {
		TotalVolumeBuy: 280000,
		TotalVolumeSell: 0,
		TotalExposureLots: 280000,
		TotalExposureValue: 63532300,
		CurrentUnrealizedProfit: -7227000.000000004,
		TotalClosedProfitForDay: 0,
		Ratio: 10,
	},
	'US Shares': {
		TotalVolumeBuy: 72505000,
		TotalVolumeSell: 1171000,
		TotalExposureLots: 71334000,
		TotalExposureValue: 15094059070,
		CurrentUnrealizedProfit: -464896680.00000095,
		TotalClosedProfitForDay: 5517004.680000001,
		Ratio: 10,
	},
	'Cryptocurrencies': {
		TotalVolumeBuy: 1208200,
		TotalVolumeSell: 984300,
		TotalExposureLots: 223900,
		TotalExposureValue: -16189195857,
		CurrentUnrealizedProfit: 50079285104471.58,
		TotalClosedProfitForDay: 1643558.9899999998,
		Ratio: 10,
	},
	'Unknown': {
		TotalVolumeBuy: 71344600,
		TotalVolumeSell: 7245700,
		TotalExposureLots: 64098900,
		TotalExposureValue: 150990400681.46994,
		CurrentUnrealizedProfit: 302161693126.74976,
		TotalClosedProfitForDay: 3840422.77,
		Ratio: 10,
	},
	'ETF\'s': {
		TotalVolumeBuy: 50003000,
		TotalVolumeSell: 0,
		TotalExposureLots: 50003000,
		TotalExposureValue: 928795529.9999999,
		CurrentUnrealizedProfit: 70071479.99999993,
		TotalClosedProfitForDay: 0,
		Ratio: 10,
	},
})

const realData = ref<SocketData>({} as SocketData)

const data = computed<SocketData>(() => {
	if (p.isPreview) {
		return previewData.value
	}

	return realData.value
})

// const myPref = usePreferencesStore()

watch(() => p.isPreview, (isPreview) => {
	if (!isPreview) {
		exposureReportInstance.connect().onUpdate((data) => {
			realData.value = data
		})
	}
}, { immediate: true, once: true })

const handleChartClick = (event: any, elements: any) => {
	console.log('🚀 ~ handleChartClick ~ elements:', elements)
	// if (elements && elements.length > 0) {
	// 	const index = elements[0].index
	// 	const category = categoriesArray.value[index]
	// 	// Navigate to a specific route based on the category
	// 	router.push(`/exposure/${encodeURIComponent(category)}`)
	// }
}
</script>
