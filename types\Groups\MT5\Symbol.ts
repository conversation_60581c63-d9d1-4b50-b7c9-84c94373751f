import type { Symbols } from '~/types/Symbols'
/**
 * Request execution settings for IMTConGroupSymbol.
 */
export enum EnREFlags {
	/**
	 * No flags.
	 */
	RE_FLAGS_NONE = 0,

	/**
	 * Additional confirmation mode.
	 */
	RE_FLAGS_ORDER = 1,
}

/**
 * Flags of permissions by the group symbols for IMTConGroupSymbol.
 */
export enum EnPermissionsFlags {
	/**
	 * No flags.
	 */
	PERMISSION_NONE = 0,

	/**
	 * Allow the depth of market by symbols for the group.
	 */
	PERMISSION_BOOK = 1,
}

/**
 * Margin calculation flags are listed in IMTConGroup::EnMarginFlags.
 */
export enum EnMarginFlags {
	/**
	 * No flags.
	 */
	MARGIN_FLAGS_NONE = 0,

	/**
	 * Client's profit accumulated during the day will be added to balance at end of trading day.
	 */
	MARGIN_FLAGS_CLEAR_ACC = 1,

	/**
	 * End of enumeration. All flags are enabled.
	 */
	MARGIN_FLAGS_ALL,
}

export type MarginFlags = EnMarginFlags | Symbols.MT5.MarginFlags
export interface Symbol {
	/**
	 * The path to a symbol or group of symbols that are subject to the special group settings.
	 */
	path: string

	/**
	 * The symbol trading mode for the group. Passed in a value of the EnTradeMode enumeration.
	 */
	tradeMode: Symbols.MT5.TradeMode

	/**
	 * The symbol execution mode for the group. Passed in a value of the EnExecutionMode enumeration.
	 */
	execMode: Symbols.MT5.ExecutionMode

	/**
	 * Types of filling allowed for the symbol in this group. Passed as a value of the EnFillingFlags enumeration (sum of values of appropriate flags).
	 */
	fillFlags: Symbols.MT5.FillingFlags

	/**
	 * Types of order expiration allowed for the symbol in this group. Passed as a value of the EnExpirationFlags enumeration (sum of values of appropriate flags).
	 */
	expirFlags: Symbols.MT5.ExpirationFlags

	/**
	 * Difference between the symbol spread for the group and the default spread.
	 */
	spreadDiff: number

	/**
	 * The balance of spread difference set for the group. The balance of spread difference is set as a shift from the equal distribution of the spread difference value between Bid and Ask prices.
	 */
	spreadDiffBalance: number

	/**
	 * The price band, within which the group is not allowed to place stop orders for a symbol.
	 */
	stopsLevel: number

	/**
	 * The price band, within which it is not allowed to modify orders and positions for the group.
	 */
	freezeLevel: number

	/**
	 * The minimum volume of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMin: number

	/**
	 * The minimum volume extension of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMinExt: number

	/**
	 * The maximum volume of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMax: number

	/**
	 * The maximum volume extension of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMaxExt: number

	/**
	 * The step of change of trade operations volume for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeStep: number

	/**
	 * The step extension of change of trade operations volume for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeStepExt: number

	/**
	 * The maximum allowable aggregate volume of positions and orders on a symbol in one direction for this group. One unit corresponds to 1/10000 lot.
	 */
	volumeLimit: number

	/**
	 * The maximum allowable aggregate volume extension of positions and orders on a symbol in one direction for this group. One unit corresponds to 1/10000 lot.
	 */
	volumeLimitExt: number

	/**
	 * The additional modes of symbol margin checking for the group. Passed in a value of the EnMarginFlags.
	 */
	marginFlags: MarginFlags

	/**
	 * The size of initial symbol margin for the group.
	 */
	marginInitial: number

	/**
	 * The size of symbol maintenance margin for the group.
	 */
	marginMaintenance: number

	/**
	 * The initial margin rate for market Buy orders.
	 */
	marginInitialBuy: number

	/**
	 * The initial margin rate for market Sell orders.
	 */
	marginInitialSell: number

	/**
	 * The initial margin rate for Buy Limit orders.
	 */
	marginInitialBuyLimit: number

	/**
	 * The initial margin rate for Sell Limit orders.
	 */
	marginInitialSellLimit: number

	/**
	 * The initial margin rate for Buy Stop orders.
	 */
	marginInitialBuyStop: number

	/**
	 * The initial margin rate for Sell Stop orders.
	 */
	marginInitialSellStop: number

	/**
	 * The initial margin rate for Buy Stop Limit orders.
	 */
	marginInitialBuyStopLimit: number

	/**
	 * The initial margin rate for Sell Stop Limit orders.
	 */
	marginInitialSellStopLimit: number

	/**
	 * The maintenance margin rate for market Buy orders.
	 */
	marginMaintenanceBuy: number

	/**
	 * The maintenance margin rate for market Sell orders.
	 */
	marginMaintenanceSell: number

	/**
	 * The maintenance margin rate for Buy Limit orders.
	 */
	marginMaintenanceBuyLimit: number

	/**
	 * The maintenance margin rate for Sell Limit orders.
	 */
	marginMaintenanceSellLimit: number

	/**
	 * The maintenance margin rate for Buy Stop orders.
	 */
	marginMaintenanceBuyStop: number

	/**
	 * The maintenance margin rate for Sell Stop orders.
	 */
	marginMaintenanceSellStop: number

	/**
	 * The maintenance margin rate for Buy Stop Limit orders.
	 */
	marginMaintenanceBuyStopLimit: number

	/**
	 * The maintenance margin rate for Sell Stop Limit orders.
	 */
	marginMaintenanceSellStopLimit: number

	/**
	 * The liquidity margin rate of the symbol for the group.
	 */
	marginLiquidity: number

	/**
	 * The hedged margin value.
	 */
	marginHedged: number

	/**
	 * The currency used for margin calculations.
	 */
	marginCurrency: number

	/**
	 * The swap calculation mode for a certain symbol for the group. Passed in a value of the EnSwapMode enumeration.
	 */
	swapMode: Symbols.MT5.SwapMode

	/**
	 * The long position swap for a symbol for the group.
	 */
	swapLong: number

	/**
	 * The short position swap for a symbol for the group.
	 */
	swapShort: number

	/**
	 * A day of charging a triple swap for a symbol for this group.
	 */
	swap3Day: number

	/**
	 * The number of days in a year used in calculating swap percent for a given group. Passed by the EnSwapDays enumeration value.
	 */
	swapYearDay: number

	/**
	 * Additional swap settings by symbol for the given group. Passed by the EnSwapFlags enumeration value.
	 */
	swapFlags: Symbols.MT5.SwapFlags

	/**
	 * Sunday swap multiplier in symbol settings for the given group.
	 */
	swapRateSunday: number

	/**
	 * Monday swap multiplier in symbol settings for the given group.
	 */
	swapRateMonday: number

	/**
	 * Tuesday swap multiplier in symbol settings for the given group.
	 */
	swapRateTuesday: number

	/**
	 * Wednesday swap multiplier in symbol settings for the given group.
	 */
	swapRateWednesday: number

	/**
	 * Thursday swap multiplier in symbol settings for the given group.
	 */
	swapRateThursday: number

	/**
	 * Friday swap multiplier in symbol settings for the given group.
	 */
	swapRateFriday: number

	/**
	 * Saturday swap multiplier in symbol settings for the given group.
	 */
	swapRateSaturday: number

	/**
	 * Time in seconds during which the price issued by a dealer in the request execution mode is valid.
	 */
	reFlags: EnREFlags

	/**
	 * Timeout for re-quote in seconds.
	 */
	reTimeout: number

	/**
	 * The flags of instant execution for the group. Passed in a value of the EnIEFlags enumeration (sum of values of appropriate flags).
	 */
	ieFlags: Symbols.MT5.TradeInstantFlags

	/**
	 * The mode of checking during instant execution set for a group. Passed in a value of the EnInstantMode enumeration.
	 */
	ieCheckMode: Symbols.MT5.InstantMode

	/**
	 * The maximum allowed difference between the time of arrival of the price, at which the client places an order, and the time of the last price. Specified in seconds.
	 */
	ieTimeout: number

	/**
	 * The maximum allowed slippage in the profitable direction during instant execution.
	 */
	ieSlipProfit: number

	/**
	 * The maximum allowed slippage in the loss direction during instant execution.
	 */
	ieSlipLosing: number

	/**
	 * The maximum volume of a trade operation that can be executed in the instant execution mode. One unit corresponds to 1/10000 lot.
	 */
	ieVolumeMax: number
	ieVolumeMaxExt: number
	/**
	 * The flags of order types that are allowed for the symbol. Passed in a value of the EnOrderFlags enumeration (sum of values of appropriate flags).
	 */
	orderFlags: Symbols.MT5.OrderFlags

	permissionsFlags: EnPermissionsFlags

	permissionsBookdepth: number //  assumed type
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface OldSymbol {
	/**
	 * The path to a symbol or group of symbols that are subject to the special group settings.
	 */
	path: string

	/**
	 * The symbol trading mode for the group. Passed in a value of the EnTradeMode enumeration.
	 */
	tradeMode: number

	/**
	 * The symbol execution mode for the group. Passed in a value of the EnExecutionMode enumeration.
	 */
	execMode: number

	/**
	 * Types of filling allowed for the symbol in this group. Passed as a value of the EnFillingFlags enumeration (sum of values of appropriate flags).
	 */
	fillFlags: number

	/**
	 * Types of order expiration allowed for the symbol in this group. Passed as a value of the EnExpirationFlags enumeration (sum of values of appropriate flags).
	 */
	expirFlags: number

	/**
	 * Difference between the symbol spread for the group and the default spread.
	 */
	spreadDiff: number

	/**
	 * The balance of spread difference set for the group. He balance of spread difference is set a shift from the equal distribution of the spread difference value between Bid and Ask prices. For example, if the spread difference is equal to 4 and it is distributed as -2 Bid/+2 Ask, then the balance of spread difference value is
  */
	spreadDiffBalance: number

	/**
	 * The price band, within which the group is not allowed to place stop orders for a symbol.
	 */
	stopsLevel: number

	/**
	 * The price band, within which it is not allowed to modify orders and positions for the group.
	 */
	freezeLevel: number

	/**
	 * The minimum volume of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMin: number
	volumeMinExt: number
	/**
	 * The maximum volume of trade operations for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeMax: number
	volumeMaxExt: number
	/**
	 * The step of change of trade operations volume for a symbol for the group. One unit corresponds to 1/10000 lot.
	 */
	volumeStep: number
	volumeStepExt: number
	/**
	 * The maximum allowable aggregate volume of positions and orders on a symbol in one direction for this group. One unit corresponds to 1/10000 lot.
	 */
	volumeLimit: number
	volumeLimitExt: number
	/**
	 * The additional modes of symbol margin checking for the group. Passed in a value of the EnMarginFlags.
	 */
	marginFlags: EnMarginFlags

	/**
	 * The size of initial symbol margin for the group.
	 */
	marginInitial: number

	/**
	 * The size of symbol maintenance margin for the group.
	 */
	marginMaintenance: number

	/**
	 * The initial margin rate for market Buy orders.
	 */
	marginInitialBuy: number

	/**
	 * The initial margin rate for market Sell orders.
	 */
	marginInitialSell: number

	/**
	 * The initial margin rate for Buy Limit orders.
	 */
	marginInitialBuyLimit: number

	/**
	 * The initial margin rate for Sell Limit orders.
	 */
	marginInitialSellLimit: number

	/**
	 * The initial margin rate for Buy Stop orders.
	 */
	marginInitialBuyStop: number

	/**
	 * The initial margin rate for Sell Stop orders.
	 */
	marginInitialSellStop: number

	/**
	 * The initial margin rate for Buy Stop Limit orders.
	 */
	marginInitialBuyStopLimit: number

	/**
	 * The initial margin rate for Sell Stop Limit orders.
	 */
	marginInitialSellStopLimit: number

	/**
	 * The maintenance margin rate for market Buy orders.
	 */
	marginMaintenanceBuy: number

	/**
	 * The maintenance margin rate for market Sell orders.
	 */
	marginMaintenanceSell: number

	/**
	 * The maintenance margin rate for Buy Limit orders.
	 */
	marginMaintenanceBuyLimit: number

	/**
	 * The maintenance margin rate for Sell Limit orders.
	 */
	marginMaintenanceSellLimit: number

	/**
	 * The maintenance margin rate for Buy Stop orders.
	 */
	marginMaintenanceBuyStop: number

	/**
	 * The maintenance margin rate for Sell Stop orders.
	 */
	marginMaintenanceSellStop: number

	/**
	 * The maintenance margin rate for Buy Stop Limit orders.
	 */
	marginMaintenanceBuyStopLimit: number

	/**
	 * The maintenance margin rate for Sell Stop Limit orders.
	 */
	marginMaintenanceSellStopLimit: number

	marginLiquidity: number
	/**
	 * The hedged margin value.
	 */
	marginHedged: number

	marginCurrency: number

	/**
	 * The swap calculation mode for a certain symbol for the group. Passed in a value of the EnSwapMode enumeration.
	 */
	swapMode: number

	/**
	 * The long position swap for a symbol for the group.
	 */
	swapLong: number

	/**
	 * The short position swap for a symbol for the group.
	 */
	swapShort: number

	/**
	 * A day of charging a triple swap for a symbol for this group.
	 */
	swap3Day: number

	/**
	 * The number of days in a year used in calculating swap percent for a given group. Passed by the EnSwapDays enumeration value.
	 */
	swapYearDay: number

	/**
	 * Additional swap settings by symbol for the given group. Passed by the EnSwapFlags enumeration value.
	 */
	swapFlags: number

	/**
	 * Sunday swap multiplier in symbol settings for the given group.
	 */
	swapRateSunday: number

	/**
	 * Monday swap multiplier in symbol settings for the given group.
	 */
	swapRateMonday: number

	/**
	 * Tuesday swap multiplier in symbol settings for the given group.
	 */
	swapRateTuesday: number

	/**
	 * Wednesday swap multiplier in symbol settings for the given group.
	 */
	swapRateWednesday: number

	/**
	 * Thursday swap multiplier in symbol settings for the given group.
	 */
	swapRateThursday: number

	/**
	 * Friday swap multiplier in symbol settings for the given group.
	 */
	swapRateFriday: number

	/**
	 * Saturday swap multiplier in symbol settings for the given group.
	 */
	swapRateSaturday: number

	/**
	 * Time in seconds during which the price issued by a dealer in the request execution mode is valid.
	 */
	reTimeout: number
	ieFlags: number
	/**
	 * The mode of checking during instant execution set for a group. Passed in a value of the EnInstantMode enumeration.
	 */
	ieCheckMode: number

	/**
	 * The maximum allowed difference between the time of arrival of the price, at which the client places an order, and the time of the last price. Specified in seconds.
	 */
	ieTimeout: number

	/**
	 * The maximum allowed slippage in the profitable direction during instant execution.
	 */
	ieSlipProfit: number

	/**
	 * The maximum allowed slippage in the loss direction during instant execution.
	 */
	ieSlipLosing: number

	/**
	 * The maximum volume of a trade operation that can be executed in the instant execution mode. One unit corresponds to 1/10000 lot.
	 */
	ieVolumeMax: number
	ieVolumeMaxExt: number
	/**
	 * The flags of order types that are allowed for the symbol. Passed in a value of the EnOrderFlags enumeration (sum of values of appropriate flags).
	 */
	orderFlags: number

	/**
	 * The liquidity rate of the symbol for the group. It determines the amount of the current value of an asset for the specified financial instrument, which will be taken into account as collateral (accounted for in client's equity).
	 */
	marginRateLiquidity: number

	/**
	 * The flags of request execution for the group. Passed in a value of the EnREFlags enumeration (sum of values of appropriate flags).
	 */
	reFlags: EnREFlags
}
