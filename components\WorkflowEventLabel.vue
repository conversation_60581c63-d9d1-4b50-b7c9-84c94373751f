<template>
	<v-chip
		:color="color"
		variant="tonal"
	>
		{{ text }}
	</v-chip>
</template>

<script lang="ts" setup>
import { Transactions } from '~/types/Transactions'

const { t } = useI18n()

const model = defineModel<string>()

const text = computed(() => {
	switch (model.value) {
		case Transactions._Id.Workflow.Event.Fallback:
			return t('WorkflowEventLabel.fallback')
		case Transactions._Id.Workflow.Event.Approval:
			return t('WorkflowEventLabel.approval')
		case Transactions._Id.Workflow.Event.Reviewer:
			return t('WorkflowEventLabel.reviewal')
		case Transactions._Id.Workflow.Event.Requester:
			return t('WorkflowEventLabel.request')
		default:
			return t('WorkflowEventLabel.unknown')
	}
})

const color = computed(() => {
	switch (model.value) {
		case Transactions._Id.Workflow.Event.Fallback:
			return 'deep-orange'
		case Transactions._Id.Workflow.Event.Approval:
			return 'teal'
		case Transactions._Id.Workflow.Event.Reviewer:
			return 'light-blue'
		case Transactions._Id.Workflow.Event.Requester:
			return 'blue-grey'
		default:
			return 'grey'
	}
})
</script>
