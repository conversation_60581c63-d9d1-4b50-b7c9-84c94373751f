<template>
	<v-container fluid>
		<v-text-field
			v-model.number="item.timeout"
			v-mask="Mask.Integer"
			:rules="rules({ type: 'integer', max: 60 })"
			:label="$t('Groups.MT4.Crud.Permissions.timeout')"
			suffix="seconds (for confirmations)"
		/>
		<v-select
			v-model="newsLanguages"
			multiple
			:items="newsLanguagesItems"
			:rules="rules({ type: 'array', max: 8 })"
			:label="$t('Groups.MT4.Crud.Permissions.news-languages')"
			chips
			closable-chips
		/>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model.number="item.maxSecurities"
					type="number"
					:items="maxItems"
					:label="$t('Groups.MT4.Crud.Permissions.max-symbols')"
					:return-object="false"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-combobox
					v-model.number="item.maxPositions"
					type="number"
					:return-object="false"
					:items="maxItems"
					:label="$t('Groups.MT4.Crud.Permissions.max-orders')"
				/>
			</v-col>
		</v-row>
		<v-select
			v-model="tradeSignalModel"
			:items="tradeSignalItems"
			:label="$t('Groups.MT4.Crud.Permissions.trade-signals')"
		/>
		<v-row>
			<v-col cols="12">
				<v-checkbox
					v-model="rightsModel"
					:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_EMAIL"
					hide-details
					color="Primary"
					:label="$t('Groups.MT4.Crud.Permissions.enable-internal-mail-syst')"
				/>
				<v-checkbox
					v-model="item.useSwap"
					:true-value="1"
					:false-value="0"
					hide-details
					color="Primary"
					:label="$t('Groups.MT4.Crud.Permissions.enable-charge-of-swap')"
				/>
				<v-checkbox
					v-model="rightsModel"
					:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_TRAILING"
					hide-details
					color="Primary"
					:label="$t('Groups.MT4.Crud.Permissions.enable-trailing-stop')"
				/>
				<v-checkbox
					v-model="rightsModel"
					:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_ADVISOR"
					hide-details
					color="Primary"
					:label="$t('Groups.MT4.Crud.Permissions.enable-trading-by-expert-')"
				/>
				<v-checkbox
					v-model="rightsModel"
					:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_EXPIRATION"
					hide-details
					color="Primary"
					:label="$t('Groups.MT4.Crud.Permissions.enable-expiration-of-pend')"
				/>
			</v-col>
			<v-divider class="mx-4" />
			<v-col
				cols="12"
				md="12"
			>
				<v-defaults-provider
					:defaults="{
						VCheckbox: {
							trueValue: 1,
							falseValue: 0,
						} }"
				>
					<v-checkbox
						v-model="item.checkIEPrices"
						hide-details
						color="Primary"
						:label="$t('Groups.MT4.Crud.Permissions.check-request-prices-in-i')"
					/>
					<v-checkbox
						v-model="item.hedgeProhibited"
						:disabled="!!item.closeFIFO"
						hide-details
						color="Primary"
						:label="$t('Groups.MT4.Crud.Permissions.prohibit-hedge-positions')"
					/>
					<v-checkbox
						v-model="item.closeFIFO"
						hide-details
						color="Primary"
						:label="$t('Groups.MT4.Crud.Permissions.position-closing-accordin')"
						@update:model-value="closeFIFOHandler"
					/>
					<v-checkbox
						v-model="item.closeReopen"
						hide-details
						color="Primary"
						:label="$t('Groups.MT4.Crud.Permissions.use-partial-close-with-fu')"
					/>
					<v-checkbox
						v-model="rightsModel"
						:true-value="Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_RISK_WARNING"
						hide-details
						color="Primary"
						:label="$t('Groups.MT4.Crud.Permissions.show-the-risk-warning-win')"
					/>
				</v-defaults-provider>
			</v-col>
		</v-row>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Mask } from '~/types/Mask'
import { Groups } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const newsLanguagesItems = enumToItems(Groups.MT4.NewsLanguage, 'Groups.MT4.NewsLanguage')

const { t } = useI18n()

const maxItems = [
	{ title: '10', value: 10 },
	{ title: '30', value: 30 },
	{ title: '50', value: 50 },
	{ title: t('Groups.MT4.Crud.Permissions.unlimited'), value: 0 },
]

const newsLanguages = computed<number[]>({
	get: () => [...new Set(p.item.newsLanguages)],
	set: (value) => {
		if (value.length < 8) {
			// fill with zeros to 8
			value = value.concat(Array(8 - value.length).fill(0))
		}
		emit('update:item', { newsLanguages: value })
	},
})

const computedRights = computed<typeof p.item.rights>({
	get: () => p.item.rights,
	set: (value: number) => {
		emit('update:item', {
			rights: value,
		})
	},
})

const { model: rightsModel } = useMultiSelectEnum(computedRights, Groups.MT4.GroupPermissionFlags)

const closeFIFOHandler = (value: number | null) => {
	if (value === 1) {
		emit('update:item', {
			hedgeProhibited: 1,
		})
	}
}

const tradeSignalItems = enumToItems(Groups.MT4.GroupPermissionFlags, 'Groups.MT4.GroupPermissionFlags', item => [Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_SIGNALS_ALL, Groups.MT4.GroupPermissionFlags.ALLOW_FLAG_SIGNALS_OWN].includes(item.value) ? item : undefined)

tradeSignalItems.unshift({ title: t('Groups.MT4.Crud.Permissions.disable'), value: 0 })

const tradeSignalModel = computed({
	get: () => {
		for (const item of tradeSignalItems) {
			if (rightsModel.value.includes(item.value)) {
				return item.value
			}
		}

		return 0
	},
	set: (value: number) => {
		// remove all trade signals
		const clonedRightsModel = toRaw(useCloned(rightsModel.value).cloned.value)
		tradeSignalItems.forEach((item) => {
			const index = clonedRightsModel.indexOf(item.value)
			if (index !== -1) {
				clonedRightsModel.splice(index, 1)
			}
		})

		// add selected trade signal
		if (value !== 0) {
			clonedRightsModel.push(value)
		}

		rightsModel.value = clonedRightsModel
	},
})
</script>
