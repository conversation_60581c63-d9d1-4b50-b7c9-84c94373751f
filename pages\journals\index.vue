<template>
	<page title="Journals">
		<datatable
			ref="table"
			url="/audit-log"
			:headers="headers"
			search-key="message"
			:default-model="{ fromDate: null, toDate: null }"
			lazyload
			hide-default-footer
		>
			<template #filter="{ model }">
				<api-items
					v-slot="{ items }"
					url="/users/lookup"
				>
					<v-autocomplete
						v-model="model.userId"
						:label="$t('journals.user')"
						:items="items"
						clearable
						item-value="id"
						item-title="name"
					/>
				</api-items>
				<date-time-input
					v-model="model.fromDate"
					:max="model.toDate || now"
					:label="$t('journals.from')"
					clearable
					:time-picker-props="{
						ampmInTitle: true,
					}"
				/>
				<date-time-input
					v-model="model.toDate"
					:label="$t('journals.to')"
					:min="model.fromDate"
					:max="now"
					clearable
					:time-picker-props="{
						ampmInTitle: true,
					}"
				/>
				<v-text-field
					v-model="model.ipAddress"
					v-mask="Mask.IP"
					:label="$t('journals.ip-address')"
					clearable
				/>
			</template>
			<template #item.source="{ value }">
				{{ toCapitalCase(value) }}
			</template>
			<template #item.message="{ item }">
				<v-list-item
					class="px-0"
					min-width="200px"
				>
					<v-list-item-title class="text-wrap">
						{{ item.message }}
					</v-list-item-title>
					<user-info-tooltip
						:id="item.userId"
						@update="usersCrudRef?.update({ id: item.userId })"
					>
						<v-list-item-subtitle>
							{{ item.email }}
						</v-list-item-subtitle>
					</user-info-tooltip>
				</v-list-item>
			</template>
			<template #item.email="{ value, item }">
				<user-info-tooltip
					:id="item.userId"
					@update="usersCrudRef?.update({ id: item.userId })"
				>
					{{ value }}
				</user-info-tooltip>
			</template>
			<template #item.level="{ value }">
				<journals-level :model-value="value" />
			</template>
			<template #item.timestamp="{ value }">
				{{ dayjs.unix(value).format(DateFormat.App) }}
			</template>
			<template #item.filters="{ value }">
				<v-chip-group column>
					<v-chip
						v-for="filterValue, key in (value as Record<string, string>)"
						:key="key"
						small
						rounded
						:ripple="false"
					>
						<span class="text-caption text-no-wrap">
							{{ toSentenceCase(key) }}
						</span>
						<span class="mx-1">:</span>
						<span class="text-caption">
							{{ filterValue }}
						</span>
					</v-chip>
				</v-chip-group>
			</template>
			<template #item.actions="{ item }">
				<v-btn
					v-if="[Journals.HttpAction.POST, Journals.HttpAction.PUT].includes(item.action)"
					prepend-icon="i-mdi:eye"
					variant="text"
					text="Changes"
					:disabled="!item.changes?.length"
					@click="crudRef?.view(item)"
				/>
			</template>
		</datatable>
		<journals-crud ref="crudRef" />
		<usersCrud ref="usersCrudRef" />
	</page>
</template>

<script lang="ts" setup>
import { useChangeCase } from '@vueuse/integrations/useChangeCase'
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type UsersCrud from '~/components/UsersCrud.vue'
import { DateFormat } from '~/types/DateFormat'
import { Mask } from '~/types/Mask'
import JournalsCrud from '~/components/Journals/Crud.vue'
import { Journals } from '~/types'

definePageMeta({
	permission: 'journals.view',
})

const crudRef = ref<InstanceType<typeof JournalsCrud>>()

const usersCrudRef = ref<InstanceType<typeof UsersCrud>>()

const dayjs = useDayjs()

const now = useNow()

const table = ref<typeof Datatable | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: 'Source',
		value: 'source',
	},
	{
		title: 'Message',
		value: 'message',
		minWidth: '150px',
	},
	{
		title: 'Server',
		value: 'tradingServerName',
	},
	{
		title: 'IP Address',
		value: 'ipAddress',
	},
	{
		title: 'Filters',
		value: 'filters',
	},
	{
		title: 'Level',
		value: 'level',
	},
	{
		title: 'Date',
		value: 'timestamp',
		nowrap: true,
	},
	{
		// title: 'Actions',
		value: 'actions',
	},

]

const toSentenceCase = (value: string) => {
	return useChangeCase(value, 'sentenceCase')
}

const toCapitalCase = (value: string) => {
	return useChangeCase(value, 'capitalCase')
}
</script>
