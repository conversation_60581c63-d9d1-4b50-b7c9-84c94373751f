<template>
	<div>
		<page :title="$t('users.users')">
			<template #actions>
				<v-btn
					v-if="can('roles.view')"
					variant="text"
					prepend-icon="i-mdi:account-tag-outline"
					color="primary"
					:to="{ name: 'users-roles' }"
				>
					{{ $t('users.roles') }}
				</v-btn>
				<v-btn
					v-if="can('users.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('users.create-user') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/users"
				:headers="headers"
				search-key="name"
			>
				<template #filter="{ model }:{model:Users.GETQuery}">
					<v-text-field
						v-model="model.email"
						:label="$t('users.email')"
						clearable
					/>
					<api-items
						v-slot="{ items }"
						url="/roles/lookup"
					>
						<v-autocomplete
							v-model="model.roleId"
							:label="$t('users.role')"
							:items="items"
							clearable
							item-value="id"
							item-title="displayName"
						/>
					</api-items>
				</template>
				<template #item.role="{ value }">
					<info-tooltip
						open-on-hover
						:disabled="cannot('roles.view')"
					>
						{{ value.displayName }}

						<template #info>
							<div>
								<b>{{ $t('roles.name') }}:</b> {{ value.name }}
							</div>
							<div>
								<b>{{ $t('roles.display-name') }}:</b> {{ value.displayName }}
							</div>
						</template>

						<template #actions>
							<v-spacer />
							<v-btn
								v-if="can('roles.edit')"
								size="small"
								prepend-icon="i-mdi:pencil"
								variant="text"
								@click="rolesCrudRef?.update(value)"
							>
								{{ $t('actions.edit') }}
							</v-btn>
						</template>
					</info-tooltip>
				</template>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('users.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('users.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<users-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
		<roles-crud
			ref="rolesCrudRef"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type RolesCrud from '~/components/RolesCrud.vue'
import type UsersCrud from '~/components/UsersCrud.vue'
import type { Users } from '~/types'

definePageMeta({
	permission: 'users.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof UsersCrud | null>(null)
const rolesCrudRef = ref<typeof RolesCrud | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('users.name'),
		value: 'name',
	},
	{
		title: t('users.email'),
		value: 'email',
	},
	{
		title: t('users.role'),
		value: 'role',
	},
	{
		title: t('users.date'),
		value: 'timestamp',
	},
	{
		value: 'actions',
	},
]
</script>

<style scoped lang="scss">
.v-sheet{
	&:fullscreen .v-table{
			height: 100%;
	}
	&:fullscreen .v-navigation-drawer{
			left:-320px !important;
			margin:0 !important;
			top:0 !important;
			height: 100% !important;
			padding-bottom: 0 !important;
		&.v-navigation-drawer--active {
			left:0 !important
		}
	}
}
.v-locale--is-rtl .v-sheet{
	&:fullscreen .v-navigation-drawer{
		right:-320px !important;
		left:unset !important;
		&.v-navigation-drawer--active {
			right:0 !important
		}
	}

}
</style>
