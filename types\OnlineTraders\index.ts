import type { ClientToServerEvents, ServerToClientEvents } from '~/types/Common/Socket.IO'

export namespace OnlineTraders {
	export enum Status {
		MultiSessionsCountryMismatch = 0,
		CountryMismatch = 1,
		MultiSessions = 2,
		SingleSession = 3,
	}

	export interface User {
		login: number
		userName: string
		ipAddress: string
		ipCountry: string
		iso: string
		device: string
		time: number
		group: string
		totalSessions: number
		mTAccountCountry: string
		status: Status
		index?: number
	}

	export interface SocketData {
		channel: string
		totalSessions: number
		data: User[]
	}
	export interface Response extends ServerToClientEvents {
		update: (data: SocketData) => void
	}

	export interface Request extends ClientToServerEvents {
		get: (room: string) => void
	}

}
