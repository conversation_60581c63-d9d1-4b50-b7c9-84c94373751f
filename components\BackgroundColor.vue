<template>
	<v-text-field
		ref="textFieldRef"
		class="background-color"
		:model-value="' '"
		readonly
		:append-inner-icon="menuModel? 'i-mdi:menu-up':'i-mdi:menu-down'"
	>
		<div class="d-flex align-center ga-3 overflow-hidden">
			<v-sheet
				width="20"
				height="20"
				border
				rounded="circle"
				:color="(backgroundInternalModel as unknown as string) || 'transparent'"
				class="flex-shrink-0"
			/>
			<div class="text-truncate">
				{{ backgroundInternalModel? backgroundColorName : 'None' }}
			</div>
		</div>

		<v-menu
			v-model="menuModel"
			activator="parent"
			:close-on-content-click="false"
		>
			<v-card
				flat
				width="inherit"
			>
				<v-color-picker
					ref="colorPickerRef"
					v-model="backgroundInternalModel"
					hide-inputs
					show-swatches
					mode="hex"
					:elevation="0"
					min-width="300"
					:width="textFieldRef?.$el?.offsetWidth"
				/>
				<template #actions>
					<v-btn
						:disabled="backgroundInternalModel === undefined"
						@click="removeColor"
					>
						Remove Color
					</v-btn>
					<v-spacer />
					<v-btn @click="closeMenu">
						Close
					</v-btn>
				</template>
			</v-card>
		</v-menu>
	</v-text-field>
</template>

<script lang="ts" setup>
import { VColorPicker, VTextField } from 'vuetify/components'
import { GetColorName } from 'hex-color-to-color-name'

type RGB = [number, number, number]

const NoneColor = 4278190080

const colorPickerRef = ref<InstanceType<typeof VColorPicker> | null>(null)

const swatches = ref<Element[]>([])

const backgroundModel = defineModel('background', {
	type: Number,
})

const backgroundInternalModel = computed({
	get: () => {
		if (backgroundModel.value === NoneColor) {
			return undefined
		}
		return colorRefToHex(backgroundModel.value || 0)
	},
	set: (v) => {
		if (v === undefined) {
			backgroundModel.value = NoneColor
			return
		}
		backgroundModel.value = hexToColorRef(String(v))
	},
})

const colorModel = defineModel('color', {
	type: Number,
})

const colorInternalModel = computed({
	get: () => colorRefToHex(colorModel.value || 0),
	set: (v) => {
		colorModel.value = hexToColorRef(String(v))
	},
})

watch(() => colorInternalModel.value, (v) => {
	if (v) {
		colorInternalModel.value = bestFontColor(v)
	}
})

const backgroundColorName = computed(() => backgroundInternalModel.value ? GetColorName(backgroundInternalModel.value) : '')

const textFieldRef = ref<InstanceType<typeof VTextField> | null>(null)

const menuModel = ref(false)

function colorRefToHex(colorRef: number) {
	// Extract the red, green, and blue components from the COLORREF integer.
	const red = colorRef & 0xFF // Masking with 0xFF gives us the least significant byte
	const green = (colorRef >> 8) & 0xFF // Shifting right by 8 bits, then masking
	const blue = (colorRef >> 16) & 0xFF // Shifting right by 16 bits, then masking

	// Convert each component to a two-digit hex string and concatenate them.
	const hex = '#'
		+ red.toString(16).padStart(2, '0')
		+ green.toString(16).padStart(2, '0')
		+ blue.toString(16).padStart(2, '0')

	return hex
}

function hexToColorRef(hex: string) {
	// Remove the hash at the beginning if it's there
	hex = hex.replace(/^#/, '')

	// Parse r, g, and b values from the hex string
	const bigint = Number.parseInt(hex, 16)
	const r = (bigint >> 16) & 255
	const g = (bigint >> 8) & 255
	const b = bigint & 255

	return (b << 16) | (g << 8) | r
}

function hexToRgb(hex: string): RGB {
	// Remove the hash at the beginning if it's there
	hex = hex.replace(/^#/, '')

	// Parse r, g, and b values from the hex string
	const bigint = Number.parseInt(hex, 16)
	const r = (bigint >> 16) & 255
	const g = (bigint >> 8) & 255
	const b = bigint & 255

	return [r, g, b]
}

function getLuminance(r: number, g: number, b: number): number {
	const channelLuminance = (channel: number): number => {
		channel /= 255.0
		return channel <= 0.03928 ? channel / 12.92 : Math.pow((channel + 0.055) / 1.055, 2.4)
	}

	return 0.2126 * channelLuminance(r) + 0.7152 * channelLuminance(g) + 0.0722 * channelLuminance(b)
}

function getContrastRatio(l1: number, l2: number): number {
	return l1 > l2 ? (l1 + 0.05) / (l2 + 0.05) : (l2 + 0.05) / (l1 + 0.05)
}

function bestFontColor(hexColor: string): string {
	const [r, g, b] = hexToRgb(hexColor)

	const luminanceBg = getLuminance(r, g, b)

	const luminanceWhite = getLuminance(255, 255, 255)
	const luminanceBlack = getLuminance(0, 0, 0)

	const contrastWithWhite = getContrastRatio(luminanceBg, luminanceWhite)
	const contrastWithBlack = getContrastRatio(luminanceBg, luminanceBlack)

	return contrastWithWhite >= contrastWithBlack ? '#ffffff' : '#000000'
}

const closeMenu = () => {
	menuModel.value = false
}

watch(() => menuModel.value, (isVisible) => {
	nextTick(() => {
		if (isVisible) {
			const elements = (colorPickerRef.value?.$el as HTMLElement).getElementsByClassName('v-color-picker-swatches__color')
			// close on elements click
			swatches.value = Array.from(elements)
			swatches.value.forEach((element) => {
				element.addEventListener('click', closeMenu)
			})
		} else {
			swatches.value.forEach((element) => {
				element.removeEventListener('click', closeMenu)
			})
		}
	})
})

const removeColor = () => {
	backgroundInternalModel.value = undefined
	closeMenu()
}
</script>

<style lang="scss">
.background-color{
	input{
		display: none;
	}
}
</style>
