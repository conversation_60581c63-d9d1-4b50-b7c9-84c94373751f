<template>
	<teleport
		defer
		to="#main-layout"
	>
		<v-navigation-drawer
			ref="crudRef"
			v-model="drawerModel"
			tag="div"
			width="700"
			location="end"
			temporary
			order="0"
			v-bind="$attrs"
		>
			<template #prepend>
				<v-card-title class="d-flex align-center">
					{{ title }}
					<v-spacer />

					<v-btn
						variant="plain"
						icon="i-mdi-close"
						color="rgb(var(--v-theme-on-background))"
						@click="close()"
					/>
				</v-card-title>
				<v-card-subtitle>
					Changes ({{ transformedChangedItems.length }})
				</v-card-subtitle>
			</template>
			<v-card-text>
				<v-treeview
					:items="transformedChangedItems"
					open-on-click
				>
					<template
						#append="{ item }"
					>
						<div v-if="!item.children && typeof item.value === 'object'">
							<template v-if="record!.action === Journals.HttpAction.PUT">
								<v-chip color="warning">
									<template v-if="item.value.old === ''">
										<i>empty</i>
									</template>
									<template v-else>
										{{ item.value.old }}
									</template>
								</v-chip>
								<v-icon
									start
									end
									icon="i-material-symbols-light:line-end-arrow-notch-rounded"
								/>
							</template>
							<v-chip
								color="success"
							>
								<template v-if="item.value.new === ''">
									<i>empty</i>
								</template>
								<template v-else>
									{{ item.value.new }}
								</template>
							</v-chip>
						</div>
					</template>
				</v-treeview>
			</v-card-text>
		</v-navigation-drawer>
	</teleport>
</template>

<script lang="ts" setup>
import type { VNavigationDrawer } from 'vuetify/components'
import { VTreeview } from 'vuetify/labs/VTreeview'
import { Journals } from '~/types'

const drawerModel = ref(false)

const crudRef = ref<InstanceType<typeof VNavigationDrawer> | null>(null)

const record = ref<Journals.Record>()

const title = computed(() => record.value?.message)

const close = () => {
	drawerModel.value = false
}

type transformedItem = {
	title: string
	value: {
		old: any
		new: any
	} | string
	children?: transformedItem[]
}

const transformedChangedItems = computed<transformedItem[]>(() => {
	if (!record.value) return []

	return transformArray(record.value.changes)
})

function transformArray(data: Journals.Record['changes']): transformedItem[] {
	const result: transformedItem[] = []

	for (const item of data) {
		const keys = item.name.split('.')
		let parent = result
		let path = ''

		for (let i = 0; i < keys.length; i++) {
			const key = keys[i]
			path += (path ? '.' : '') + key
			let existing = parent.find(x => x.title === key)

			if (!existing) {
				existing = {
					title: key,
					value: path,
					children: [],
				}
				parent.push(existing)
			}

			if (i === keys.length - 1) {
				existing.value = {
					old: item.oldValue,
					new: item.newValue,

				}

				delete existing.children
			}

			parent = existing.children || []
		}
	}

	return result
}

defineExpose({
	// create: () => crudRef.value?.create(),
	// update: (item: any) => crudRef.value?.update(item),
	// delete: (item: any) => crudRef.value?.delete(item),
	view: (payload: any) => {
		record.value = payload

		drawerModel.value = true
	},
})
</script>
