<template>
	<v-form
		ref="formRef"
		@submit.prevent="submit"
	>
		<page :title="$t('manage-access.manage-access')">
			<v-container fluid>
				<v-row>
					<v-col v-bind="firstColProps">
						<v-list-item prepend-icon="i-mdi:file-plus">
							<v-list-item-title>
								{{ $t('manage-access.allowed-roles') }}
							</v-list-item-title>
							<v-list-item-subtitle class="text-medium-emphasis">
								{{ $t('manage-access.allowed-roles-are-roles-that-are-allowed-to-access') }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-col>
					<v-col v-bind="secondColProps">
						<v-skeleton-loader
							type="list-item-two-line@3"
							:loading="pending"
						>
							<roles-panels
								v-model="form.allowedRoles"
								v-model:current-panel="allowedRolesExpansionModel"
								:disabled="cannot('transactionWorkflow.edit')"
								:sortable="false"
							>
								<template #title="{ item }">
									<find-item-by-id
										:id="item"
										v-slot="{ item: selectedItem }"
										:items="rolesData"
									>
										{{ selectedItem.displayName }}
									</find-item-by-id>
								</template>
								<template #default="{ model }">
									<v-autocomplete
										v-model="model.value"
										item-title="displayName"
										item-value="id"
										:label="$t('manage-access.roles')"
										:placeholder="$t('manage-access.select-role')"
										:items="allowedRolesItems"
										:rules="rules({ required: true })"
									/>
								</template>
							</roles-panels>
						</v-skeleton-loader>
						<div class="text-end my-4">
							<v-btn
								v-if="can('transactionWorkflow.edit')"
								variant="text"
								:disabled="isAddAllowedDisabled"
								prepend-icon="i-mdi:plus"
								size="small"
								@click="addAllowedRole"
							>
								{{ $t('manage-access.add-allow-role') }}
							</v-btn>
						</div>
					</v-col>
				</v-row>
				<v-divider
					inset
					class="my-4"
				/>
				<v-row>
					<v-col v-bind="firstColProps">
						<v-list-item prepend-icon="i-mdi:file-eye">
							<v-list-item-title>
								{{ $t('manage-access.review-roles') }}
							</v-list-item-title>
							<v-list-item-subtitle class="text-medium-emphasis">
								{{ $t('manage-access.review-roles-description') }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-col>
					<v-col v-bind="secondColProps">
						<v-skeleton-loader
							type="list-item-two-line@3"
							:loading="pending"
						>
							<roles-panels
								v-model="form.reviewRoles"
								v-model:current-panel="reviewRolesCurrentPanel"
								:disabled="cannot('transactionWorkflow.edit')"
							>
								<template #title="{ item }">
									<find-item-by-id
										:id="item"
										v-slot="{ item: selectedItem }:{item:Roles.Lookup.SingleRecord}"
										:items="rolesData"
									>
										{{ selectedItem.name }}
									</find-item-by-id>
								</template>
								<template #default="{ model }">
									<v-autocomplete
										v-model="model.value"
										:placeholder="$t('manage-access.select-role')"

										item-title="name"
										item-value="id"
										:items="reviewRolesItems"
										:rules="rules({ required: true })"
									/>
								</template>
							</roles-panels>
						</v-skeleton-loader>
						<div class="text-end my-4">
							<v-btn
								v-if="can('transactionWorkflow.edit')"
								:disabled="isAddReviewDisabled"
								variant="text"
								prepend-icon="i-mdi:plus"
								size="small"
								@click="addReviewRole"
							>
								{{ $t('manage-access.add-review-role') }}
							</v-btn>
						</div>
					</v-col>
				</v-row>
				<v-divider
					inset
					class="my-4"
				/>
				<v-row>
					<v-col v-bind="firstColProps">
						<v-list-item prepend-icon="i-mdi:file-check">
							<v-list-item-title>
								{{ $t('manage-access.approval-roles') }}
							</v-list-item-title>
							<v-list-item-subtitle class="text-medium-emphasis">
								{{ $t('manage-access.approval-roles-description') }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-col>
					<v-col v-bind="secondColProps">
						<v-skeleton-loader
							type="list-item-two-line@3"
							:loading="pending"
						>
							<roles-panels
								v-model="form.approvalRoles"
								v-model:current-panel="approvalRolesCurrentPanel"
								:disabled="cannot('transactionWorkflow.edit')"
							>
								<template #title="{ item }:{item:ElementOf<typeof form.approvalRoles>}">
									<find-item-by-id
										:id="item.roleId"
										v-slot="{ item: selectedItem }:{item:Roles.Lookup.SingleRecord}"
										:items="rolesData"
									>
										{{ selectedItem.name }}
										<v-chip-group>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1"> {{ $t('manage-access.limit') }} </span>{{ item.transactionAmountLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1"> {{ $t('manage-access.daily') }} </span>{{ item.transactionDailyLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1"> {{ $t('manage-access.monthly') }} </span>{{ item.transactionMonthlyLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1"> {{ $t('manage-access.yearly') }} </span>{{ item.transactionYearlyLimit || 'Not Set' }}
											</v-chip>
										</v-chip-group>
									</find-item-by-id>
								</template>
								<template #default="{ model }:{model:Ref<ElementOf<typeof form.approvalRoles>>}">
									<v-row dense>
										<v-col>
											<v-autocomplete
												v-model="model.value.roleId"
												:placeholder="$t('manage-access.select-role')"
												item-title="displayName"
												item-value="id"
												:items="approvalRolesItems"
												:rules="rules({ required: true })"
												hide-details="auto"
											/>
										</v-col>
									</v-row>
									<v-row>
										<v-col>
											<v-text-field
												v-model="model.value.transactionAmountLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-amount-limit')"
											/>
										</v-col>
										<v-col>
											<v-text-field
												v-model="model.value.transactionDailyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-daily-limit')"
											/>
										</v-col>
									</v-row>
									<v-row>
										<v-col>
											<v-text-field
												v-model="model.value.transactionMonthlyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-monthly-limit')"
											/>
										</v-col>
										<v-col>
											<v-text-field
												v-model="model.value.transactionYearlyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-yearly-limit')"
											/>
										</v-col>
									</v-row>
								</template>
							</roles-panels>
						</v-skeleton-loader>
						<div class="text-end my-4">
							<v-btn
								v-if="can('transactionWorkflow.edit')"
								:disabled="isAddApprovalDisabled"
								variant="text"
								prepend-icon="i-mdi:plus"
								size="small"
								@click="addApprovalRole"
							>
								{{ $t('manage-access.add-approval-role') }}
							</v-btn>
						</div>
					</v-col>
				</v-row>
				<v-divider
					inset
					class="my-4"
				/>
				<v-row>
					<v-col v-bind="firstColProps">
						<v-list-item prepend-icon="i-mdi:account-key">
							<v-list-item-title>
								{{ $t('manage-access.limitation-by-user') }}
							</v-list-item-title>
							<v-list-item-subtitle class="text-medium-emphasis">
								{{ $t('manage-access.limitation-by-user-description') }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-col>
					<v-col v-bind="secondColProps">
						<v-skeleton-loader
							type="list-item-two-line@3"
							:loading="pending"
						>
							<roles-panels
								v-model="form.approvalUsers"
								v-model:current-panel="approvalUsersCurrentPanel"
								:disabled="cannot('transactionWorkflow.edit')"
							>
								<template #title="{ item }">
									<find-item-by-id
										:id="item.userId"
										v-slot="{ item: selectedItem }:{item:Users.Lookup.SingleRecord}"
										:items="usersData"
									>
										{{ selectedItem.name }}
										<v-chip-group>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1">Limit:</span>{{ item.transactionAmountLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1">Daily:</span>{{ item.transactionDailyLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1">Monthly:</span>{{ item.transactionMonthlyLimit || 'Not Set' }}
											</v-chip>
											<v-chip
												size="x-small"
												rounded
											>
												<span class="me-1">Yearly:</span>{{ item.transactionYearlyLimit || 'Not Set' }}
											</v-chip>
										</v-chip-group>
									</find-item-by-id>
								</template>
								<template #default="{ model }:{model:Ref<ElementOf<typeof form.approvalUsers>>}">
									<v-row dense>
										<v-col>
											<v-autocomplete
												v-model="model.value.userId"
												:placeholder="$t('manage-access.select-user')"
												item-title="name"
												item-value="id"
												:items="approvalUsersItems"
												:rules="rules({ required: true })"
												hide-details="auto"
											/>
										</v-col>
									</v-row>
									<v-row>
										<v-col>
											<v-text-field
												v-model="model.value.transactionAmountLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-amount-limit')"
											/>
										</v-col>
										<v-col>
											<v-text-field
												v-model="model.value.transactionDailyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-daily-limit')"
											/>
										</v-col>
									</v-row>
									<v-row>
										<v-col>
											<v-text-field
												v-model="model.value.transactionMonthlyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-monthly-limit')"
											/>
										</v-col>
										<v-col>
											<v-text-field
												v-model="model.value.transactionYearlyLimit"
												type="number"
												hide-details="auto"
												:label="$t('manage-access.transaction-yearly-limit')"
											/>
										</v-col>
									</v-row>
								</template>
							</roles-panels>
						</v-skeleton-loader>
						<div class="text-end my-4">
							<v-btn
								v-if="can('transactionWorkflow.edit')"
								:disabled="isAddApprovalUsersDisabled"
								variant="text"
								prepend-icon="i-mdi:plus"
								size="small"
								@click="addApprovalUsers"
							>
								{{ $t('manage-access.add-user') }}
							</v-btn>
						</div>
					</v-col>
				</v-row>
				<v-divider
					inset
					class="my-4"
				/>
				<v-row>
					<v-col v-bind="firstColProps">
						<v-list-item prepend-icon="i-mdi:account-alert">
							<v-list-item-title>
								{{ $t('manage-access.fallback-role') }}
							</v-list-item-title>
							<v-list-item-subtitle class="text-medium-emphasis">
								{{ $t('manage-access.fallback-role-description') }}
							</v-list-item-subtitle>
						</v-list-item>
					</v-col>
					<v-col v-bind="secondColProps">
						<v-list-item
							class="border"
							rounded="lg"
							:disabled="cannot('transactionWorkflow.edit')"
						>
							<v-select
								v-model="form.fallbackRole"
								variant="solo"
								flat
								:placeholder="$t('manage-access.select-role')"
								:items="fallbackRoleItems"
								item-title="displayName"
								item-value="id"
								density="default"
								hide-details="auto"
								:rules="rules({ required: true })"
							/>
						</v-list-item>
					</v-col>
				</v-row>
				<v-divider
					inset
					class="my-4"
				/>
			</v-container>

			<template #bottom-actions>
				<v-container fluid>
					<v-row>
						<v-col class="d-flex">
							<v-spacer />
							<v-btn
								color="secondary"
								variant="text"
								@click="returnToActions"
							>
								{{ $t('manage-access.cancel') }}
							</v-btn>
							<v-btn
								v-if="can('transactionWorkflow.edit')"
								variant="text"
								:loading="isSubmitting"
								type="submit"
							>
								{{ $t('manage-access.save') }}
							</v-btn>
						</v-col>
					</v-row>
				</v-container>
			</template>
		</page>
	</v-form>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import type { ElementOf } from '~/types/Helpers'
import { Actions, Users, Roles } from '~/types'

definePageMeta({
	permission: 'transactionWorkflow.view',
})

const { t } = useI18n()

const route = useRoute('transactions-workflow-actions-actionId-manage-access')

const { errorSnackbar, successSnackbar } = useSnackbar()

const formRef = ref<InstanceType<typeof VForm> | null>(null)

const isSubmitting = ref(false)

const { data, error, pending } = useApi<Actions._Id.Workflow.GETResponse>(Actions._Id.Workflow.URL(route.params.actionId))

watch(() => data.value, (value) => {
	if (value) {
		form.allowedRoles = value.allowedRoles
		form.reviewRoles = value.reviewRoles
		form.approvalRoles = value.approvalRoles.map(item => ({
			roleId: item.roleId,
			transactionAmountLimit: item.transactionAmountLimit || null,
			transactionDailyLimit: item.transactionDailyLimit || null,
			transactionMonthlyLimit: item.transactionMonthlyLimit || null,
			transactionYearlyLimit: item.transactionYearlyLimit || null,
		})) as Actions._Id.Workflow.PUTRequest['approvalRoles']
		form.approvalUsers = (value.approvalUsers as Actions._Id.Workflow.PUTRequest['approvalUsers']).map(item => ({
			userId: item.userId,
			transactionAmountLimit: item.transactionAmountLimit || null,
			transactionDailyLimit: item.transactionDailyLimit || null,
			transactionMonthlyLimit: item.transactionMonthlyLimit || null,
			transactionYearlyLimit: item.transactionYearlyLimit || null,
		})) as Actions._Id.Workflow.PUTRequest['approvalUsers']
		form.fallbackRole = value.fallbackRole
	}
})

type NullableArrayOfRecords<T> = {
	[P in keyof T]: T[P] extends Record<string, any> ? {
		[K in keyof T[P]]: T[P][K] | null
	} : T[P];
}

interface UpdateWorkflowForm {
	allowedRoles: Array<number | null>
	reviewRoles: Array<number | null>
	approvalRoles: NullableArrayOfRecords<Actions._Id.Workflow.PUTRequest['approvalRoles']>
	approvalUsers: NullableArrayOfRecords<Actions._Id.Workflow.PUTRequest['approvalUsers']>
	fallbackRole: number | null
}

const form = reactive <UpdateWorkflowForm>({
	allowedRoles: [],
	reviewRoles: [],
	approvalRoles: [],
	approvalUsers: [],
	fallbackRole: null,
})

if (error.value) {
	errorSnackbar(error.value.message)
}
const { data: rolesData /* error: rolesError, refresh: rolesRefresh */ } = useApi<Roles.Lookup.GETResponse>(Roles.Lookup.URL)
const { data: usersData /* error: rolesError, refresh: rolesRefresh */ } = useApi<Users.Lookup.GETResponse>(Users.Lookup.URL, {
	params: {
		roleIds: computed(() => form.approvalRoles.map(r => r.roleId).join(',')),
	},
})

const firstColProps = ref({
	cols: 12,
	md: 4,
})

const secondColProps = ref({
	offsetMd: 2,
})

/* START OF ALLOWED ROLES */

const allowedRolesExpansionModel = ref <number | null>(null)

// const allowedRoles = ref <Array<number|null>>(data.value?.allowedRoles || [])

const allowedRolesItems = computed(() => rolesData.value?.map((role) => {
	const disabled = form.allowedRoles.includes(role.id)
	return {
		...role,
		props: {
			disabled,
			subtitle: disabled ? t('manage-access.already-in-use') : undefined,
		},
	}
}) || [])

const addAllowedRole = () => {
	const length = form.allowedRoles.push(null)
	allowedRolesExpansionModel.value = length - 1
}

const isAddAllowedDisabled = computed(() => (((rolesData.value?.length ?? 0) - form.allowedRoles.length) <= 0 || typeof allowedRolesExpansionModel.value === 'number'))

/* END OF ALLOWED ROLES */

/* START OF REVIEW ROLES */

// const reviewRoles = ref <Array<number|null>>([9])

const reviewRolesCurrentPanel = ref <number | null>(null)

const reviewRolesItems = computed(() => rolesData.value?.map((role) => {
	const disabled = form.reviewRoles.includes(role.id)
	return {
		...role,
		props: {
			disabled,
			subtitle: disabled ? t('manage-access.already-in-use') : undefined,
		},
	}
}) || [])

const addReviewRole = () => {
	const length = form.reviewRoles.push(null)
	reviewRolesCurrentPanel.value = length - 1
}

const isAddReviewDisabled = computed(() => (((rolesData.value?.length ?? 0) - form.reviewRoles.length) <= 0 || typeof reviewRolesCurrentPanel.value === 'number'))

/* END OF REVIEW ROLES */

/* START OF APPROVAL ROLES */

// const approvalRoles = ref<Array<any>>([{ id: 1 }, { id: 2 }, { id: 3 }])

const approvalRolesCurrentPanel = ref <number | null>(null)

const approvalRolesItems = computed(() => rolesData.value?.map((role) => {
	const disabled = form.approvalRoles.map(r => r.roleId).includes(role.id)
	return {
		...role,
		props: {
			disabled,
			subtitle: disabled ? t('manage-access.already-in-use') : undefined,
		},
	}
}) || [])

const addApprovalRole = () => {
	const length = form.approvalRoles.push({ roleId: null, transactionAmountLimit: null, transactionDailyLimit: null, transactionMonthlyLimit: null, transactionYearlyLimit: null })
	approvalRolesCurrentPanel.value = length - 1
}

const isAddApprovalDisabled = computed(() => (((rolesData.value?.length ?? 0) - form.approvalRoles.length) <= 0 || typeof approvalRolesCurrentPanel.value === 'number'))

/* END OF APPROVAL ROLES */

/* START OF LIMITATION BY USER */

// const limitationByUser = ref <Array<any>>([{ id: 1 }, { id: 2 }, { id: 3 }])

const approvalUsersCurrentPanel = ref <number | null>(null)

const approvalUsersItems = computed(() => usersData.value?.map((user) => {
	const disabled = form.approvalUsers.map(u => u.userId).includes(user.id)
	return {
		...user,
		props: {
			disabled,
			subtitle: disabled ? t('manage-access.already-in-use') : undefined,
		},
	}
}) || [])

const addApprovalUsers = () => {
	const length = form.approvalUsers.push({ userId: null, transactionAmountLimit: null, transactionDailyLimit: null, transactionMonthlyLimit: null, transactionYearlyLimit: null })
	approvalUsersCurrentPanel.value = length - 1
}

const isAddApprovalUsersDisabled = computed(() => (((rolesData.value?.length ?? 0) - form.approvalUsers.length) <= 0 || typeof approvalUsersCurrentPanel.value === 'number'))

/* END OF LIMITATION BY USER */

/* START OF FALLBACK ROLE */

// const fallbackRole = ref <number|null>(null)

const fallbackRoleItems = computed(() => rolesData.value || [])

/* END OF FALLBACK ROLE */

const submit = async () => {
	const isValid = await formRef.value?.validate()

	if (!isValid?.valid) {
		return
	}

	const { $api } = useNuxtApp()

	const clonedForm = useCloned(form).cloned.value
	const normalizedForm = {
		...clonedForm,
		allowedRoles: clonedForm.allowedRoles.filter(role => role !== null) as Array<number>,
		reviewRoles: clonedForm.reviewRoles.filter(role => role !== null) as Array<number>,
		approvalRoles: clonedForm.approvalRoles.filter(role => role.roleId !== null) as Array<Actions._Id.Workflow.PUTRequest['approvalRoles'][number]>,
		approvalUsers: clonedForm.approvalUsers.filter(user => user.userId !== null) as Array<Actions._Id.Workflow.PUTRequest['approvalUsers'][number]>,
		fallbackRole: clonedForm.fallbackRole || null,
	}

	isSubmitting.value = true
	$api(`/actions/${route.params.actionId}/workflow`, {
		method: 'PUT',
		body: normalizedForm,
	})
		.then(() => {
			successSnackbar(t('manage-access.workflow-has-been-saved-successfully'))
			returnToActions()
		})
		.catch((e) => {
			errorSnackbar(e.message || t('manage-access.an-error-occurred-while-saving-the-workflow'))
		})
		.finally(() => {
			isSubmitting.value = false
		})
}

const returnToActions = () => {
	const { $router } = useNuxtApp()

	$router.push({ name: 'transactions-workflow-actions' })
}
</script>
