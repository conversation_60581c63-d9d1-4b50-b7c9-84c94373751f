<template>
	<v-list-item
		rounded="lg"
		link
		lines="one"
		:title="title"
		:subtitle="subtitle"
		append-icon="i-mdi-chevron-down "
		max-width="300"
		v-bind="props"
	>
		<v-menu
			v-model="menuModel"
			activator="parent"
			open-on-hover
			open-on-click
			open-delay="300"
			close-delay="300"
			:close-on-content-click="display.mdAndUp.value"
		>
			<v-list
				slim
				density="compact"
			>
				<v-list-subheader class="hidden-md-and-up">
					{{ $t('UserBtn.settings') }}
				</v-list-subheader>
				<v-list-item
					prepend-icon="i-mdi:theme-light-dark "
					class="hidden-md-and-up"
				>
					<v-list-item-title class="me-8">
						{{ $t('UserBtn.dark-theme') }}
					</v-list-item-title>
					<template #append>
						<v-switch
							v-model="themeCookie"
							density="compact"
							color="success"
							true-value="dark"
							false-value="light"
							true-icon="i-mdi:weather-night"
							false-icon="i-mdi:weather-sunny"
							hide-details
							@update:model-value="themeHandler($event as Theme)"
						/>
					</template>
				</v-list-item>

				<v-list-item
					v-if="$vuetify.display.smAndDown"
					class="hidden-md-and-up"
					prepend-icon="i-arcticons:metatrader-5"
				>
					<v-list-item-title>
						{{ $t('UserBtn.server') }}
					</v-list-item-title>
					<template #append>
						<api-items
							v-slot="props"
							url="/my/trading-servers"
						>
							<v-select
								v-bind="props"
								v-model="tradingServerName"
								density="compact"
								flat
								variant="solo"
								item-title="displayName"
								item-value="name"
								hide-details
								class="me-n4"
								:menu-props="{ minWidth: 550 }"
							>
								<template #item="{ props: selectProps, item }">
									<v-list-item
										lines="two"
										v-bind="selectProps"
									>
										<v-list-item-subtitle>
											{{ item.raw.ipAddress }}:{{ item.raw.port }}
										</v-list-item-subtitle>
										<template #append>
											{{ item.raw.tradingPlatform?.displayName }}
										</template>
									</v-list-item>
								</template>
							</v-select>
						</api-items>
					</template>
				</v-list-item>
				<v-divider class="my-2 hidden-md-and-up" />
				<v-list-item
					prepend-icon="i-mdi:security-account-outline "
					:to="{ name: 'my-security' }"
					:title="$t('UserBtn.account-security')"
					append-icon="i-mdi-chevron-right rtl-flip"
					@click="closeMenu"
				/>
				<v-list-item
					prepend-icon="i-mdi:password"
					:to="{ name: 'my-change-password' }"
					:title="$t('UserBtn.change-password')"
					append-icon="i-mdi-chevron-right rtl-flip"
					@click="closeMenu"
				/>

				<v-list-item
					prepend-icon="i-mdi:logout"
					:title="$t('UserBtn.logout')"
					append-icon="i-mdi-chevron-right rtl-flip"
					@click="logout"
				/>
				<v-divider class="ma-2" />
				<v-list-item
					prepend-icon="i-mdi:information-variant"
					:title="$t('UserBtn.about')"
					append-icon="i-mdi-chevron-right rtl-flip"
					@click="aboutModel = true"
				/>
			</v-list>
		</v-menu>
		<about-dialog v-model="aboutModel" />
	</v-list-item>
</template>

<script lang="ts" setup>
import { VList } from 'vuetify/components'

const menuModel = ref(false)

const aboutModel = ref(false)

const display = useDisplay()

const authStore = useAuthStore()

const title = computed(() => {
	return authStore.user?.name
})

const subtitle = computed(() => {
	return display.mdAndUp.value ? authStore.user?.email : ''
})

const themeCookie = useThemeCookie()

const prefStore = usePreferencesStore()

const themeHandler = (theme: Theme) => {
	prefStore.updateKey('theme', theme)
}

const tradingServerName = computed({
	get: () => prefStore.getKey('tradingServerName'),
	set: value => prefStore.updateKey('tradingServerName', value),
})

const closeMenu = () => {
	menuModel.value = false
}

const logout = async () => {
	await authStore.logout()
	closeMenu()
}

const { props } = useKeepMenuVisible(menuModel)
</script>
