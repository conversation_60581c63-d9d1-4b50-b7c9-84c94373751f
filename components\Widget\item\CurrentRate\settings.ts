import type { WidgetItemSettings } from '../../types'
// import type { Permissions } from '~/types/Permissions'

export type Data = {
	symbol: string
}

const TopVolumeLots: WidgetItemSettings<Data> = {

	dimensions: {
		w: 4,
		h: 3,
		minW: 2,
		minH: 3,
		maxW: undefined,
		maxH: undefined,
		resizable: true,
		preserveAspectRatio: false,
	},

	data: {
		symbol: 'EURUSD',
	},

	permission: undefined,
}

export default TopVolumeLots
