<template>
	<v-container>
		<v-toolbar
			density="compact"
			color="surface"
		>
			<v-spacer />
			<v-btn
				variant="text"
				prepend-icon="i-mdi:plus"
				@click="setForEdit(-1)"
			>
				{{ $t('Groups.MT5.Crud.Commissions.add-commission') }}
			</v-btn>
		</v-toolbar>
		<v-table
			class="draggable-rows"
			hover
		>
			<thead>
				<tr>
					<th>{{ $t('Groups.MT5.Crud.Commissions.name') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Commissions.symbol') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Commissions.type') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Commissions.range') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Commissions.charge') }}</th>
					<th />
				</tr>
			</thead>
			<tbody
				ref="tbodyRef"
				v-auto-animate
			>
				<tr
					v-for="(item, index) in item.commissions"
					:key="item.path"
					@dblclick="setForEdit(index)"
				>
					<td>{{ item.name }}</td>
					<td>{{ item.path }}</td>
					<td>
						<find-item-by-id
							:id="item.mode"
							v-slot="{ item: selectedItem }"
							item-key="value"
							:items="modeItems"
						>
							{{ selectedItem.title }}
						</find-item-by-id>
					</td>
					<td>
						<find-item-by-id
							:id="item.rangeMode"
							v-slot="{ item: selectedItem }"
							item-key="value"
							:items="rangeModeItems"
						>
							{{ selectedItem.title }}
						</find-item-by-id>
					</td>
					<td>
						<find-item-by-id
							:id="item.chargeMode"
							v-slot="{ item: selectedItem }"
							item-key="value"
							:items="chargeModeItems"
						>
							{{ selectedItem.title }}
						</find-item-by-id>
					</td>
					<td class="d-flex">
						<v-btn
							variant="text"
							size="small"
							icon="i-mdi:trash-can"
							@click="remove(index)"
						/>
						<v-btn
							variant="text"
							size="small"
							icon="i-mdi:pencil"
							@click="setForEdit(index)"
						/>
						<v-btn
							variant="text"
							class="handle"
							size="small"
							icon="i-mdi:drag"
						/>
					</td>
				</tr>
				<tr v-if="!item.commissions.length">
					<td
						colspan="6"
						class="text-center"
					>
						{{ $t('$vuetify.noDataText') }}
					</td>
				</tr>
			</tbody>
		</v-table>
		<v-dialog
			v-model="editDialogModel"
			persistent
			max-width="700"
			@keydown.esc.stop
		>
			<v-confirm-edit
				v-slot="{ actions, model }"
				v-model="currentCommission"
				cancel-text="Reset"
				@save="save"
			>
				<v-form ref="editFormRef">
					<v-card :title="dialogTitle">
						<template #text>
							<groups-crud-mt5-commission-form v-model="model.value" />
						</template>

						<template #actions>
							<v-btn @click="editDialogModel = false">
								{{ $t('Groups.MT5.Crud.Commissions.cancel') }}
							</v-btn>
							<v-spacer />
							<component :is="actions" />
						</template>
					</v-card>
				</v-form>
			</v-confirm-edit>
		</v-dialog>
	</v-container>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import type { Groups } from '~/types'
import { EnCommMode, EnCommRangeMode, EnCommChargeMode, EnCommEntryMode, EnCommActionMode, EnCommProfitMode, EnCommReasonFlags } from '~/types/Groups/MT5/Commission'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const defaultItem: Groups.MT5.Commission = {
	name: '',
	description: '',
	path: '*',
	mode: EnCommMode.COMM_STANDARD,
	rangeMode: EnCommRangeMode.COMM_RANGE_VOLUME,
	chargeMode: EnCommChargeMode.COMM_CHARGE_DAILY,
	turnoverCurrency: '',
	entryMode: EnCommEntryMode.COMM_ENTRY_ALL,
	actionMode: EnCommActionMode.COMM_ACTION_ALL,
	profitMode: EnCommProfitMode.COMM_PROFIT_ALL,
	reasonMode: EnCommReasonFlags.COMM_REASON_FLAG_ALL,
	tiers: [],
}

const currentIndex = ref(-1)

const currentCommission = ref(defaultItem)

const editDialogModel = ref(false)

const editFormRef = ref<InstanceType<typeof VForm>>()

const confirm = useNuxtApp().$confirm

const { t } = useI18n()

const dialogTitle = computed(() => currentIndex.value === -1 ? t('Groups.MT5.Crud.Commissions.add-commission-0') : t('Groups.MT5.Crud.Commissions.edit-commission-currentco', [currentCommission.value?.path]))

const setForEdit = (index: number) => {
	currentIndex.value = index
	if (index === -1) {
		currentCommission.value = useCloned(defaultItem).cloned.value
	} else {
		currentCommission.value = useCloned(p.item.commissions[index]).cloned.value
	}
	editDialogModel.value = true
}

const modeItems = enumToItems(EnCommMode, 'Groups.MT5.EnCommMode')

const rangeModeItems = enumToItems(EnCommRangeMode, 'Groups.MT5.EnCommRangeMode')

const chargeModeItems = enumToItems(EnCommChargeMode, 'Groups.MT5.EnCommChargeMode')

const remove = (index: number) => {
	confirm(t('Groups.MT5.Crud.Commissions.are-you-sure-you-want-to-remove')).then(() => {
		const commissions = useCloned(p.item.commissions).cloned.value
		commissions.splice(index, 1)
		emit('update:item', {
			commissions,
		})
	}).catch(() => {})
}

const save = async (updatedCommission: Groups.MT5.Commission) => {
	const validation = await editFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	if (currentIndex.value === -1) {
		emit('update:item', {
			commissions: [...p.item.commissions, updatedCommission],
		})
	} else {
		const commissions = useCloned(p.item.commissions).cloned.value

		commissions[currentIndex.value] = updatedCommission

		emit('update:item', { commissions })
	}

	editDialogModel.value = false
}
</script>
