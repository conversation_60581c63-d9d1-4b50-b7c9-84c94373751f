<template>
	<crud
		ref="crudRef"
		:default-item="defaultCategoryItem"
		item-name="Symbol"
		item-identity="name"
		:navigation-drawer-props="{ width: 500 }"
		:url="CategoriesSymbols._Id.URL(':id')"
		v-bind="$attrs"
	>
		<template #title="{}">
			Assign Symbol to Category
		</template>
		<template #default="{ item, errors }: { item: Item<CategoriesSymbols._Id.GETResponse | CategoriesSymbols._Id.POSTRequest | CategoriesSymbols._Id.PUTRequest>, errors: ItemErrors<CategoriesSymbols._Id.GETResponse>, isCreateAction: boolean}">
			<api-items
				v-slot="props"
				:url="Categories.URL"
				:error-messages="errors.categoryId"
				items-key="items"
			>
				<v-select
					v-model="item.categoryId"
					v-bind="props"
					item-title="displayName1"
					item-value="id"
					label="Category"
					:rules="rules({ required: true })"
				/>
			</api-items>

			<symbols-lookup
				v-model="item.name"
				label="Symbol"
				:rules="rules({ required: true })"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type { DefaultItem, Item, ItemErrors } from '~/components/Crud.vue'
import { CategoriesSymbols, Categories } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultCategoryItem: DefaultItem<CategoriesSymbols._Id.POSTRequest | CategoriesSymbols._Id.PUTRequest> = {
	name: '',
	categoryId: 0,
	symbolsCategory: [],
}

defineExpose({
	create: (categoryId?: CategoriesSymbols.Record['categoryId']) => {
		if (categoryId)
			defaultCategoryItem.categoryId = categoryId
		crudRef.value?.create()
	},
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
