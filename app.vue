<template>
	<v-theme-provider :theme="themeCookie">
		<!-- <v-locale-provider :rtl="i18n.localeProperties.value.dir === 'rtl'"> -->
		<DefaultsProvider
			:datatable="{
				itemsPerPage: 50,
				itemsKey: 'items',
				itemsPerPageReqKey: 'pageSize',
				itemsPerPageResKey: 'pageSize',
				totalReqKey: 'total',
				totalResKey: 'total',
				pageReqKey: 'page',
				pageResKey: 'page',
				hasNextKey: 'hasNext',
				height: 'calc(100dvh - var(--v-layout-top) - var(--2fa-banner-height) - var(--page-top-height) - var(--datatable-footer-height) - 172px)',
				fixedHeader: true,
			}"
		>
			<NuxtErrorBoundary>
				<NuxtLayout>
					<NuxtPage />
				</NuxtLayout>
				<!-- You use the default slot to render your content -->
				<template #error="{ error, clearError }">
					<!-- You can display the error locally here: {{ error }}
									<button @click="clearError">
										This will clear the error.
									</button> -->
					<ErrorPage
						:error="error"
						:clear="clearError"
					/>
				</template>
			</NuxtErrorBoundary>

			<vue3-snackbar
				bottom
				duration="50000"
				message-text-color="#ffffff"
				message-icon-color="#ffffff"
				shadow
				background-color="rgb(var(--v-theme-snackbar))"
				base-background-color="rgb(var(--v-theme-snackbar))"
				border="left"
				groups
			>
				<!-- <template #message-inner="{ message }">
					<div />
					<div class="d-flex flex-column pa-4">
						<div
							class="text-subtitle-1"
							v-text="message.title"
						/>
						<div
							class="text-body-1"
							v-text="message.text"
						/>
					</div>
				</template> -->
				<template #message-icon="{ message }">
					<v-icon
						v-if="message.type === 'error'"
						size="32"
						icon="i-material-symbols-light:error-outline-rounded"
					/>
					<v-icon
						v-if="message.type === 'success'"
						size="32"
						icon="i-material-symbols-light:check-circle-outline-rounded"
					/>
					<v-icon
						v-if="message.type === 'info'"
						size="32"
						icon="i-material-symbols-light:info-outline-rounded"
					/>
					<v-icon
						v-if="message.type === 'warning'"
						size="32"
						icon="i-material-symbols-light:warning-outline-rounded"
					/>
				</template>
				<template #message-close-icon="{ isDismissible, dismiss }">
					<v-btn
						v-if="isDismissible"
						color="white"
						icon="i-material-symbols-light:close"
						size="small"
						variant="text"
						@click="dismiss"
					/>
				</template>
			</vue3-snackbar>

			<VuetifyConfirm />
			<LazyDevTools v-if="runtimeConfig.public.devTools" />
		</DefaultsProvider>
		<div style="" />
		<!-- </v-locale-provider> -->
	</v-theme-provider>
</template>

<script setup lang="ts">
import { Vue3Snackbar } from 'vue3-snackbar'
import { supportedLocalesCodes } from './config/general'
import ErrorPage from '~/error.vue'

const i18n = useI18n()
const appConfig = useAppConfig()

const runtimeConfig = useRuntimeConfig()

// const fontLink = computed(() => {
// 	return `/fonts/${i18n.locale.value}.css`
// })

const { isRtl } = useLocale()

// check if the user has an old i18n cookie that has unsupported locales
const localeCookie = i18n.getLocaleCookie()

if (localeCookie && !supportedLocalesCodes.includes(localeCookie)) {
	i18n.setLocaleCookie(i18n.defaultLocale)
	i18n.setLocale(i18n.defaultLocale)
}

const bodyClasses = computed(() => {
	return `locale-${i18n.locale.value} ${isRtl.value ? 'v-locale--is-rtl' : 'v-locale--is-ltr'}`
})

useHead(
	{
		htmlAttrs() {
			return {
				lang: i18n.locale,
			}
		},
		bodyAttrs: {
			class: bodyClasses,
		},
		titleTemplate: (titleChunk) => {
			return `${titleChunk ? titleChunk + ' -' : ''} Pilot`
		},
		meta: [
			{
				name: appConfig.app.description,
				content: appConfig.app.description,
			},
			// fav ico

		],
		// font
		link: [
			// {
			// 	rel: 'stylesheet',
			// 	href: fontLink,
			// },
			{
				rel: 'icon',
				type: 'image/x-icon',
				href: '/favicon.ico',
			},
		],
	})
const themeCookie = useThemeCookie()

const theme = useTheme()
if (themeCookie.value) {
	theme.global.name.value =	themeCookie.value
}

// const datatableDefaults = ref()
onMounted(() => {
	window.addEventListener(
		'keydown',
		function (e) {
			if (e.key === 'F8') {
				/* eslint-disable-next-line */
					debugger;
			}
		},
		false,
	)
})
</script>
