<template>
	<page :title="$t('symbols.symbols')">
		<template #actions>
			<v-btn
				v-if="can('symbol.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="crud?.create()"
			>
				{{ $t('symbols.create-symbol') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/symbols/mt4"
			:headers="headers"
			search-key="name"
			:default-model="{ name: '', path: '' }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
		>
			<template #toolbar.prepend>
				<v-btn
					:disabled="lastOpenedPathHistory.length < 2"
					icon="i-mdi:arrow-back"
					class="me-1"
					@click="back"
				/>
			</template>
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>
			<template #toolbar.middle-start>
				<v-chip
					v-if="lastOpenedType"
					rounded
					closable
					size="large"
					color="primary"
					@click:close="clearType"
				>
					{{ lastOpenedType.title }}
				</v-chip>
			</template>
			<template #prepend="{ height }">
				<v-sheet
					class="fill-height pe-2 me-2 border-e "
					min-width="300"
				>
					<v-toolbar
						color="surface"
						density="compact"
						class="mb-2"
					>
						<v-text-field
							v-model="searchTypeModel"
							clearable
							:placeholder="$t('symbols.find-type')"
							append-inner-icon="i-mdi:magnify"
							flat
							variant="solo-filled"
							hide-details
							density="comfortable"
							class="rounded-t-lg"
						/>
					</v-toolbar>
					<v-sheet
						class="overflow-y-scroll"
						:max-height="`calc(${height} + var(--datatable-footer-height) - 6px) `"
					>
						<!-- <symbols-path-tree ref="pathTreeRef" v-model="lastOpenedPath" v-model:search="debouncedSearchPathModel" v-model:opened="openedModel" @loaded="pathsData = $event" /> -->
						<symbols-type-list
							ref="typeListRef"
							v-model:search="debouncedSearchTypeModel"
							v-model="lastOpenedType"
						/>
					</v-sheet>
				</v-sheet>
			</template>
			<template #item.icon>
				<v-icon icon="i-ph:currency-circle-dollar" />
			</template>
			<template #item.symbol="{ item }">
				<v-list-item
					class="px-0"
					lines="one"
					max-width="300"
					:title="item.symbol"
					:subtitle="item.description"
				/>
			</template>
			<template #item.exemode="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item }"
					item-key="value"
					:items="executionModeItems"
				>
					{{ $t(item.title) }}
				</find-item-by-id>
			</template>
			<template #item.trade="{ value }">
				<find-item-by-id
					:id="value"
					v-slot="{ item }"
					item-key="value"
					:items="tradeItems"
				>
					{{ $t(item.title) }}
				</find-item-by-id>
			</template>
			<template #item.filterLimit="{ value }">
				{{ value / 100 }}
			</template>

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('symbol.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('symbol.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</div>
			</template>

			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<symbols-crud-mt4
		ref="crud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh();typeListRef?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type SymbolsCrud from '@/components/Symbols/Crud/Mt4/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
// import SymbolsPathTree from '@/components/Symbols/PathTree/index.vue'
import { Symbols } from '~/types'
import SymbolsTypeList from '@/components/Symbols/TypeList/index.vue'

const typeListRef = ref<InstanceType<typeof SymbolsTypeList> | null>(null)

const table = ref <InstanceType<typeof Datatable> | null>(null)

const crud = ref<InstanceType<typeof SymbolsCrud> | null>(null)

const searchTypeModel = ref('')

const debouncedSearchTypeModel = useDebounce(searchTypeModel, 300)

const lastOpenedType = ref<Symbols.MT4.Type.Lookup.SingleRecord | null>(null)

const isClearingCache = ref(false)

const { undo: back, history: lastOpenedPathHistory } = useRefHistory(lastOpenedType)

watch(() => lastOpenedType.value, (item) => {
	table.value?.filter({
		type: item ? item?.value : undefined,
	})
})

const { t } = useI18n()

const headers: Headers = [
	{
		title: '',
		value: 'icon',
	},
	{
		title: t('symbols.symbol'),
		value: 'symbol',
		// nowrap: true,
	},
	{
		title: t('symbols.execution'),
		value: 'exemode',
	},
	{
		title: t('symbols.filter'),
		value: 'filter',
	},
	{
		title: t('symbols.spread'),
		value: 'spread',
	},
	{
		title: t('symbols.stops'),
		value: 'filterLimit',
	},
	{
		title: t('symbols.trade'),
		value: 'trade',
		nowrap: true,
	},
	{
		value: 'actions',
	},
]

const clearType = () => {
	lastOpenedType.value = null
	// openedModel.value = []
	table.value?.filter({})
}

const executionModeItems = enumToItems(Symbols.MT4.ExecutionMode, 'Symbols.MT4.ExecutionMode')

const tradeItems = enumToItems(Symbols.MT4.TradingMode, 'Symbols.MT4.TradingMode')

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/symbols/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
		typeListRef.value!.refresh()
	}).finally(() => {
		isClearingCache.value = false
	})
}
</script>
