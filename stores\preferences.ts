import { defineStore } from 'pinia'
import type { LayoutItem } from '~/components/Widget/types'
import type { My } from '~/types'

type Keys = {
	theme: Theme
	locale: string
	tradingServerName: string | null
	tradingServerType: 'MT4' | 'MT5'
	rail: boolean
	dashboardWidgets: LayoutItem[]
}

interface State {
	keys: Keys
	defaultKeys: Keys
}
export const usePreferencesStore = defineStore({
	persist: true,
	id: 'preferencesStore',
	state: (): State => ({
		keys: {
			theme: 'light',
			locale: 'en',
			tradingServerName: null,
			tradingServerType: 'MT4',
			rail: false,
			dashboardWidgets: [],
		},
		defaultKeys: {
			theme: 'light',
			locale: 'en',
			tradingServerName: null,
			tradingServerType: 'MT4',
			rail: false,
			dashboardWidgets: [],
		},
	}),
	getters: {
		getKey(state) {
			return <T extends keyof State['keys']>(key: T) => state.keys[key]
		},
	},
	actions: {
		updateKey<T extends keyof State['keys']>(key: T, value: State['keys'][T]) {
			this.setKey(key, value)
			const { $api } = useNuxtApp()
			$api('/my/preferences', {
				method: 'PUT',
				body: {
					key,
					value,
				},
			})
		},
		setKeys(set: My.Preferences.GETResponse) {
			this.keys = useCloned(this.defaultKeys).cloned.value
			set.forEach((item) => {
				if (Object.keys(this.keys).includes(item.key)) {
					this.setKey(item.key as keyof State['keys'], item.value)
				}
			})
		},
		setKey<T extends keyof State['keys']>(key: T, value: State['keys'][T]) {
			if (value === 'False') {
				value = false as State['keys'][T]
			}
			if (value === 'True') {
				value = true as State['keys'][T]
			}
			this.keys[key] = value
		},
		load() {
			const { $api } = useNuxtApp()
			return $api<My.Preferences.GETResponse>('/my/preferences').then((resp) => {
				this.setKeys(resp)
				this._syncTheme()
				this._syncLocale()
			})
		},
		_syncTheme() {
			const theme = this.getKey('theme')
			const themeCookie = useThemeCookie()
			themeCookie.value = theme
		},
		_syncLocale() {
			const locale = this.getKey('locale')
			const { $i18n } = useNuxtApp()
			$i18n.locale.value = locale
		},
	},
})
