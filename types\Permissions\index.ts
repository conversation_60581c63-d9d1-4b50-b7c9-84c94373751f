/*
Permissions Lookup Grouped By Category ResponseBody
*/
import type { Names as PermissionNames } from './Names'

export namespace Permissions {
	export namespace LookupGrouped {
		export const URL = '/permissions/lookup-grouped'
		export interface SingleRecord {
			name: string
			permissions: {
				id: number
				name: string
				description: null | string
				category: null
				createdAt: string
				updatedAt: null
			}[]
		}

		export type GETResponse = SingleRecord[]
	}

	export type Names = PermissionNames
}
