<template>
	<Title>{{ $t('forgot.forgot-password') }}</Title>
	<v-window v-model="windowModel">
		<v-window-item>
			<v-form
				ref="formRef"
				@submit.prevent="submit"
			>
				<div class="text-center text-h6 pb-4">
					{{ $t('forgot.forgot-password') }}
				</div>
				<p class="text-body-2 mb-4">
					{{ $t('forgot.please-enter-your-email') }}
				</p>
				<v-text-field
					v-model.trim="form.email"
					:rules="rules({ type: 'email', required: true })"
					:error-messages="errors.email"
					class="mb-2"
					:placeholder="$t('forgot.enter-your-email-address')"
					:label="$t('forgot.email')"
					hide-details="auto"
					autocomplete="username"
				/>

				<div class="d-flex justify-end">
					<v-btn
						:to="{ path: '/auth/login' }"
						variant="text"
						size="small"
						class="text-capitalize"
					>
						{{ $t('forgot.remembered-your-password') }}
					</v-btn>
				</div>

				<v-btn
					class="mt-6"
					color="primary"
					size="large"
					type="submit"
					variant="elevated"
					block
					:disabled="loading"
					:loading="loading"
				>
					{{ $t('forgot.reset-my-password') }}
				</v-btn>
			</v-form>
		</v-window-item>
		<v-window-item>
			<v-empty-state
				:title="t('forgot.reset-link-has-been-sent')"
				:text="$t('forgot.an-email-has-been-sent-to', [form.email])"
				icon="i-mdi-check-circle-outline"
				color="success"
			>
				<template #actions>
					<v-btn
						color="primary"
						@click="returnToLogin"
					>
						{{ t('forgot.back-to-login') }}
					</v-btn>
				</template>
			</v-empty-state>
		</v-window-item>
	</v-window>
</template>

<script lang="ts" setup>
import { VForm } from 'vuetify/components'
import { Auth } from '~/types'

const authStore = useAuthStore()

definePageMeta({
	middleware: 'guest',
})

definePageMeta({
	async validate(_route) {
		const authStore = useAuthStore()
		await authStore.verifyLoginMode()
		return authStore.LoginMode === Auth.LoginMode.Modes.Database
	},
})

// const { successSnackbar } = useSnackbar()

const windowModel = ref(0)

const router = useRouter()

const { t } = useI18n()

const form = reactive<Auth.ForgotPassword.POSTRequest>({
	email: '',
})

const loading = ref(false)

const formRef = ref<InstanceType<typeof VForm> | null>(null)

const { errors, handler } = useFormHandler(form)

const submit = async () => {
	const validation = await formRef.value?.validate()

	if (!validation?.valid) {
		return
	}
	loading.value = true
	await authStore.forget(form)
		.then(() => {
			windowModel.value = 1
		})
		.catch(handler)
		.finally(() => {
			loading.value = false
		})
}

const returnToLogin = () => {
	router.push({ name: 'auth-login' }).then(() => {
		formRef.value?.reset()
		formRef.value?.resetValidation()
		windowModel.value = 0
	})
}
</script>
