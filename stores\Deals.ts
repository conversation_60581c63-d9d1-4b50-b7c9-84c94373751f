import { defineStore } from 'pinia'
import type { Deals } from '~/types'

type Deal = {
	minPrice: string | null
	maxPrice: string | null
	minPriceTime: string | null
	maxPriceTime: string | null
	loading: boolean
	error: boolean
}
export type State = {
	deals: {
		[key: string]: Deal
	}
}
export const useDealsStore = defineStore({
	id: 'useDealsStore',
	state: (): State => ({
		deals: {},
	}),
	getters: {
		getMin: state => (key: string) => {
			return state.deals?.[key]?.maxPrice
		},
		getMinTime: state => (key: string) => {
			return state.deals?.[key]?.minPriceTime
		},
		getMax: state => (key: string) => {
			return state.deals?.[key]?.minPrice
		},
		getMaxTime: state => (key: string) => {
			return state.deals?.[key]?.maxPriceTime
		},
		get: state => (key: string): Deal | undefined => {
			return state.deals?.[key]
		},
		isLoading: state => (symbol: string) => {
			return state.deals?.[symbol]?.loading
		},
	},
	actions: {
		setMinMax(key: string, object: any) {
			this.deals[key] = { ...object, loading: false }
		},
		setError(key: string) {
			this.deals[key] = { minPrice: null, maxPrice: null, minPriceTime: null, maxPriceTime: null, loading: false, error: true }
		},
		removeMinMax(key: string) {
			delete this.deals[key]
		},
		getMinMax(symbol: string, from: string, to: string, action: string | number) {
			const { $api } = useNuxtApp()
			const key = this.getKey(symbol, from, to, action)

			// if (this.deals[key]) { return }

			this.deals[key] = { minPrice: null, maxPrice: null, minPriceTime: null, maxPriceTime: null, loading: true, error: false }
			return $api<Deals.SymbolPrices.GETResponse>(`/report/historical-deals-prices/${symbol}`,
				{
					params: { openTime: from, closeTime: to, action },
					// @ts-ignore vue-tsc issue
					priority: 'low',
				})
				.then((resp) => {
					this.setMinMax(key, resp)
				}).catch(() => {
					this.setError(key)
				})
		},
		getKey(symbol: string, from: string, to: string, action: string | number) {
			// serialize to get a unique key
			return `${symbol}-${from}-${to}-${action}`
		},
	},
})
