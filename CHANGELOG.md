## [1.4.2](https://github.com/IngotTech/mt-manager-frontend/compare/v1.4.1...v1.4.2) (2024-10-01)


### Bug Fixes

* env ([b8a833f](https://github.com/IngotTech/mt-manager-frontend/commit/b8a833f7be6296399e70305d90851ab15a1a8840))

## [1.4.1](https://github.com/IngotTech/mt-manager-frontend/compare/v1.4.0...v1.4.1) (2024-10-01)


### Bug Fixes

* testing .app url ([3a57c85](https://github.com/IngotTech/mt-manager-frontend/commit/3a57c8545a011e5c7d80c1b9cac3a5abce8511c6))

# [1.4.0](https://github.com/IngotTech/mt-manager-frontend/compare/v1.3.1...v1.4.0) (2024-09-30)


### Features

* create/update trading accounts on mt4 and mt5 ([898bb81](https://github.com/IngotTech/mt-manager-frontend/commit/898bb81ffcfe68054bf75e3d6247d83cf8b5c461))

## [1.3.1](https://github.com/IngotTech/mt-manager-frontend/compare/v1.3.0...v1.3.1) (2024-09-23)


### Bug Fixes

* users mt servers selection ([def00f8](https://github.com/IngotTech/mt-manager-frontend/commit/def00f8168c4e30e9ab3c5a7c4d1b88e28edc43b))

# [1.3.0](https://github.com/IngotTech/mt-manager-frontend/compare/v1.2.2...v1.3.0) (2024-09-18)


### Features

* add mt4 and mt5 groups ([aeb26b6](https://github.com/IngotTech/mt-manager-frontend/commit/aeb26b618342833a94a0e2ea0141d85f6b6a29bb))

## [1.2.2](https://github.com/IngotTech/mt-manager-frontend/compare/v1.2.1...v1.2.2) (2024-08-29)


### Bug Fixes

* add develop branch in semantic release ([b3c80e0](https://github.com/IngotTech/mt-manager-frontend/commit/b3c80e055f68206eb65088e6328c2bc8bb16fa14))
* encode url params to prevents conflict with query params ([ad433f6](https://github.com/IngotTech/mt-manager-frontend/commit/ad433f6182746c12e158bc44d6e1082f7a720065))
* implement mt4 symbols ([21a7bf7](https://github.com/IngotTech/mt-manager-frontend/commit/21a7bf733a26f6360b075233eb44cb6f4760b2d4))

## [1.2.1](https://github.com/IngotTech/mt-manager-frontend/compare/v1.2.0...v1.2.1) (2024-08-25)


### Bug Fixes

* translations ([546b4ca](https://github.com/IngotTech/mt-manager-frontend/commit/546b4ca67f120da2598878e3871df0d3e8e66e68))

# [1.2.0](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.9...v1.2.0) (2024-08-25)


### Bug Fixes

* datatable fixes ([0615a6a](https://github.com/IngotTech/mt-manager-frontend/commit/0615a6ad249f1ba7d30412d7946e6eac90c07569))
* errors handler to handle 401 arrays ([9d762b6](https://github.com/IngotTech/mt-manager-frontend/commit/9d762b67c020ca19db27853d46beb1d4871db9cf))
* general ([9c68438](https://github.com/IngotTech/mt-manager-frontend/commit/9c68438e5209995bc5532a3460a2e345fccd72d0))
* general fixes ([f1be941](https://github.com/IngotTech/mt-manager-frontend/commit/f1be9410f666d3a5f35d4e2f998b18f580480388))
* merge issues ([4fa272f](https://github.com/IngotTech/mt-manager-frontend/commit/4fa272fce684444279be5abeeb4dd4f2ace68696))


### Features

* add symbols crud ([d86eda1](https://github.com/IngotTech/mt-manager-frontend/commit/d86eda16f0cd4b2feca262cf54504351fb6e959c))

## [1.1.9](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.8...v1.1.9) (2024-08-25)


### Bug Fixes

* add ip restriction to users ([b8b671b](https://github.com/IngotTech/mt-manager-frontend/commit/b8b671b7cc58a733bbf61d92886b33c76ccc0136))

## [1.1.8](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.7...v1.1.8) (2024-08-22)


### Bug Fixes

* force build ([5695441](https://github.com/IngotTech/mt-manager-frontend/commit/56954414464d4f78c18c7fc9e48cdb7b914a1df5))

## [1.1.7](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.6...v1.1.7) (2024-08-22)


### Bug Fixes

* reset page validation ([450bf26](https://github.com/IngotTech/mt-manager-frontend/commit/450bf2630b2b727516aeb68e2b2d88feb183975a))

## [1.1.6](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.5...v1.1.6) (2024-08-22)


### Bug Fixes

* implement login modes ([13c37a0](https://github.com/IngotTech/mt-manager-frontend/commit/13c37a07651b057015e031adac3b53bef1a936f3))
* update confirm plugin to latest ([792108e](https://github.com/IngotTech/mt-manager-frontend/commit/792108ece66e58ac48617290a63f30844879f9cb))

## [1.1.6](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.5...v1.1.6) (2024-08-22)


### Bug Fixes

* implement login modes ([13c37a0](https://github.com/IngotTech/mt-manager-frontend/commit/13c37a07651b057015e031adac3b53bef1a936f3))

## [1.1.5](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.4...v1.1.5) (2024-08-15)


### Bug Fixes

* test versioning ([3970675](https://github.com/IngotTech/mt-manager-frontend/commit/3970675d6162463a3bece6a655e996577c0f3bfe))

## [1.1.4](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.3...v1.1.4) (2024-08-13)


### Bug Fixes

* timeout on prices file upload ([49d7427](https://github.com/IngotTech/mt-manager-frontend/commit/49d7427b6e4442f19f69b09cbf862c14977ed117))

## [1.1.3](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.2...v1.1.3) (2024-08-13)


### Bug Fixes

* prices report ([715f4ed](https://github.com/IngotTech/mt-manager-frontend/commit/715f4ed68f4cb5a96a93c3dbfa18808023c9d6e3))

## [1.1.2](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.1...v1.1.2) (2024-08-13)


### Bug Fixes

* api version not showing ([f5669fb](https://github.com/IngotTech/mt-manager-frontend/commit/f5669fb0826aad2dad253a1db311e18d64eef1b6))

## [1.1.1](https://github.com/IngotTech/mt-manager-frontend/compare/v1.1.0...v1.1.1) (2024-08-12)


### Bug Fixes

* when login history empty switch to new login automatically ([965f682](https://github.com/IngotTech/mt-manager-frontend/commit/965f6821a8e2405261d66a7d01a2a0796c86d9b4))

# [1.1.0](https://github.com/IngotTech/mt-manager-frontend/compare/v1.0.0...v1.1.0) (2024-08-12)


### Bug Fixes

* add .output to preserved dirs ([e2bda78](https://github.com/IngotTech/mt-manager-frontend/commit/e2bda7800713db943b3a99d539cad508b6ac892f))


### Features

* add version control + about page ([d12a3e2](https://github.com/IngotTech/mt-manager-frontend/commit/d12a3e22c71ad53a49a988cc43b4dc4b7dabecda))

# 1.0.0 (2024-08-12)


### Bug Fixes

* 401 handler not redirecting to login ([7ce74ba](https://github.com/IngotTech/mt-manager-frontend/commit/7ce74babcaa3a2e2c0232c2bdc70567b8525537c))
* 404 error on production ([4bfc766](https://github.com/IngotTech/mt-manager-frontend/commit/4bfc766e6c715840615b83e3b3047bc140161138))
* auth get stuck on second retry ([b6e2772](https://github.com/IngotTech/mt-manager-frontend/commit/b6e2772161b1ca92b68b1d14e3e5101e85ead431))
* auto focus on otp inputs ([a0657e4](https://github.com/IngotTech/mt-manager-frontend/commit/a0657e4588ccafc2b90403d44eeb8aee01d3b0a5))
* backend response modification ([3fc34b0](https://github.com/IngotTech/mt-manager-frontend/commit/3fc34b037c5443089c91b4b762e8bb80de45fddb))
* check if the apply filter will be applied before emptying data ([0a3e0eb](https://github.com/IngotTech/mt-manager-frontend/commit/0a3e0eba2749dc357050a964e1353804da7989a9))
* clear otp input when disabled ([b9e5ccf](https://github.com/IngotTech/mt-manager-frontend/commit/b9e5ccf0c1266a95636804f2b43579f0497d9a01))
* clear warning ([3a421f5](https://github.com/IngotTech/mt-manager-frontend/commit/3a421f5f3f731cee16c3e5ce6ffbced49b46b842))
* column-Visibility not showing in fullscreen mode ([52d85e9](https://github.com/IngotTech/mt-manager-frontend/commit/52d85e9dea31fba3fffa3ef6facbac2128a04312))
* confirm dialog error ([218c889](https://github.com/IngotTech/mt-manager-frontend/commit/218c8894ab91c3c7566ac3ce7fa9ddeba34cdafc))
* correct redirect on first entry ([230b583](https://github.com/IngotTech/mt-manager-frontend/commit/230b5839a22666daceb328da72c4b92a5a35ece3))
* crud confirmation ([29b10ba](https://github.com/IngotTech/mt-manager-frontend/commit/29b10ba8c7a9129917e0a0c1f2133eca367ab79c))
* crud duplicated requests ([98cdd1f](https://github.com/IngotTech/mt-manager-frontend/commit/98cdd1ff55135b241d5569cd9641e6b6e3c6f3e0))
* dashboard alignment ([7e1b4ea](https://github.com/IngotTech/mt-manager-frontend/commit/7e1b4ea83a21674f692304fcba98b9702f050e4a))
* datatable emits refresh on local search ([b09538b](https://github.com/IngotTech/mt-manager-frontend/commit/b09538b1b33956b09245fbe750105f52d4ef72f4))
* datatable items length warning ([f373b17](https://github.com/IngotTech/mt-manager-frontend/commit/f373b17571d0cfc4825042137534f549e56aa6cb))
* datatable not showing loading when items-per-page = -1 ([d0b7582](https://github.com/IngotTech/mt-manager-frontend/commit/d0b7582327f1c2d4bdc9ea159ed6fec4a5199850))
* datatable perpage issue ([c561256](https://github.com/IngotTech/mt-manager-frontend/commit/c5612560d0b82084a565b1a45e19f95ff48855bc))
* db boolean values comes as string ([7e48356](https://github.com/IngotTech/mt-manager-frontend/commit/7e483564431796eb1097826f527a6b916809e762))
* disable PWA ([63db155](https://github.com/IngotTech/mt-manager-frontend/commit/63db155511e70b9a00b06f898ce6f4d3a4e230f8))
* double call for refresh token ([29cac00](https://github.com/IngotTech/mt-manager-frontend/commit/29cac002b1ab842d2dbb8bfc43096301dc66bca3))
* duplicate column + general enhancement ([232165c](https://github.com/IngotTech/mt-manager-frontend/commit/232165cd47874a10412f7d1f732aab15b1b5800b))
* edit ([17d29cd](https://github.com/IngotTech/mt-manager-frontend/commit/17d29cd1625e083b3319b3c31fdb59cebc39ca95))
* edit ([7d75233](https://github.com/IngotTech/mt-manager-frontend/commit/7d752330e48f7db4446830d36b46a9bca0d6260a))
* edit header ([6527ad6](https://github.com/IngotTech/mt-manager-frontend/commit/6527ad661d4d84972300a34dc418b40a1ac33157))
* edit name ([c909d58](https://github.com/IngotTech/mt-manager-frontend/commit/c909d58028b53061b9176b011153249a01310c08))
* error massages doesn't appear on prod ([52a535a](https://github.com/IngotTech/mt-manager-frontend/commit/52a535a55f51e14be4611d54c36ebd019c8f456f))
* error message in crude ([3c2fb8d](https://github.com/IngotTech/mt-manager-frontend/commit/3c2fb8d17b94614be8de1ea83807e4db26626cfa))
* executedByName cell ([f8cf3ea](https://github.com/IngotTech/mt-manager-frontend/commit/f8cf3ea3fc88d605583311a54a8e51654d0e090e))
* filtration reset issue ([f1c90f8](https://github.com/IngotTech/mt-manager-frontend/commit/f1c90f8722dde088278914b136771a0a18f0fc78))
* fix ([4b01726](https://github.com/IngotTech/mt-manager-frontend/commit/4b01726907acf410b5142a4650320c6914be6e30))
* fix ([15788fb](https://github.com/IngotTech/mt-manager-frontend/commit/15788fbc2c3eeba5e26d483f675d78e38de9137b))
* fix conflict ([bb01dfe](https://github.com/IngotTech/mt-manager-frontend/commit/bb01dfe7f8deb1a95dcf2bc0c1394e802919e06f))
* fix conflicts ([fec1c64](https://github.com/IngotTech/mt-manager-frontend/commit/fec1c640eefa7b88478eda29295020da0c3f4a57))
* font weights + create transaction permission ([94b2e98](https://github.com/IngotTech/mt-manager-frontend/commit/94b2e982cdda9aa0a9beaffed1600b0cfd9259a4))
* general ([9390da1](https://github.com/IngotTech/mt-manager-frontend/commit/9390da1add90607ae52f0663bcf01cbeb78df83b))
* general ([202d339](https://github.com/IngotTech/mt-manager-frontend/commit/202d3398e4fc1dfadbf3eebcfb80a3e52d22a641))
* general ([e5a3ffc](https://github.com/IngotTech/mt-manager-frontend/commit/e5a3ffc6078d8c180bc52f2b3d10e8d1b30644ba))
* general ([298e6b4](https://github.com/IngotTech/mt-manager-frontend/commit/298e6b47df24666da9fbb32374bd1ec1294e9599))
* general ([c04ceb0](https://github.com/IngotTech/mt-manager-frontend/commit/c04ceb0895d5d4b8ee810786419781a7a9399d17))
* general fixes ([af344ea](https://github.com/IngotTech/mt-manager-frontend/commit/af344eabda53b2da02a486dff76fb7d0510cf107))
* general fixes ([cc50975](https://github.com/IngotTech/mt-manager-frontend/commit/cc50975fc2c795fa5070fc5c16ed02d6019261c7))
* guest middleware ([4bc6d6f](https://github.com/IngotTech/mt-manager-frontend/commit/4bc6d6f8adf1d0aafeb724f1d45cb279d588f4df))
* menu ([c61e481](https://github.com/IngotTech/mt-manager-frontend/commit/c61e4814cad1b2f62e6492bd0ddea8d9633ef08d))
* meta data name ([e04d633](https://github.com/IngotTech/mt-manager-frontend/commit/e04d63372e28249d784d16239a65856ecd71ec77))
* misc ([b4e1043](https://github.com/IngotTech/mt-manager-frontend/commit/b4e1043c7baa35811c2ba4cf981c7b920a6f2bc4))
* no cache ([f45b86d](https://github.com/IngotTech/mt-manager-frontend/commit/f45b86d0884a32a2930fbea7cb2fdb07d5c3ac31))
* now being parsed incorrectly due to reactivity ([5ad96d9](https://github.com/IngotTech/mt-manager-frontend/commit/5ad96d94bd9020f21389710c0cdbcc452efdcb91))
* prev ([6b92927](https://github.com/IngotTech/mt-manager-frontend/commit/6b929273753631bbb807f058d1e491ab094828db))
* redirect path and clear warnings ([0707eca](https://github.com/IngotTech/mt-manager-frontend/commit/0707ecac8ae8fe892539a4e7f63fd1ef0a7f3b7f))
* redirection when only slash ([dfbe442](https://github.com/IngotTech/mt-manager-frontend/commit/dfbe442920f9d1fdf93ae01885affedb8ccabb5f))
* reset filter not updating model ([c126ecb](https://github.com/IngotTech/mt-manager-frontend/commit/c126ecb4fce2c3994858be4447155420d8f89674))
* set admin as default layout ([1258fde](https://github.com/IngotTech/mt-manager-frontend/commit/1258fde8fa1712636219c299ddc84208fdd3a58b))
* snackbar & crud ([6a88397](https://github.com/IngotTech/mt-manager-frontend/commit/6a883979b9c7378f9ad0b62c87a1c268fb2fc8f6))
* socket url for the demo ([2e90f1f](https://github.com/IngotTech/mt-manager-frontend/commit/2e90f1f56edcaeb70d255bc85b10ba7dba0c670d))
* transactions crud ([46d9682](https://github.com/IngotTech/mt-manager-frontend/commit/46d96826d210b2882347a71dd11497402979a3f1))
* type issue ([b7327ad](https://github.com/IngotTech/mt-manager-frontend/commit/b7327ad7fb7ab69c7d297e96b78729c79127ef0b))
* types generation ([d48d1de](https://github.com/IngotTech/mt-manager-frontend/commit/d48d1de7d041ee4d19ea0e361a933ea694e3b787))
* unify loading behavior/ ui ([74b30f7](https://github.com/IngotTech/mt-manager-frontend/commit/74b30f7216163f69185929aca2221223a91cf6c7))
* upgrade vuetify-confirm ([5ef2599](https://github.com/IngotTech/mt-manager-frontend/commit/5ef259926bbb26b4e8700410b2b34276a47c4ebe))
* user ([ab13622](https://github.com/IngotTech/mt-manager-frontend/commit/ab13622a291f0ce48cae45dfe467811b467b4e84))
* users ([70db64e](https://github.com/IngotTech/mt-manager-frontend/commit/70db64effeda39734fa5c0d3cc625d89ed404a6f))
* validation ([11a281d](https://github.com/IngotTech/mt-manager-frontend/commit/11a281d5df3512c0e9a5557d30043999cfc98ed3))
* wrong import ([914e655](https://github.com/IngotTech/mt-manager-frontend/commit/914e655ba50c9e472caa674fc056ba1e9b1d59db))


### Features

* add de/select all on group search ([c594006](https://github.com/IngotTech/mt-manager-frontend/commit/c594006bb3f758190b617d1215e8d50700195535))
* add dot badge if column is hidden ([22145d9](https://github.com/IngotTech/mt-manager-frontend/commit/22145d94dd9d02f2209a0081ce1c645c6b9051d9))
* add favicon ([90b4294](https://github.com/IngotTech/mt-manager-frontend/commit/90b4294606e323b372ac89a7adfb8af432d3ac25))
* add fixed header to tables ([e06a2b4](https://github.com/IngotTech/mt-manager-frontend/commit/e06a2b4d297991117f2ac3e3dc04da938d66bc2b))
* add index column ([ca64fc2](https://github.com/IngotTech/mt-manager-frontend/commit/ca64fc2f5a00dd805a479d61b7d2ab462eae7217))
* add login history quick access ([e72e3d1](https://github.com/IngotTech/mt-manager-frontend/commit/e72e3d164d8905d839d72a895b2c4351e9f595e3))
* add permissions to new pages ([6d0b1e4](https://github.com/IngotTech/mt-manager-frontend/commit/6d0b1e4176059491d839a296b80233e25046a125))
* add position id filter ([d0a5711](https://github.com/IngotTech/mt-manager-frontend/commit/d0a57119adc02e66d58d82a8640aee41f0232daa))
* add rail to main menu ([3250440](https://github.com/IngotTech/mt-manager-frontend/commit/3250440c7d9679959d64f05417613bbd47770a63))
* cancel api search since all data are received ([589315c](https://github.com/IngotTech/mt-manager-frontend/commit/589315c2d9f36d6e2b4b33bf797627a351d31f21))
* convert deals to use datatable comp ([9e73daf](https://github.com/IngotTech/mt-manager-frontend/commit/9e73daf1db0bcbeac36c2f28d3640efeb4956232))
* historical-symbol-prices pages ([f60238a](https://github.com/IngotTech/mt-manager-frontend/commit/f60238a424fa6cc6e1812f45437b584c988639d5))
* implement async-validator as utility ([ef92923](https://github.com/IngotTech/mt-manager-frontend/commit/ef9292379a294ec5f82f0e145f8fc50643c2b150))
* implement permissions on widgets ([00033f1](https://github.com/IngotTech/mt-manager-frontend/commit/00033f140709eb939ad03e38515ffa602b2e13f9))
* login using microsoft credentials ([78a0213](https://github.com/IngotTech/mt-manager-frontend/commit/78a0213548cdb92975357715d5f2d4ff10b01f18))
* merge updated-table branch into main ([0100378](https://github.com/IngotTech/mt-manager-frontend/commit/0100378b86bc46f2d903ea5bdbaff2efd8fd3cda))
* restructuring types ([7dd14c3](https://github.com/IngotTech/mt-manager-frontend/commit/7dd14c34c200f0047d28b7a87b61a24210939e5c))
* send account currency with transactions ([c808f98](https://github.com/IngotTech/mt-manager-frontend/commit/c808f98292edbfc993fa1d528581111d05d16225))
* sync rail func in preferences ([903a917](https://github.com/IngotTech/mt-manager-frontend/commit/903a9178c092037755a04d64518e703db47145c0))

# 1.0.0 (2024-08-12)


### Bug Fixes

* 401 handler not redirecting to login ([7ce74ba](https://github.com/IngotTech/mt-manager-frontend/commit/7ce74babcaa3a2e2c0232c2bdc70567b8525537c))
* 404 error on production ([4bfc766](https://github.com/IngotTech/mt-manager-frontend/commit/4bfc766e6c715840615b83e3b3047bc140161138))
* auth get stuck on second retry ([b6e2772](https://github.com/IngotTech/mt-manager-frontend/commit/b6e2772161b1ca92b68b1d14e3e5101e85ead431))
* auto focus on otp inputs ([a0657e4](https://github.com/IngotTech/mt-manager-frontend/commit/a0657e4588ccafc2b90403d44eeb8aee01d3b0a5))
* backend response modification ([3fc34b0](https://github.com/IngotTech/mt-manager-frontend/commit/3fc34b037c5443089c91b4b762e8bb80de45fddb))
* check if the apply filter will be applied before emptying data ([0a3e0eb](https://github.com/IngotTech/mt-manager-frontend/commit/0a3e0eba2749dc357050a964e1353804da7989a9))
* clear otp input when disabled ([b9e5ccf](https://github.com/IngotTech/mt-manager-frontend/commit/b9e5ccf0c1266a95636804f2b43579f0497d9a01))
* clear warning ([3a421f5](https://github.com/IngotTech/mt-manager-frontend/commit/3a421f5f3f731cee16c3e5ce6ffbced49b46b842))
* column-Visibility not showing in fullscreen mode ([52d85e9](https://github.com/IngotTech/mt-manager-frontend/commit/52d85e9dea31fba3fffa3ef6facbac2128a04312))
* confirm dialog error ([218c889](https://github.com/IngotTech/mt-manager-frontend/commit/218c8894ab91c3c7566ac3ce7fa9ddeba34cdafc))
* correct redirect on first entry ([230b583](https://github.com/IngotTech/mt-manager-frontend/commit/230b5839a22666daceb328da72c4b92a5a35ece3))
* crud confirmation ([29b10ba](https://github.com/IngotTech/mt-manager-frontend/commit/29b10ba8c7a9129917e0a0c1f2133eca367ab79c))
* crud duplicated requests ([98cdd1f](https://github.com/IngotTech/mt-manager-frontend/commit/98cdd1ff55135b241d5569cd9641e6b6e3c6f3e0))
* dashboard alignment ([7e1b4ea](https://github.com/IngotTech/mt-manager-frontend/commit/7e1b4ea83a21674f692304fcba98b9702f050e4a))
* datatable emits refresh on local search ([b09538b](https://github.com/IngotTech/mt-manager-frontend/commit/b09538b1b33956b09245fbe750105f52d4ef72f4))
* datatable items length warning ([f373b17](https://github.com/IngotTech/mt-manager-frontend/commit/f373b17571d0cfc4825042137534f549e56aa6cb))
* datatable not showing loading when items-per-page = -1 ([d0b7582](https://github.com/IngotTech/mt-manager-frontend/commit/d0b7582327f1c2d4bdc9ea159ed6fec4a5199850))
* datatable perpage issue ([c561256](https://github.com/IngotTech/mt-manager-frontend/commit/c5612560d0b82084a565b1a45e19f95ff48855bc))
* db boolean values comes as string ([7e48356](https://github.com/IngotTech/mt-manager-frontend/commit/7e483564431796eb1097826f527a6b916809e762))
* disable PWA ([63db155](https://github.com/IngotTech/mt-manager-frontend/commit/63db155511e70b9a00b06f898ce6f4d3a4e230f8))
* double call for refresh token ([29cac00](https://github.com/IngotTech/mt-manager-frontend/commit/29cac002b1ab842d2dbb8bfc43096301dc66bca3))
* duplicate column + general enhancement ([232165c](https://github.com/IngotTech/mt-manager-frontend/commit/232165cd47874a10412f7d1f732aab15b1b5800b))
* edit ([17d29cd](https://github.com/IngotTech/mt-manager-frontend/commit/17d29cd1625e083b3319b3c31fdb59cebc39ca95))
* edit ([7d75233](https://github.com/IngotTech/mt-manager-frontend/commit/7d752330e48f7db4446830d36b46a9bca0d6260a))
* edit header ([6527ad6](https://github.com/IngotTech/mt-manager-frontend/commit/6527ad661d4d84972300a34dc418b40a1ac33157))
* edit name ([c909d58](https://github.com/IngotTech/mt-manager-frontend/commit/c909d58028b53061b9176b011153249a01310c08))
* error massages doesn't appear on prod ([52a535a](https://github.com/IngotTech/mt-manager-frontend/commit/52a535a55f51e14be4611d54c36ebd019c8f456f))
* error message in crude ([3c2fb8d](https://github.com/IngotTech/mt-manager-frontend/commit/3c2fb8d17b94614be8de1ea83807e4db26626cfa))
* executedByName cell ([f8cf3ea](https://github.com/IngotTech/mt-manager-frontend/commit/f8cf3ea3fc88d605583311a54a8e51654d0e090e))
* filtration reset issue ([f1c90f8](https://github.com/IngotTech/mt-manager-frontend/commit/f1c90f8722dde088278914b136771a0a18f0fc78))
* fix ([4b01726](https://github.com/IngotTech/mt-manager-frontend/commit/4b01726907acf410b5142a4650320c6914be6e30))
* fix ([15788fb](https://github.com/IngotTech/mt-manager-frontend/commit/15788fbc2c3eeba5e26d483f675d78e38de9137b))
* fix conflict ([bb01dfe](https://github.com/IngotTech/mt-manager-frontend/commit/bb01dfe7f8deb1a95dcf2bc0c1394e802919e06f))
* fix conflicts ([fec1c64](https://github.com/IngotTech/mt-manager-frontend/commit/fec1c640eefa7b88478eda29295020da0c3f4a57))
* font weights + create transaction permission ([94b2e98](https://github.com/IngotTech/mt-manager-frontend/commit/94b2e982cdda9aa0a9beaffed1600b0cfd9259a4))
* general ([9390da1](https://github.com/IngotTech/mt-manager-frontend/commit/9390da1add90607ae52f0663bcf01cbeb78df83b))
* general ([202d339](https://github.com/IngotTech/mt-manager-frontend/commit/202d3398e4fc1dfadbf3eebcfb80a3e52d22a641))
* general ([e5a3ffc](https://github.com/IngotTech/mt-manager-frontend/commit/e5a3ffc6078d8c180bc52f2b3d10e8d1b30644ba))
* general ([298e6b4](https://github.com/IngotTech/mt-manager-frontend/commit/298e6b47df24666da9fbb32374bd1ec1294e9599))
* general ([c04ceb0](https://github.com/IngotTech/mt-manager-frontend/commit/c04ceb0895d5d4b8ee810786419781a7a9399d17))
* general fixes ([af344ea](https://github.com/IngotTech/mt-manager-frontend/commit/af344eabda53b2da02a486dff76fb7d0510cf107))
* general fixes ([cc50975](https://github.com/IngotTech/mt-manager-frontend/commit/cc50975fc2c795fa5070fc5c16ed02d6019261c7))
* guest middleware ([4bc6d6f](https://github.com/IngotTech/mt-manager-frontend/commit/4bc6d6f8adf1d0aafeb724f1d45cb279d588f4df))
* menu ([c61e481](https://github.com/IngotTech/mt-manager-frontend/commit/c61e4814cad1b2f62e6492bd0ddea8d9633ef08d))
* meta data name ([e04d633](https://github.com/IngotTech/mt-manager-frontend/commit/e04d63372e28249d784d16239a65856ecd71ec77))
* misc ([b4e1043](https://github.com/IngotTech/mt-manager-frontend/commit/b4e1043c7baa35811c2ba4cf981c7b920a6f2bc4))
* no cache ([f45b86d](https://github.com/IngotTech/mt-manager-frontend/commit/f45b86d0884a32a2930fbea7cb2fdb07d5c3ac31))
* now being parsed incorrectly due to reactivity ([5ad96d9](https://github.com/IngotTech/mt-manager-frontend/commit/5ad96d94bd9020f21389710c0cdbcc452efdcb91))
* prev ([6b92927](https://github.com/IngotTech/mt-manager-frontend/commit/6b929273753631bbb807f058d1e491ab094828db))
* redirect path and clear warnings ([0707eca](https://github.com/IngotTech/mt-manager-frontend/commit/0707ecac8ae8fe892539a4e7f63fd1ef0a7f3b7f))
* redirection when only slash ([dfbe442](https://github.com/IngotTech/mt-manager-frontend/commit/dfbe442920f9d1fdf93ae01885affedb8ccabb5f))
* reset filter not updating model ([c126ecb](https://github.com/IngotTech/mt-manager-frontend/commit/c126ecb4fce2c3994858be4447155420d8f89674))
* set admin as default layout ([1258fde](https://github.com/IngotTech/mt-manager-frontend/commit/1258fde8fa1712636219c299ddc84208fdd3a58b))
* snackbar & crud ([6a88397](https://github.com/IngotTech/mt-manager-frontend/commit/6a883979b9c7378f9ad0b62c87a1c268fb2fc8f6))
* socket url for the demo ([2e90f1f](https://github.com/IngotTech/mt-manager-frontend/commit/2e90f1f56edcaeb70d255bc85b10ba7dba0c670d))
* transactions crud ([46d9682](https://github.com/IngotTech/mt-manager-frontend/commit/46d96826d210b2882347a71dd11497402979a3f1))
* type issue ([b7327ad](https://github.com/IngotTech/mt-manager-frontend/commit/b7327ad7fb7ab69c7d297e96b78729c79127ef0b))
* types generation ([d48d1de](https://github.com/IngotTech/mt-manager-frontend/commit/d48d1de7d041ee4d19ea0e361a933ea694e3b787))
* unify loading behavior/ ui ([74b30f7](https://github.com/IngotTech/mt-manager-frontend/commit/74b30f7216163f69185929aca2221223a91cf6c7))
* upgrade vuetify-confirm ([5ef2599](https://github.com/IngotTech/mt-manager-frontend/commit/5ef259926bbb26b4e8700410b2b34276a47c4ebe))
* user ([ab13622](https://github.com/IngotTech/mt-manager-frontend/commit/ab13622a291f0ce48cae45dfe467811b467b4e84))
* users ([70db64e](https://github.com/IngotTech/mt-manager-frontend/commit/70db64effeda39734fa5c0d3cc625d89ed404a6f))
* validation ([11a281d](https://github.com/IngotTech/mt-manager-frontend/commit/11a281d5df3512c0e9a5557d30043999cfc98ed3))
* wrong import ([914e655](https://github.com/IngotTech/mt-manager-frontend/commit/914e655ba50c9e472caa674fc056ba1e9b1d59db))


### Features

* add de/select all on group search ([c594006](https://github.com/IngotTech/mt-manager-frontend/commit/c594006bb3f758190b617d1215e8d50700195535))
* add dot badge if column is hidden ([22145d9](https://github.com/IngotTech/mt-manager-frontend/commit/22145d94dd9d02f2209a0081ce1c645c6b9051d9))
* add favicon ([90b4294](https://github.com/IngotTech/mt-manager-frontend/commit/90b4294606e323b372ac89a7adfb8af432d3ac25))
* add fixed header to tables ([e06a2b4](https://github.com/IngotTech/mt-manager-frontend/commit/e06a2b4d297991117f2ac3e3dc04da938d66bc2b))
* add index column ([ca64fc2](https://github.com/IngotTech/mt-manager-frontend/commit/ca64fc2f5a00dd805a479d61b7d2ab462eae7217))
* add login history quick access ([e72e3d1](https://github.com/IngotTech/mt-manager-frontend/commit/e72e3d164d8905d839d72a895b2c4351e9f595e3))
* add permissions to new pages ([6d0b1e4](https://github.com/IngotTech/mt-manager-frontend/commit/6d0b1e4176059491d839a296b80233e25046a125))
* add position id filter ([d0a5711](https://github.com/IngotTech/mt-manager-frontend/commit/d0a57119adc02e66d58d82a8640aee41f0232daa))
* add rail to main menu ([3250440](https://github.com/IngotTech/mt-manager-frontend/commit/3250440c7d9679959d64f05417613bbd47770a63))
* cancel api search since all data are received ([589315c](https://github.com/IngotTech/mt-manager-frontend/commit/589315c2d9f36d6e2b4b33bf797627a351d31f21))
* convert deals to use datatable comp ([9e73daf](https://github.com/IngotTech/mt-manager-frontend/commit/9e73daf1db0bcbeac36c2f28d3640efeb4956232))
* historical-symbol-prices pages ([f60238a](https://github.com/IngotTech/mt-manager-frontend/commit/f60238a424fa6cc6e1812f45437b584c988639d5))
* implement async-validator as utility ([ef92923](https://github.com/IngotTech/mt-manager-frontend/commit/ef9292379a294ec5f82f0e145f8fc50643c2b150))
* implement permissions on widgets ([00033f1](https://github.com/IngotTech/mt-manager-frontend/commit/00033f140709eb939ad03e38515ffa602b2e13f9))
* login using microsoft credentials ([78a0213](https://github.com/IngotTech/mt-manager-frontend/commit/78a0213548cdb92975357715d5f2d4ff10b01f18))
* merge updated-table branch into main ([0100378](https://github.com/IngotTech/mt-manager-frontend/commit/0100378b86bc46f2d903ea5bdbaff2efd8fd3cda))
* restructuring types ([7dd14c3](https://github.com/IngotTech/mt-manager-frontend/commit/7dd14c34c200f0047d28b7a87b61a24210939e5c))
* send account currency with transactions ([c808f98](https://github.com/IngotTech/mt-manager-frontend/commit/c808f98292edbfc993fa1d528581111d05d16225))
* sync rail func in preferences ([903a917](https://github.com/IngotTech/mt-manager-frontend/commit/903a9178c092037755a04d64518e703db47145c0))

# 1.0.0 (2024-08-12)


### Bug Fixes

* 401 handler not redirecting to login ([7ce74ba](https://github.com/IngotTech/mt-manager-frontend/commit/7ce74babcaa3a2e2c0232c2bdc70567b8525537c))
* 404 error on production ([4bfc766](https://github.com/IngotTech/mt-manager-frontend/commit/4bfc766e6c715840615b83e3b3047bc140161138))
* auth get stuck on second retry ([b6e2772](https://github.com/IngotTech/mt-manager-frontend/commit/b6e2772161b1ca92b68b1d14e3e5101e85ead431))
* auto focus on otp inputs ([a0657e4](https://github.com/IngotTech/mt-manager-frontend/commit/a0657e4588ccafc2b90403d44eeb8aee01d3b0a5))
* backend response modification ([3fc34b0](https://github.com/IngotTech/mt-manager-frontend/commit/3fc34b037c5443089c91b4b762e8bb80de45fddb))
* check if the apply filter will be applied before emptying data ([0a3e0eb](https://github.com/IngotTech/mt-manager-frontend/commit/0a3e0eba2749dc357050a964e1353804da7989a9))
* clear otp input when disabled ([b9e5ccf](https://github.com/IngotTech/mt-manager-frontend/commit/b9e5ccf0c1266a95636804f2b43579f0497d9a01))
* clear warning ([3a421f5](https://github.com/IngotTech/mt-manager-frontend/commit/3a421f5f3f731cee16c3e5ce6ffbced49b46b842))
* column-Visibility not showing in fullscreen mode ([52d85e9](https://github.com/IngotTech/mt-manager-frontend/commit/52d85e9dea31fba3fffa3ef6facbac2128a04312))
* confirm dialog error ([218c889](https://github.com/IngotTech/mt-manager-frontend/commit/218c8894ab91c3c7566ac3ce7fa9ddeba34cdafc))
* correct redirect on first entry ([230b583](https://github.com/IngotTech/mt-manager-frontend/commit/230b5839a22666daceb328da72c4b92a5a35ece3))
* crud confirmation ([29b10ba](https://github.com/IngotTech/mt-manager-frontend/commit/29b10ba8c7a9129917e0a0c1f2133eca367ab79c))
* crud duplicated requests ([98cdd1f](https://github.com/IngotTech/mt-manager-frontend/commit/98cdd1ff55135b241d5569cd9641e6b6e3c6f3e0))
* dashboard alignment ([7e1b4ea](https://github.com/IngotTech/mt-manager-frontend/commit/7e1b4ea83a21674f692304fcba98b9702f050e4a))
* datatable emits refresh on local search ([b09538b](https://github.com/IngotTech/mt-manager-frontend/commit/b09538b1b33956b09245fbe750105f52d4ef72f4))
* datatable items length warning ([f373b17](https://github.com/IngotTech/mt-manager-frontend/commit/f373b17571d0cfc4825042137534f549e56aa6cb))
* datatable not showing loading when items-per-page = -1 ([d0b7582](https://github.com/IngotTech/mt-manager-frontend/commit/d0b7582327f1c2d4bdc9ea159ed6fec4a5199850))
* datatable perpage issue ([c561256](https://github.com/IngotTech/mt-manager-frontend/commit/c5612560d0b82084a565b1a45e19f95ff48855bc))
* db boolean values comes as string ([7e48356](https://github.com/IngotTech/mt-manager-frontend/commit/7e483564431796eb1097826f527a6b916809e762))
* disable PWA ([63db155](https://github.com/IngotTech/mt-manager-frontend/commit/63db155511e70b9a00b06f898ce6f4d3a4e230f8))
* double call for refresh token ([29cac00](https://github.com/IngotTech/mt-manager-frontend/commit/29cac002b1ab842d2dbb8bfc43096301dc66bca3))
* duplicate column + general enhancement ([232165c](https://github.com/IngotTech/mt-manager-frontend/commit/232165cd47874a10412f7d1f732aab15b1b5800b))
* edit ([17d29cd](https://github.com/IngotTech/mt-manager-frontend/commit/17d29cd1625e083b3319b3c31fdb59cebc39ca95))
* edit ([7d75233](https://github.com/IngotTech/mt-manager-frontend/commit/7d752330e48f7db4446830d36b46a9bca0d6260a))
* edit header ([6527ad6](https://github.com/IngotTech/mt-manager-frontend/commit/6527ad661d4d84972300a34dc418b40a1ac33157))
* edit name ([c909d58](https://github.com/IngotTech/mt-manager-frontend/commit/c909d58028b53061b9176b011153249a01310c08))
* error massages doesn't appear on prod ([52a535a](https://github.com/IngotTech/mt-manager-frontend/commit/52a535a55f51e14be4611d54c36ebd019c8f456f))
* error message in crude ([3c2fb8d](https://github.com/IngotTech/mt-manager-frontend/commit/3c2fb8d17b94614be8de1ea83807e4db26626cfa))
* executedByName cell ([f8cf3ea](https://github.com/IngotTech/mt-manager-frontend/commit/f8cf3ea3fc88d605583311a54a8e51654d0e090e))
* filtration reset issue ([f1c90f8](https://github.com/IngotTech/mt-manager-frontend/commit/f1c90f8722dde088278914b136771a0a18f0fc78))
* fix ([4b01726](https://github.com/IngotTech/mt-manager-frontend/commit/4b01726907acf410b5142a4650320c6914be6e30))
* fix ([15788fb](https://github.com/IngotTech/mt-manager-frontend/commit/15788fbc2c3eeba5e26d483f675d78e38de9137b))
* fix conflict ([bb01dfe](https://github.com/IngotTech/mt-manager-frontend/commit/bb01dfe7f8deb1a95dcf2bc0c1394e802919e06f))
* fix conflicts ([fec1c64](https://github.com/IngotTech/mt-manager-frontend/commit/fec1c640eefa7b88478eda29295020da0c3f4a57))
* font weights + create transaction permission ([94b2e98](https://github.com/IngotTech/mt-manager-frontend/commit/94b2e982cdda9aa0a9beaffed1600b0cfd9259a4))
* general ([9390da1](https://github.com/IngotTech/mt-manager-frontend/commit/9390da1add90607ae52f0663bcf01cbeb78df83b))
* general ([202d339](https://github.com/IngotTech/mt-manager-frontend/commit/202d3398e4fc1dfadbf3eebcfb80a3e52d22a641))
* general ([e5a3ffc](https://github.com/IngotTech/mt-manager-frontend/commit/e5a3ffc6078d8c180bc52f2b3d10e8d1b30644ba))
* general ([298e6b4](https://github.com/IngotTech/mt-manager-frontend/commit/298e6b47df24666da9fbb32374bd1ec1294e9599))
* general ([c04ceb0](https://github.com/IngotTech/mt-manager-frontend/commit/c04ceb0895d5d4b8ee810786419781a7a9399d17))
* general fixes ([af344ea](https://github.com/IngotTech/mt-manager-frontend/commit/af344eabda53b2da02a486dff76fb7d0510cf107))
* general fixes ([cc50975](https://github.com/IngotTech/mt-manager-frontend/commit/cc50975fc2c795fa5070fc5c16ed02d6019261c7))
* guest middleware ([4bc6d6f](https://github.com/IngotTech/mt-manager-frontend/commit/4bc6d6f8adf1d0aafeb724f1d45cb279d588f4df))
* menu ([c61e481](https://github.com/IngotTech/mt-manager-frontend/commit/c61e4814cad1b2f62e6492bd0ddea8d9633ef08d))
* meta data name ([e04d633](https://github.com/IngotTech/mt-manager-frontend/commit/e04d63372e28249d784d16239a65856ecd71ec77))
* misc ([b4e1043](https://github.com/IngotTech/mt-manager-frontend/commit/b4e1043c7baa35811c2ba4cf981c7b920a6f2bc4))
* no cache ([f45b86d](https://github.com/IngotTech/mt-manager-frontend/commit/f45b86d0884a32a2930fbea7cb2fdb07d5c3ac31))
* now being parsed incorrectly due to reactivity ([5ad96d9](https://github.com/IngotTech/mt-manager-frontend/commit/5ad96d94bd9020f21389710c0cdbcc452efdcb91))
* prev ([6b92927](https://github.com/IngotTech/mt-manager-frontend/commit/6b929273753631bbb807f058d1e491ab094828db))
* redirect path and clear warnings ([0707eca](https://github.com/IngotTech/mt-manager-frontend/commit/0707ecac8ae8fe892539a4e7f63fd1ef0a7f3b7f))
* redirection when only slash ([dfbe442](https://github.com/IngotTech/mt-manager-frontend/commit/dfbe442920f9d1fdf93ae01885affedb8ccabb5f))
* reset filter not updating model ([c126ecb](https://github.com/IngotTech/mt-manager-frontend/commit/c126ecb4fce2c3994858be4447155420d8f89674))
* set admin as default layout ([1258fde](https://github.com/IngotTech/mt-manager-frontend/commit/1258fde8fa1712636219c299ddc84208fdd3a58b))
* snackbar & crud ([6a88397](https://github.com/IngotTech/mt-manager-frontend/commit/6a883979b9c7378f9ad0b62c87a1c268fb2fc8f6))
* socket url for the demo ([2e90f1f](https://github.com/IngotTech/mt-manager-frontend/commit/2e90f1f56edcaeb70d255bc85b10ba7dba0c670d))
* transactions crud ([46d9682](https://github.com/IngotTech/mt-manager-frontend/commit/46d96826d210b2882347a71dd11497402979a3f1))
* type issue ([b7327ad](https://github.com/IngotTech/mt-manager-frontend/commit/b7327ad7fb7ab69c7d297e96b78729c79127ef0b))
* types generation ([d48d1de](https://github.com/IngotTech/mt-manager-frontend/commit/d48d1de7d041ee4d19ea0e361a933ea694e3b787))
* unify loading behavior/ ui ([74b30f7](https://github.com/IngotTech/mt-manager-frontend/commit/74b30f7216163f69185929aca2221223a91cf6c7))
* upgrade vuetify-confirm ([5ef2599](https://github.com/IngotTech/mt-manager-frontend/commit/5ef259926bbb26b4e8700410b2b34276a47c4ebe))
* user ([ab13622](https://github.com/IngotTech/mt-manager-frontend/commit/ab13622a291f0ce48cae45dfe467811b467b4e84))
* users ([70db64e](https://github.com/IngotTech/mt-manager-frontend/commit/70db64effeda39734fa5c0d3cc625d89ed404a6f))
* validation ([11a281d](https://github.com/IngotTech/mt-manager-frontend/commit/11a281d5df3512c0e9a5557d30043999cfc98ed3))
* wrong import ([914e655](https://github.com/IngotTech/mt-manager-frontend/commit/914e655ba50c9e472caa674fc056ba1e9b1d59db))


### Features

* add de/select all on group search ([c594006](https://github.com/IngotTech/mt-manager-frontend/commit/c594006bb3f758190b617d1215e8d50700195535))
* add dot badge if column is hidden ([22145d9](https://github.com/IngotTech/mt-manager-frontend/commit/22145d94dd9d02f2209a0081ce1c645c6b9051d9))
* add favicon ([90b4294](https://github.com/IngotTech/mt-manager-frontend/commit/90b4294606e323b372ac89a7adfb8af432d3ac25))
* add fixed header to tables ([e06a2b4](https://github.com/IngotTech/mt-manager-frontend/commit/e06a2b4d297991117f2ac3e3dc04da938d66bc2b))
* add index column ([ca64fc2](https://github.com/IngotTech/mt-manager-frontend/commit/ca64fc2f5a00dd805a479d61b7d2ab462eae7217))
* add login history quick access ([e72e3d1](https://github.com/IngotTech/mt-manager-frontend/commit/e72e3d164d8905d839d72a895b2c4351e9f595e3))
* add permissions to new pages ([6d0b1e4](https://github.com/IngotTech/mt-manager-frontend/commit/6d0b1e4176059491d839a296b80233e25046a125))
* add position id filter ([d0a5711](https://github.com/IngotTech/mt-manager-frontend/commit/d0a57119adc02e66d58d82a8640aee41f0232daa))
* add rail to main menu ([3250440](https://github.com/IngotTech/mt-manager-frontend/commit/3250440c7d9679959d64f05417613bbd47770a63))
* cancel api search since all data are received ([589315c](https://github.com/IngotTech/mt-manager-frontend/commit/589315c2d9f36d6e2b4b33bf797627a351d31f21))
* convert deals to use datatable comp ([9e73daf](https://github.com/IngotTech/mt-manager-frontend/commit/9e73daf1db0bcbeac36c2f28d3640efeb4956232))
* historical-symbol-prices pages ([f60238a](https://github.com/IngotTech/mt-manager-frontend/commit/f60238a424fa6cc6e1812f45437b584c988639d5))
* implement async-validator as utility ([ef92923](https://github.com/IngotTech/mt-manager-frontend/commit/ef9292379a294ec5f82f0e145f8fc50643c2b150))
* implement permissions on widgets ([00033f1](https://github.com/IngotTech/mt-manager-frontend/commit/00033f140709eb939ad03e38515ffa602b2e13f9))
* login using microsoft credentials ([78a0213](https://github.com/IngotTech/mt-manager-frontend/commit/78a0213548cdb92975357715d5f2d4ff10b01f18))
* merge updated-table branch into main ([0100378](https://github.com/IngotTech/mt-manager-frontend/commit/0100378b86bc46f2d903ea5bdbaff2efd8fd3cda))
* restructuring types ([7dd14c3](https://github.com/IngotTech/mt-manager-frontend/commit/7dd14c34c200f0047d28b7a87b61a24210939e5c))
* send account currency with transactions ([c808f98](https://github.com/IngotTech/mt-manager-frontend/commit/c808f98292edbfc993fa1d528581111d05d16225))
* sync rail func in preferences ([903a917](https://github.com/IngotTech/mt-manager-frontend/commit/903a9178c092037755a04d64518e703db47145c0))
