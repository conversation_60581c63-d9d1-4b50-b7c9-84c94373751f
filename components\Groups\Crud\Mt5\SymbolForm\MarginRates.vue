<template>
	<div>
		<DefaultsGroup
			v-model="useMultipleDefaultModel(item, [
				'marginRateLiquidityDefault',
				'marginRateCurrencyDefault',
			]).value"
			:symbol="item"
			:keys="[
				'marginRateLiquidity',
				'marginRateCurrency',
				'marginInitialBuy',
				'marginInitialBuyLimit',
				'marginInitialBuyStop',
				'marginInitialBuyStopLimit',
				'marginInitialSell',
				'marginInitialSellLimit',
				'marginInitialSellStop',
				'marginInitialSellStopLimit',
				'marginMaintenanceBuy',
				'marginMaintenanceBuyLimit',
				'marginMaintenanceBuyStop',
				'marginMaintenanceBuyStopLimit',
				'marginMaintenanceSell',
				'marginMaintenanceSellLimit',
				'marginMaintenanceSellStop',
				'marginMaintenanceSellStopLimit',
			]"
			@update:symbol="$emit('update:item', $event)"
		>
			<v-row>
				<v-col>
					<v-text-field
						v-model.number="item.marginRateLiquidity"
						:error-messages="errors.marginRateLiquidity"
						hide-spin-buttons
						type="number"
						:rules="rules({ required: true })"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.liquidity-margin-rate')"
					/>
				</v-col>
				<v-col>
					<v-text-field
						v-model.number="item.marginRateCurrency"
						type="number"
						hide-spin-buttons
						:error-messages="errors.marginRateCurrency"
						:rules="rules({ required: true })"
						:label="$t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.currency-margin-rate')"
					/>
				</v-col>
			</v-row>
			<fieldset class="mb-2">
				<legend>{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.margin-rates') }}</legend>
				<v-table>
					<thead>
						<tr>
							<th
								class="border-e"
								style="min-width: 150px"
							/>
							<th class="text-center">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.market-order') }}
							</th>
							<th class="text-center">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.limit-order') }}
							</th>
							<th class="text-center">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.stop-order') }}
							</th>
							<th class="text-center">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.stop-limit-order') }}
							</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<td
								colspan="5"
								class="bg-grey-lighten-3 font-weight-medium"
							>
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.initial-margin') }}
							</td>
						</tr>
						<tr>
							<td class="border-e">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.buy') }}
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialBuy"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialBuyLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialBuyStop"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialBuyStopLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
						</tr>
						<tr>
							<td class="border-e">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.sell') }}
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialSell"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialSellLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialSellStop"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginInitialSellStopLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
						</tr>
						<tr>
							<td
								colspan="5"
								class="bg-grey-lighten-3 font-weight-medium"
							>
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.maintenance-margin') }}
							</td>
						</tr>
						<tr>
							<td class="border-e">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.buy') }}
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceBuy"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceBuyLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceBuyStop"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceBuyStopLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
						</tr>
						<tr>
							<td class="border-e">
								{{ $t('Groups.MT5.Crud.Symbols.SymbolForm.MarginRates.sell') }}
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceSell"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceSellLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceSellStop"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
							<td>
								<v-text-field
									v-model.number="item.marginMaintenanceSellStopLimit"
									single-line
									density="compact"
									hide-details
									flat
									variant="solo-filled"
									class="my-1"
								/>
							</td>
						</tr>
					</tbody>
				</v-table>
			</fieldset>
		</DefaultsGroup>
	</div>
</template>

<script lang="ts" setup>
import DefaultsGroup from './Components/DefaultsGroup.vue'
import type { Emit, Props } from './Shared'
import { defaults } from './Shared'
// import { Groups, Symbols } from '~/types'

defineEmits<Emit>()

withDefaults(defineProps<Props>(), defaults)
</script>
