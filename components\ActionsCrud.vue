<template>
	<action-types-crud
		ref="actionTypeCrudRef"
		:navigation-drawer-props="{ width: 500 }"
		@created="actionTypeCreatedHandler"
	/>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		item-name="Action"
		item-identity="displayName"
		:navigation-drawer-props="{ width: 550 }"
		url="/actions/:id"
		v-bind="$attrs"
	>
		<template #default="{ item, errors }: { item: Item<Actions._Id.GETResponse>, errors: ItemErrors<Actions._Id.GETResponse>}">
			<v-text-field
				v-model="item.displayName"
				:error-messages="errors.displayName"
				:rules="rules({ required: true })"
				:label="$t('ActionsCrud.display-name')"
			/>

			<api-items
				ref="actionTypesApiItemsRef"
				v-slot="props"
				url="/action-types/lookup"
				:error-messages="errors.actionTypeId"
			>
				<v-select
					v-model="item.actionTypeId"
					:rules="rules({ required: true })"
					:label="$t('ActionsCrud.action-type')"
					v-bind="props"
					item-title="displayName"
					item-value="id"
					:menu-props="{ modelValue: isSelectOpen }"
					@update:menu="updateMenuHandler"
				>
					<template
						v-if="can('actionTypes.create')"
						#prepend-item
					>
						<v-list-item
							slim
							@click="createNewActionType()"
						>
							<template #prepend>
								<v-icon>i-mdi:plus</v-icon>
							</template>
							{{ $t('ActionsCrud.add-new-action-type') }}
						</v-list-item>

						<v-divider class="mt-2" />
					</template>
				</v-select>
			</api-items>
			<v-textarea
				v-model="item.description"
				:error-messages="errors.description"
				:label="$t('ActionsCrud.description')"
			/>
			<v-textarea
				v-model="item.comment"
				rows="2"
				:error-messages="errors.comment"
				:label="$t('ActionsCrud.comment')"
			/>
			<!-- <v-switch v-model="item.onlyReceiver" hide-details :error-messages="errors.onlyReceiver" color="success" :label="$t('ActionsCrud.only-receiver')" /> -->
			<v-radio-group
				v-model="item.onlyReceiver"
				color="accent"
				:error-messages="errors.onlyReceiver"
				:label="$t('ActionsCrud.only-receiver')"
			>
				<v-radio
					:value="true"
					label="Receiver"
				/>
				<v-radio
					:value="false"
					label="Sender & Receiver"
				/>
			</v-radio-group>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from './Crud.vue'
import type { CrudEventData, DefaultItem, Item, ItemErrors } from './Crud.vue'

import type ApiItems from './ApiItems.vue'
import type { Actions } from '~/types/'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Actions._Id.POSTRequest | Actions._Id.PUTRequest> = {
	comment: '',
	displayName: '',
	onlyReceiver: false,
	actionTypeId: null,
	description: null,
}

const actionTypesApiItemsRef = ref<InstanceType<typeof ApiItems> | null>(null)

const actionTypeCrudRef = ref<InstanceType<typeof Crud> | null>(null)

const isSelectOpen = ref<boolean>(false)

const actionTypeCreatedHandler = async (e: CrudEventData) => {
	await actionTypesApiItemsRef.value?.refresh()

	if (crudRef.value) {
		const item = crudRef.value?.getItem()
		item.actionTypeId = e.response.id
		crudRef.value?.setItem(item)
	}
}

const createNewActionType = () => {
	isSelectOpen.value = false
	actionTypeCrudRef.value?.create()
}

const updateMenuHandler = (value: boolean) => {
	isSelectOpen.value = value
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
