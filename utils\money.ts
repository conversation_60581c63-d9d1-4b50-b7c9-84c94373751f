export default (amount: string | number, currency: string | null | undefined = 'USD', options: Intl.NumberFormatOptions = {}) => {
	if (!currency) {
		currency = undefined
	}

	const { $i18n } = useNuxtApp()

	const ISOLocale = $i18n.localeProperties.value.iso || 'en-US'

	return new Intl.NumberFormat(ISOLocale, {
		style: currency ? 'currency' : undefined,
		currency,
		// currencyDisplay: 'code',
		...options,
	}).format(Number(amount))
}
