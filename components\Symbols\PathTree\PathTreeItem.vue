<template>
	<v-lazy min-height="40">
		<v-list-group
			:value="item.value"
			expand-icon="i-mdi:plus"
			collapse-icon="i-mdi:minus"
			subgroup
			style="--prepend-width: 12px"
			class="flex-grow-1"
		>
			<template #activator="{ props, isOpen }">
				<v-list-item
					:title="item.title"
					class="show-on-hover-trigger px-1"
					@click="selectPath(item.value)"
				>
					<template #prepend>
						<v-btn
							:disabled="!item.children?.length"
							:ripple="false"
							variant="text"
							size="x-small"
							:icon="isOpen?'i-mdi:minus':'i-mdi:plus'"
							v-bind="props"
							:class="{ 'opacity-0': !item.children?.length }"
						/>
						<v-icon icon="i-mdi:folder text-yellow-darken-1" />
					</template>
					<template #title="{ title }">
						{{ title }} <span class="text-caption">({{ item.totalItems }})</span>
					</template>
					<template #append>
						<v-menu
							close-on-back
							close-on-content-click
						>
							<template #activator="{ props }">
								<v-btn
									v-bind="props"
									icon="i-mdi-dots-vertical"
									size="small"
									variant="text"
								/>
							</template>
							<v-list
								density="compact"
								slim
							>
								<v-list-item
									v-if="can('symbol.create')"
									prepend-icon="i-mdi:folder-plus-outline"
									:title="$t('PathTreeItem.add-directory')"
									size="small"
									@click="addDir(item)"
								/>
								<v-list-item
									v-if="can('symbol.delete')"
									prepend-icon="i-mdi:delete-outline"
									:title="$t('PathTreeItem.delete-directory')"
									size="small"
									@click="removeDir(item)"
								/>
							</v-list>
						</v-menu>
					</template>
				</v-list-item>
			</template>

			<PathTreeItem
				v-for="(child, i) in item.children"
				:key="i"
				:item="child"
				@click.stop="selectPath(child.value)"
				@path-select="selectPath"
				@add-dir="addDir"
				@remove-dir="removeDir"
			/>
		</v-list-group>
	</v-lazy>
</template>

<script lang="ts" setup>
// import PathTreeItem from './PathTreeItem.vue'
import type { Symbols } from '~/types'

type Props = {
	item: Symbols.MT5.Paths.Lookup.SingleRecord
}

type Emit = {
	(e: 'path-select', value: string): void
	(e: 'add-dir' | 'removeDir', value: Symbols.MT5.Paths.Lookup.SingleRecord): void

}

defineProps<Props>()

const emit = defineEmits<Emit>()

const selectPath = (value: string) => {
	emit('path-select', value)
}

const addDir = (item: Symbols.MT5.Paths.Lookup.SingleRecord) => {
	emit('add-dir', item)
}

const removeDir = (item: Symbols.MT5.Paths.Lookup.SingleRecord) => {
	emit('removeDir', item)
}
</script>
