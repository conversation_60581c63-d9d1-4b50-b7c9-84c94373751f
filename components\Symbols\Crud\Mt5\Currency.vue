<template>
	<v-container>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.Currency.base') }}</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						v-model="item.currencyBase"
						:items="currencyItems || []"
						:error-messages="errors.currencyBase"
						:rules="rules({ required: true })"
						item-title="symbol"
						:label="$t('Symbols.MT5.Crud.Currency.base-currency')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.currencyBaseDigits"
						:error-messages="errors.currencyBaseDigits"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Currency.base-currency-digits')"
						suffix="digits"
					/>
				</v-col>
			</v-row>
		</fieldset>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.Currency.profit') }}</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						v-model="item.currencyProfit"
						:items="currencyItems || []"
						:error-messages="errors.currencyProfit"
						:rules="rules({ required: true })"
						item-title="symbol"
						:label="$t('Symbols.MT5.Crud.Currency.profit-currency')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.currencyProfitDigits"
						:error-messages="errors.currencyProfitDigits"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Currency.profit-currency-digits')"
						suffix="digits"
					/>
				</v-col>
			</v-row>
		</fieldset>
		<fieldset class="mb-2">
			<legend>{{ $t('Symbols.MT5.Crud.Currency.margin') }}</legend>
			<v-row>
				<v-col
					cols="12"
					md="6"
				>
					<v-select
						v-model="item.currencyMargin"
						:items="currencyItems || []"
						:error-messages="errors.currencyMargin"
						:rules="rules({ required: true })"
						item-title="symbol"
						:label="$t('Symbols.MT5.Crud.Currency.margin-currency')"
					/>
				</v-col>
				<v-col
					cols="12"
					md="6"
				>
					<v-text-field
						v-model="item.currencyMarginDigits"
						:error-messages="errors.currencyMarginDigits"
						:rules="rules({ required: true })"
						type="number"
						hide-spin-buttons
						:label="$t('Symbols.MT5.Crud.Currency.margin-currency-digits')"
						suffix="digits"
					/>
				</v-col>
			</v-row>
		</fieldset>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props } from './Shared'
import { Currencies } from '~/types'

withDefaults(defineProps<Props>(), defaults)

const { data: currencyItems } = useApi<Currencies.Lookup.GETResponse>(Currencies.Lookup.URL)
</script>
