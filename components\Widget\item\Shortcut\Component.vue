<template>
	<v-card
		height="100%"
		width="100%"
		:to="data.to"
		variant="text"
		class="pt-2 px-2"
	>
		<div
			style="max-width: 100%;max-height: 100%"
			class="text-center overflow-hidden"
		>
			<v-icon
				size="36"
				class="text-medium-emphasis"
			>
				{{ data.icon }}
			</v-icon>
			<div
				class="text-caption text-wrap line-truncate-2"
				style="line-height: 1.1;font-size:10px"
			>
				{{ data.name }}
			</div>
		</div>
	</v-card>
</template>

<script lang="ts" setup>
import type { RouteLocationRaw } from 'vue-router'
import type { WidgetItemProps } from '../../types'

type Props = WidgetItemProps & {
	options: {
		routeName: string
	}
}

type Data = {
	icon: string
	name: string
	to: RouteLocationRaw
}

const p = defineProps<Props>()

const previewData = ref<Data>({
	icon: 'i-mdi:account-group',
	name: 'Users Management',
	to: { name: 'reports-online-traders' },
})

const realData = ref<Data>({
	icon: 'i-mdi:account-group',
	name: 'Transactions Workflow',
	to: { name: 'reports-online-traders' },
})

const data = computed<Data>(() => {
	if (p.isPreview || !p.options?.routeName) {
		return previewData.value
	}

	return realData.value
})
</script>
