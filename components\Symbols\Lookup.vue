<template>
	<api-items
		v-slot="apiItemsProps"
		:url="computedUrl"
		:error-messages="p.errorMessages"
		:map-items="mapper"
	>
		<component
			:is="computedComponent"
			v-bind="{ ...p, ...$attrs, ...apiItemsProps }"
		>
			<template
				v-for="(_, name) in slots"
				#[name]="slotProps"
			>
				<!-- @vue-ignore -->
				<slot
					:name="name"
					v-bind="{ ...slotProps ?? {} }"
				/>
			</template>
		</component>
	</api-items>
</template>

<script lang="ts" setup>
import { VAutocomplete, VSelect, VCombobox } from 'vuetify/components'
import { Symbols } from '~/types'

defineOptions({
	// extends: VAutocomplete,
	inheritAttrs: false,
})

type VuetifyProps = VAutocomplete['$props'] & VSelect['$props'] & VCombobox['$props']

type VuetifySlots = VAutocomplete['$slots'] & VSelect['$slots'] & VCombobox['$slots']

interface Props extends /* @vue-ignore */ VuetifyProps {
	component?: 'autocomplete' | 'combobox' | 'select'
	return?: Symbols.MT5.Lookup.Type | 'all'
}

interface Slots extends /* @vue-ignore */ VuetifySlots {}

const p = withDefaults(defineProps<Props>(), {
	component: 'autocomplete',
	return: 'symbol',
})

const slots = defineSlots<Slots>()

const currentServer = useCurrentMtServer()

const computedUrl = computed(() => {
	if (currentServer.value?.tradingPlatform.name === 'MT4') {
		return Symbols.MT4.Lookup.URL
	} else {
		return Symbols.MT5.Lookup.URL
	}
})

const computedComponent = computed(() => {
	switch (p.component) {
		case 'combobox':
			return VCombobox
		case 'select':
			return VSelect
		case 'autocomplete':
		default:
			return VAutocomplete
	}
})

const mapper = (items: Symbols.MT4.Lookup.GETResponse | Symbols.MT5.Lookup.GETResponse) => {
	if (currentServer.value?.tradingPlatform.name === 'MT4') {
		return items as Symbols.MT4.Lookup.GETResponse
	} else {
		const result: string[] = [];

		(items as Symbols.MT5.Lookup.GETResponse).reduce((accumulator, item) => mapperRecursiveHelper(accumulator, item), result)

		return result
	}
}

const mapperRecursiveHelper = (result: string[],	item: Symbols.MT5.Lookup.SingleRecord): string[] => {
	if (item.type === p.return || p.return === 'all') {
		result.push(item.title)
	}

	if (item.children) {
		item.children.reduce(mapperRecursiveHelper, result)
	}

	return result
}
</script>
