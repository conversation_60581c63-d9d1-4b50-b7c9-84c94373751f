<template>
	<crud
		ref="crudRef"
		:default-item="defaultItem"
		:item-name="$t('RolesCrud.role')"
		item-identity="displayName"
		:navigation-drawer-props="{ width: 650 }"
		url="/roles/:id"
		v-bind="$attrs"
		@closed="tab = 'info'"
	>
		<template #default="{ item, errors }: { item: Item<Roles._Id.GETResponse>, errors: ItemErrors<Roles._Id.GETResponse>}">
			<div class="d-flex flex-row">
				<v-tabs
					v-model="tab"
					color="accent"
					class="flex-shrink-0 pe-2 border-e me-3"
					direction="vertical"
				>
					<v-badge
						:model-value="!!infoTabErrorsCount"
						:offset-y="10"
						:offset-x="10"
						:content="infoTabErrorsCount"
						color="error"
					>
						<v-tab
							prepend-icon="i-mdi:account-outline"
							:text="$t('RolesCrud.information')"
							value="info"
						/>
					</v-badge>
					<v-badge
						:model-value="!!permissionsTabErrorsCount"
						:offset-y="10"
						:offset-x="10"
						:content="permissionsTabErrorsCount"
						color="error"
					>
						<v-tab
							prepend-icon="i-mdi-shield-outline"
							:text="$t('RolesCrud.permissions')"
							value="permissions"
						/>
					</v-badge>
				</v-tabs>
				<v-tabs-window
					v-model="tab"
					class="flex-grow-1"
				>
					<v-tabs-window-item
						:eager="true"
						value="info"
					>
						<v-text-field
							ref="nameField"
							v-model="item.displayName"
							:error-messages="errors.displayName"
							:rules="rules({ required: true })"
							:label="$t('RolesCrud.display-name')"
						/>
						<v-textarea
							ref="descriptionField"
							v-model="item.description"
							:rules="rules({ required: true })"
							:label="$t('RolesCrud.descriptions')"
							:error-messages="errors.description"
						/>
					</v-tabs-window-item>

					<v-tabs-window-item
						:eager="true"
						value="permissions"
					>
						<select-permissions
							ref="permissionsField"
							v-model="item.permissions"
							:error-messages="errors.permissions"
						/>
					</v-tabs-window-item>
				</v-tabs-window>
			</div>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type { VInput, VTextarea } from 'vuetify/components'
import type Crud from './Crud.vue'
import type { DefaultItem, Item, ItemErrors } from './Crud.vue'
import type SelectPermissions from './SelectPermissions.vue'
import type { Roles } from '~/types/'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultItem: DefaultItem<Roles._Id.POSTRequest | Roles._Id.PUTRequest> = {
	displayName: '',
	description: '',
	permissions: [],
}

const tab = ref('info')

const nameField = ref<InstanceType<typeof VInput> | null>(null)
const descriptionField = ref<InstanceType<typeof VTextarea> | null>(null)
const permissionsField = ref<InstanceType<typeof SelectPermissions> | null >(null)

const infoTabErrorsCount = computed(() => {
	return [nameField, descriptionField].filter(field => field?.value?.isValid === false).length
})

const permissionsTabErrorsCount = computed(() => {
	return permissionsField.value?.input?.isValid === false ? 1 : 0
})

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
