<template>
	<crud
		ref="crudRef"
		:default-item="defaultCategoryItem"
		item-name="Category"
		item-identity="name"
		:navigation-drawer-props="{ width: 500 }"
		:url="Categories._Id.URL(':id')"
		v-bind="$attrs"
	>
		<template #default="{ item, errors }: { item: Item<Categories._Id.GETResponse | Categories._Id.POSTRequest | Categories._Id.PUTRequest>, errors: ItemErrors<Categories._Id.GETResponse>, isCreateAction: boolean}">
			<v-text-field
				v-model="item.name"
				:error-messages="errors.name"
				label="Name"
				:rules="rules({ required: true })"
			/>
			<v-text-field
				v-model="item.displayName1"
				:error-messages="errors.displayName1"
				label="Display Name 1"
				:rules="rules({ required: true })"
			/>
			<v-text-field
				v-model="item.displayName2"
				:error-messages="errors.displayName2"
				label="Display Name 2"
				:rules="rules({ required: true })"
			/>
		</template>
	</crud>
</template>

<script lang="ts" setup>
import type Crud from '~/components/Crud.vue'
import type { DefaultItem, Item, ItemErrors } from '~/components/Crud.vue'
import { Categories } from '~/types'

const crudRef = ref<InstanceType<typeof Crud> | null>(null)

const defaultCategoryItem: DefaultItem<Categories._Id.POSTRequest | Categories._Id.PUTRequest> = {
	name: '',
	displayName1: '',
	displayName2: '',
}

defineExpose({
	create: () => crudRef.value?.create(),
	update: (item: any) => crudRef.value?.update(item),
	delete: (item: any) => crudRef.value?.delete(item),
})
</script>
