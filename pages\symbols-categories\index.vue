<template>
	<page title="Symbols Categories">
		<template #actions>
			<v-btn
				v-if="can('alerts.view')"
				prepend-icon="i-mdi-bell-outline"
				color="primary"
				variant="text"
				:to="{ name: 'symbols-categories-alerts' }"
			>
				Alerts
			</v-btn>
			<v-btn
				v-if="can('category.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				variant="text"
				@click="categoriesCrudRef?.create()"
			>
				Create Category
			</v-btn>
			<v-btn
				v-if="can('symbolcategory.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="categoriesSymbolsCrudRef?.create(currentCategory?.id)"
			>
				Assign Symbol to "{{ currentCategory?.name }}"
			</v-btn>
		</template>
		<datatable
			ref="table"
			:headers="headers"
			search-key="name"
			:items="currentCategoryItems"
			:loading="status === 'pending'"
		>
			<template #toolbar.refresh>
				<v-btn
					v-tooltip:top="'Refresh'"
					:disabled="status === 'pending'"
					icon="i-mdi:refresh"
					@click="refresh()"
				/>
			</template>
			<template #prepend="{ height }">
				<v-sheet
					class="fill-height pe-2 me-2 border-e "
					min-width="300"
				>
					<v-toolbar
						color="surface"
						density="compact"
						class="mb-2"
					>
						<v-text-field
							v-model="searchCategoryModel"
							clearable
							placeholder="Find Category"
							append-inner-icon="i-mdi:magnify"
							flat
							variant="solo-filled"
							hide-details
							density="comfortable"
							class="rounded-t-lg"
						/>
					</v-toolbar>
					<v-sheet
						class="overflow-y-scroll"
						:max-height="`calc(${height} + var(--datatable-footer-height) - 6px) `"
					>
						<v-skeleton-loader
							type="list-item@4"
							:loading="status === 'pending' && !computedCategories?.length"
						>
							<v-list>
								<v-list-item
									v-for="category in computedCategories"
									:key="category.id"
									:title="category.name"
									:active="category.id === currentCategory?.id"
									@click="selectCategory(category)"
								>
									<template #append>
										<v-list-item-action end>
											<v-btn
												v-if="can('symbolcategory.edit')"
												icon="i-mdi:pencil"
												variant="text"
												size="x-small"
												@click="categoriesCrudRef?.update(category)"
											/>
											<v-btn
												v-if="can('symbolcategory.delete')"
												icon="i-mdi:delete"
												variant="text"
												size="x-small"
												@click="categoriesCrudRef?.delete(category)"
											/>
										</v-list-item-action>
									</template>
								</v-list-item>
							</v-list>
						</v-skeleton-loader>
						<!-- <symbols-categories-tree
							ref="pathTreeRef"
							v-model="lastOpenedPath"
							v-model:search="debouncedSearchPathModel"
							@loaded="pathsData = $event"
							@deleted="pathDeleteHandler"
						/> -->
					</v-sheet>
				</v-sheet>
			</template>

			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('symbol.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="categoriesSymbolsCrudRef?.update(item)"
					/>
					<v-btn
						v-if="can('symbol.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="categoriesSymbolsCrudRef?.delete(item)"
					/>
				</div>
			</template>
		</datatable>
	</page>
	<CategoriesCrud
		ref="categoriesCrudRef"
		@created="refresh()"
		@updated="refresh()"
		@deleted="refresh()"
	/>
	<CategoriesSymbolsCrud
		ref="categoriesSymbolsCrudRef"
		@created="refresh()"
		@updated="refresh()"
		@deleted="refresh()"
	/>
</template>

<script lang="ts" setup>
import CategoriesCrud from '@/components/Categories/Crud.vue'
import CategoriesSymbolsCrud from '@/components/CategoriesSymbols/Crud.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import { Categories } from '~/types'

const table = ref<InstanceType<typeof Datatable> | null>(null)

const categoriesCrudRef = ref<InstanceType<typeof CategoriesCrud> | null>(null)

const categoriesSymbolsCrudRef = ref<InstanceType<typeof CategoriesSymbolsCrud> | null>(null)

const searchCategoryModel = ref('')

const currentCategory = ref<Categories.Record>()

const debouncedSearchCategoryModel = useDebounce(searchCategoryModel, 300)

const computedCategories = computed(() => {
	if (debouncedSearchCategoryModel.value) {
		return symbolsCategories.value?.items.filter((category) => {
			return category.name.toLowerCase().includes(debouncedSearchCategoryModel.value.toLowerCase())
		})
	}

	return symbolsCategories.value?.items
})

const { data: symbolsCategories, refresh: refreshApi, status } = useApi<Categories.GETResponse>(Categories.URL)

const unwatch = watch(status, (value) => {
	if (value === 'success' && (symbolsCategories.value?.items?.length || 0) > 0) {
		selectCategory(symbolsCategories.value!.items[0])
		unwatch()
	}
}, { immediate: true })

const currentCategoryItems = ref<Categories.Record['symbolsCategory']>([])

const selectCategory = (category: Categories.Record) => {
	currentCategory.value = category
	currentCategoryItems.value = category.symbolsCategory
}

const refresh = () => {
	refreshApi().then(() => {
		// get the refreshed data
		const category = symbolsCategories.value?.items.find(category => category.id === currentCategory.value?.id)
		if (category) {
			selectCategory(category)
		} else {
			selectCategory(symbolsCategories.value!.items[0])
		}
	})
}

const headers: Headers = [
	{
		title: 'Symbol',
		value: 'name',
		width: '200px',
		// nowrap: true,
	},
	{
		title: 'Date',
		value: 'timestamp',
	},
	{
		value: 'actions',
	},
]
</script>
