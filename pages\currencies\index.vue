<template>
	<div>
		<page :title="$t('currencies.currencies')">
			<template #actions>
				<v-btn
					v-if="can('currencies.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('currencies.create-currency') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/currencies"
				:headers="headers"
				search-key="name"
			>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('currencies.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('currencies.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<currencies-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type CurrenciesCrud from '@/components/CurrenciesCrud.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'

definePageMeta({
	permission: 'currencies.view',
})

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof CurrenciesCrud | null>(null)

const { t } = useI18n()

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('currencies.name'),
		value: 'name',
	},
	{
		title: t('currencies.symbol'),
		value: 'symbol',
	},
	{
		title: t('currencies.date'),
		value: 'timestamp',
		align: 'center',
	},
	{
		value: 'actions',
	},
]
</script>
