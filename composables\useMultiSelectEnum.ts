import { defu } from 'defu'
import type { WritableComputedRef } from 'vue'

/**
 * Custom hook for handling multi-select enums, providing reactive state management.
 *
 * @param initialValue - The initial numeric value representing selected flags.
 * @param enumType - An object where keys are string representations and values are numeric flags.
 * @returns An object with computed properties:
 *  - model: A computed property for getting and setting flag values as an array of numbers.
 *
 * Example:
 * const { model } = useMultiSelectEnum(initialValue, enumType);
 *
 */
type Options = {
	/**
	 * An array of values to exclude from the returned array.
	 */
	exclude: any[]
}
const defaultOptions: Options = {

	exclude: [],
}
export const useMultiSelectEnum = (computedValue: WritableComputedRef<number> | Ref<number>, enumType: { [s: string]: any }, options?: Partial<Options>) => {
	const _options = defu(options, defaultOptions)
	const model = computed({
		get() {
			const flags: number[] = []
			Object.values(enumType).forEach((flag) => {
				// Include flag only if it is a power of two or zero
				if (!_options.exclude.includes(flag) && (flag & (flag - 1)) === 0 && (computedValue.value & flag) === flag) {
					flags.push(flag)
				}
			})
			return [...new Set(flags)]
		},
		set(newFlags: number[]) {
			let flags = 0
			newFlags.forEach((flag) => {
				if (Object.values(enumType).includes(flag)) {
					flags |= flag
				}
			})
			computedValue.value = flags
		},
	})

	return { model }
}
