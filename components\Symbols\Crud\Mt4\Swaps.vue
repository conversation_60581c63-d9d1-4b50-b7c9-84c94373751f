<template>
	<v-container
		fluid
		class="py-0"
	>
		<v-switch
			v-model="item.swapEnable"
			color="success"
			:label="$t('Symbols.MT4.Crud.Swaps.enable-swaps')"
			:true-value="1"
			:false-value="0"
		/>
		<v-defaults-provider
			:defaults="{
				global: {
					disabled: !item.swapEnable,
				},
			}"
		>
			<fieldset>
				<legend class="mb-2">
					{{ $t('Symbols.MT4.Crud.Swaps.settings') }}
				</legend>

				<v-row dense>
					<v-col cols="12">
						<v-select
							v-model="item.swapType"
							:error-messages="errors.swapType"
							:items="swapTypeItems"
							:rules="rules({ required: true })"
							:label="$t('Symbols.MT4.Crud.Swaps.type')"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model.number="item.swapLong"
							:error-messages="errors.swapLong"
							:rules="rules({ required: true })"
							type="number"
							hide-spin-buttons
							:label="$t('Symbols.MT4.Crud.Swaps.long-positions')"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-text-field
							v-model.number="item.swapShort"
							:error-messages="errors.swapShort"
							:rules="rules({ required: true })"
							type="number"
							hide-spin-buttons
							:label="$t('Symbols.MT4.Crud.Swaps.short-positions')"
						/>
					</v-col>
					<v-col
						cols="12"
						md="6"
					>
						<v-select
							v-model="item.swapRollover3Days"
							:error-messages="errors.swapRollover3Days"
							:items="swapRollover3DaysItems"
							:rules="rules({ required: true })"
							:label="$t('Symbols.MT4.Crud.Swaps.days-in-year')"
						/>
					</v-col>
					<v-col cols="12">
						<v-checkbox
							v-model="item.swapOpenPrice"
							:true-value="1"
							:false-value="0"
							color="primary"
							hide-details
							:label="$t('Symbols.MT4.Crud.Swaps.use-open-price-for-positi')"
						/>
						<v-checkbox
							v-model="item.swapVariationMargin"
							:true-value="1"
							:false-value="0"
							color="primary"
							hide-details
							:disabled="false"
							:label="$t('Symbols.MT4.Crud.Swaps.charge-variation-margin-o')"
						/>
					</v-col>
				</v-row>
			</fieldset>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Symbols } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const swapTypeItems = enumToItems(Symbols.MT4.SwapType, 'Symbols.MT4.SwapType')

const swapRollover3DaysItems = enumToItems(Symbols.MT4.Days, 'Symbols.MT4.Days', item => [Symbols.MT4.Days.SATURDAY, Symbols.MT4.Days.SUNDAY].includes(item.value) ? undefined : item)
</script>
