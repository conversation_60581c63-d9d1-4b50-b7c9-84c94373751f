import type { Pagination } from '../Helpers'

export namespace CategoriesSymbols {
	export const URL = '/symbols-categories'
	export interface SymbolsCategory {
		id: number
		name: string
		createdAt: string
	}

	export interface Record {
		id: number
		name: string
		categoryId: number
		createdAt: string
		category: Category
		symbolsCategory: SymbolsCategory[]
	}

	export interface Category {
		id: number
		name: string
		createdAt: string
	}

	export interface GETResponse extends Pagination<Record> {}

	export namespace _Id {
		export const URL = (_Id: number | string) => `/symbols-categories/${_Id}`

		export type GETResponse = Record

		export type PUTRequest = Omit<Record, 'id' | 'createdAt' | 'category'>

		export type POSTRequest = Omit<Record, 'id' | 'createdAt' | 'category'>
	}

}
