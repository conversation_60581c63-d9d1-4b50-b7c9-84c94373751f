export namespace Manager {
	export namespace MT4 {

		// export namespace Lookup {
		// 	export const URL = '/manager/mt4/lookup'
		// 	export interface SingleRecord {

		// 	}

		// 	export type GETResponse = SingleRecord[]
		// }

		export type ConManagerSec = {
			/**
			 * Internal data for security group configuration.
			 */
			internalData: number

			/**
			 * Enable flag for the group.
			 */
			enable: number

			/**
			 * Minimum lots allowed for the security group.
			 */
			minimumLots: number

			/**
			 * Maximum lots allowed for the security group.
			 */
			maximumLots: number

			/**
			 * Reserved field for future use.
			 */
			reserved: number[]
		}

		export type MTAccountStandard = {
			/**
			 * The login of the account, based on which the manager account is created.
			 */
			login: number

			/**
			 * A permission to add, edit and delete client accounts. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			manager: number

			/**
			 * A permission to conduct financial transactions (deposit, credit, withdrawal) on client accounts. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			money: number

			/**
			 * A permission to access statistics of connected clients. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			online: number

			/**
			 * A permission to receive information about client's aggregate positions and company's coverage positions. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			riskman: number

			/**
			 * A permission to process client requests and perform trading operations on their accounts. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			broker: number

			/**
			 * Full access to the server settings. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			admin: number

			/**
			 * A permission to request and receive server logs. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			logs: number

			/**
			 * A permission to request and receive various reports on client operations. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			reports: number

			/**
			 * A permission to modify and delete clients' open positions (potentially dangerous). A nonzero value - the permission is enabled, 0 - disabled.
			 */
			trades: number

			/**
			 * A permission to add their own quotes to the data stream, change types of spreads and execution. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			marketWatch: number

			/**
			 * A permission to send messages via the internal e-mail system. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			email: number

			/**
			 * Permission to view personal details on client accounts. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			userDetails: number

			/**
			 * Permission to view clients' open positions. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			seeTrades: number

			/**
			 * A permission to send messages to a news feed. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			news: number

			/**
			 * A permission to configure server plugins via the manager terminal. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			plugins: number

			/**
			 * A permission to receive automatic server reports. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			serverReports: number

			/**
			 * A permission to access the "Support" tab in the manager and administrator terminal. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			techSupport: number

			/**
			 * Access to the market of server applications in the administrator terminal. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			market: number

			/**
			 * A permission to send push notifications via the manager terminal or Manager API. A nonzero value - the permission is enabled, 0 - disabled.
			 */
			notifications: number

			/**
			 * Unused field.
			 */
			unusedRights: number[]

			/**
			 * A flag of manager connection filtering by IP. A nonzero value - the flag is enabled, 0 - disabled.
			 */
			ipFilter: number

			/**
			 * Start of allowed IP range for manager connections.
			 */
			ipFrom: number

			/**
			 * End of allowed IP range for manager connections.
			 */
			ipTo: number

			/**
			 * The name of the manager's mailbox for the internal mailing system.
			 */
			mailbox: string

			/**
			 * The list of groups processed by the manager, separated by commas.
			 */
			groups: string

			/**
			 * An array of trade request routing settings for handling.
			 */
			securityGroups: ConManagerSec[]

			/**
			 * Internal data.
			 */
			expirationTime: number

			/**
			 * The name of the manager.
			 */
			name: string

			/**
			 * Maximum number of days for requesting historical reports.
			 */
			infoDepth: number

			/**
			 * A reserved field.
			 */
			reserved: number[]
		}

		export namespace _Id {
			export const URL = (_Id: number | string) => `/manager/mt4/${_Id}`

			export interface GETResponse extends MTAccountStandard { }

			export interface POSTRequest extends MTAccountStandard { }

			export interface PUTRequest extends MTAccountStandard { }

		}

		export namespace Roles{

			export type Role = {
				name: string
				permissions: Pick<MTAccountStandard, 'logs' | 'news' | 'admin' | 'email' | 'money' | 'market' | 'online' | 'trades' | 'manager' | 'plugins' | 'reports' | 'riskman' | 'seeTrades' | 'marketWatch' | 'techSupport' | 'userDetails' | 'notifications' | 'serverReports' >
			}
			export namespace Lookup{
				export const URL = '/manager/mt4/roles/lookup'

				export type GETResponse = Role[]

			}

			export namespace _Name{
				export const URL = `/manager/mt4/roles`

				export const DeleteURL = (name: string) => `/manager/mt4/roles/${name}`

				export type GETResponse = Role

				export type POSTRequest = Role

				export type DELETERequest = Role
			}

		}

	}

	export namespace MT5 {
		/**
		 * Enum for manager rights in IMTConManager.
		 */
		export enum EnManagerRights {
			/** Connection using the administrator terminal. */
			RIGHT_ADMIN = 0,

			/** Connection using the manager terminal. */
			RIGHT_MANAGER = 1,

			/** Network configuration */
			RIGHT_CFG_SERVERS = 10,

			/** Configuration of the list of IP access. */
			RIGHT_CFG_ACCESS = 11,

			/** Configuration of the server working time. */
			RIGHT_CFG_TIME = 12,

			/** Configuration of holidays. */
			RIGHT_CFG_HOLIDAYS = 13,

			/** Configuration of synchronization. */
			RIGHT_CFG_HST_SYNC = 14,

			/** Configuration of symbols. */
			RIGHT_CFG_SYMBOLS = 15,

			/** Configuration of groups. */
			RIGHT_CFG_GROUPS = 16,

			/** Configuration of manager rights. */
			RIGHT_CFG_MANAGERS = 17,

			/** Configuration of data feeds. */
			RIGHT_CFG_DATAFEEDS = 18,

			/** Configuration of the routing table. */
			RIGHT_CFG_REQUESTS = 19,

			/** Configuration of Leverage. */
			RIGHT_CFG_LEVERAGE = 1900,

			/** Access to server journals. */
			RIGHT_SRV_JOURNALS = 20,

			/** Receive automatic server reports. */
			RIGHT_SRV_REPORTS = 21,

			/** Editing history data on the server. Additional RIGHT_CFG_SYMBOLS required. */
			RIGHT_CHARTS = 22,

			/** Sending internal emails. */
			RIGHT_EMAIL = 23,

			/** Permission to work with funds on accounts. */
			RIGHT_ACCOUNTANT = 24,

			/** Access to accounts. */
			RIGHT_ACC_READ = 25,

			/** Access to account details. */
			RIGHT_ACC_DETAILS = 26,

			/** Editing accounts. */
			RIGHT_ACC_MANAGER = 27,

			/** Getting the current client connections. */
			RIGHT_ACC_ONLINE = 28,

			/** Viewing trading orders, deals, and positions. */
			RIGHT_TRADES_READ = 29,

			/** Permission to edit fields of orders, deals, and positions. */
			RIGHT_TRADES_MANAGER = 30,

			/** Permission to add quotes to the stream. */
			RIGHT_QUOTES = 31,

			/** Permission to receive client's aggregate positions info. */
			RIGHT_RISK_MANAGER = 32,

			/** Permission to request and receive various reports. */
			RIGHT_REPORTS = 33,

			/** Sending news. */
			RIGHT_NEWS = 34,

			/** Configuring gateways. */
			RIGHT_CFG_GATEWAYS = 35,

			/** Configuring plugins. */
			RIGHT_CFG_PLUGINS = 36,

			/** Trading and dealing activities in the manager terminal. */
			RIGHT_TRADES_DEALER = 37,

			/** Configuration of reports. */
			RIGHT_CFG_REPORTS = 38,

			/** Permission to export data to external files. */
			RIGHT_EXPORT = 39,

			/** Permission to change spread and execution mode. */
			RIGHT_SYMBOL_DETAILS = 40,

			/** Access to the Technical Support tab (obsolete). */
			RIGHT_TECHSUPPORT = 41,

			/** View and track the entire queue of requests. */
			RIGHT_TRADES_SUPERVISOR = 42,

			/** View quotes without spread markup. */
			RIGHT_QUOTES_RAW = 43,

			/** Access to the Market of applications (obsolete). */
			RIGHT_MARKET = 44,

			/** Change margin settings of groups. */
			RIGHT_GRP_DETAILS_MARGIN = 45,

			/** Send push notifications to clients' mobile devices. */
			RIGHT_NOTIFICATIONS = 46,

			/** Deleting client accounts. Requires RIGHT_ACC_MANAGER. */
			RIGHT_ACC_DELETE = 47,

			/** Deleting orders, accounts, and positions. Requires RIGHT_TRADES_MANAGER. */
			RIGHT_TRADES_DELETE = 48,

			/** Disable confirmation dialog for balance operations and closing orders. */
			RIGHT_CONFIRM_ACTIONS = 49,

			/** ECN settings. */
			RIGHT_CFG_ECN = 50,

			/** Change commission settings of groups. */
			RIGHT_GRP_DETAILS_COMMISSION = 51,

			/** View existing settings in the Subscriptions section. */
			RIGHT_SUBSCRIPTIONS_VIEW = 52,

			/** Create, change, and remove settings in the Subscriptions section. */
			RIGHT_SUBSCRIPTIONS_EDIT = 53,

			/** Configure investment funds. */
			RIGHT_CFG_FUNDS = 54,

			/** Configure integration with email services. */
			RIGHT_CFG_MAILS = 55,

			/** Configure integration with SMS and messengers. */
			RIGHT_CFG_MESSENGERS = 56,

			/** Configure integration with KYC services. */
			RIGHT_CFG_KYC = 57,

			/** Configure automatic actions for scenarios. */
			RIGHT_CFG_AUTOMATIONS = 58,

			/** Access Allocations section for account creation from terminals. */
			RIGHT_CFG_ALLOCATIONS = 59,

			/** Configure Sponsored VPS for traders. */
			RIGHT_CFG_VPS = 60,

			/** Configure integration with payment systems (in development). */
			RIGHT_CFG_PAYMENTS = 61,

			/** Access server machine administration menu. */
			RIGHT_ADMIN_COMPUTER = 62,

			/** Configure integration with web services and SSL certificates. */
			RIGHT_CFG_WEB_SERVICES = 63,

			/** Access Finteza Analytics section. */
			RIGHT_FINTEZA_ACCESS = 64,

			/** View Finteza data related to websites. */
			RIGHT_FINTEZA_WEBSITES = 65,

			/** View Finteza data related to marketing campaigns. */
			RIGHT_FINTEZA_CAMPAIGNS = 66,

			/** Permission currently not used. */
			RIGHT_FINTEZA_REPORTS = 67,

			/** Convenience in working with testing and technical accounts. */
			RIGHT_ACC_TECHNICAL = 70,

			/** Enable/disable permissions for a trading account. */
			RIGHT_ACC_TECHNICAL_MODIFY = 71,

			/** Confirm and reject manual payments. */
			RIGHT_PAYMENTS_PROCESS = 72,

			/** Access to corporate link settings. */
			RIGHT_CFG_CORPORATES = 73,

			/** Access to the "Clients" section. */
			RIGHT_CLIENTS_ACCESS = 96,

			/** Permission to create new client entries manually. */
			RIGHT_CLIENTS_CREATE = 97,

			/** Permission to edit client data except documents. */
			RIGHT_CLIENTS_EDIT = 98,

			/** Permission to delete client entries. */
			RIGHT_CLIENTS_DELETE = 99,

			/** Permission to view client documents. */
			RIGHT_DOCUMENTS_ACCESS = 100,

			/** Permission to add information about documents. */
			RIGHT_DOCUMENTS_CREATE = 101,

			/** Permission to edit document information. */
			RIGHT_DOCUMENTS_EDIT = 102,

			/** Permission to delete document information. */
			RIGHT_DOCUMENTS_DELETE = 103,

			/** Permission to add document files. */
			RIGHT_DOCUMENTS_FILES_ADD = 104,

			/** Permission to delete document files. */
			RIGHT_DOCUMENTS_FILES_DELETE = 105,

			/** Permission to read comments to clients/documents. */
			RIGHT_COMMENTS_ACCESS = 106,

			/** Permission to write comments to clients/documents. */
			RIGHT_COMMENTS_CREATE = 107,

			/** Permission to delete comments to clients/documents. */
			RIGHT_COMMENTS_DELETE = 108,

			/** Launch automated validation of client data via KYC services. */
			RIGHT_CLIENTS_KYC = 109,

			/** Permission to view payments and payment accounts. */
			RIGHT_PAYMENTS_ACCESS = 110,

			/** Permission to edit payments and payment accounts. */
			RIGHT_PAYMENTS_EDIT = 111,

			/** Permission to delete payments and payment accounts. */
			RIGHT_PAYMENTS_DELETE = 112,
		}

		/**
		 * Enum for manager right flags in IMTConManager.
		 */
		export enum EnManagerRightFlags {
			/** The right is not granted. */
			RIGHT_FLAGS_DENIED = 0,

			/** The right is granted. */
			RIGHT_FLAGS_GRANTED = 1,
		}

		/**
		 * Enum for manager limit periods in IMTConManager.
		 */
		export enum EnManagerLimit {
			/** Unlimited. */
			MANAGER_LIMIT_ALL = 0,

			/** One month. */
			MANAGER_LIMIT_MONTHS_1 = 1,

			/** Three months. */
			MANAGER_LIMIT_MONTHS_3 = 2,

			/** Six months. */
			MANAGER_LIMIT_MONTHS_6 = 3,

			/** One year. */
			MANAGER_LIMIT_YEAR_1 = 4,

			/** Two years. */
			MANAGER_LIMIT_YEAR_2 = 5,

			/** Three years. */
			MANAGER_LIMIT_YEAR_3 = 6,
		}

		/**
		 * Represents a group with a path.
		 */
		export type Group = {
			/**
			 * The path to the group.
			 */
			group: string
		}

		/**
		 * Represents a range of IP addresses.
		 */
		export type IpAddressRange = {
			/**
			 * The beginning of the range of IP addresses, from which a manager account can connect.
			 */
			from: string

			/**
			 * The end of the range of IP addresses, from which a manager account can connect.
			 */
			to: string
		}

		export enum EnPermissionsFlags {
			/**
			 * Access is denied.
			 */
			PERMISSION_NONE = 0,

			/**
			 * Permission to view the report.
			 */
			PERMISSION_VIEW = 1,

			/**
			 * Permission to export report data to a file.
			 */
			PERMISSION_EXPORT = 2,

			/**
			 * Enumeration end. Corresponds to PERMISSION_EXPORT.
			 */
			PERMISSION_ALL = 3,
		}

		export type Report = {
			name: string
			permissions: EnPermissionsFlags
			limitDays: number
		}

		/**
		 * Represents the configuration for a manager.
		 */
		export type MTAccountStandard = {
			/**
			 * The login of a manager.
			 */
			login: number

			/**
			 * The name of the manager. A read-only field. Corresponds to the value of the appropriate field of the user based on whose record the manager is created.
			 */
			name?: string

			/**
			 * The name of the manager's mailbox in the internal mailing system.
			 */
			mailbox: string

			/**
			 * The ID of the trade server, to which the manager belongs.
			 */
			server?: number

			/**
			 * Manager permissions in the form of an array [1,1,0,0,...]. 1 — permission is granted, 0 — permission is not granted. The full list of permissions is described in the EnManagerRights enumeration.
			 */
			rights: EnManagerRights[]

			/**
			 * The time period of system logs that are available to a manager. Specified as a value of the EnManagerLimit enumeration.
			 */
			limitLogs: EnManagerLimit

			/**
			 * The time period of reports that are available to a manager. Specified as a value of the EnManagerLimit enumeration.
			 */
			limitReports: EnManagerLimit

			/**
			 * Account groups managed by the manager.
			 */
			groups: string[]

			/**
			 * Ranges of IP addresses, from which a manager is allowed to connect to the platform.
			 */
			accesses: IpAddressRange[]

			reports: Report[]
		}

		// export namespace Lookup {
		// 	export const URL = '/accounts/mt5/lookup'

		// 	export interface SingleRecord {

		// 	}

		// 	export type GETResponse = SingleRecord[]
		// }

		export namespace _Id {
			export const URL = (_Id: number | string) => `/manager/mt5/${_Id}`

			export interface GETResponse extends MTAccountStandard {

			}

			export interface POSTRequest extends MTAccountStandard {

			}

			export interface PUTRequest extends MTAccountStandard {

			}

		}

		export namespace Roles{

			export type Role = {
				name: string
				permissions: EnManagerRights[]
			}

			export namespace Lookup{
				export const URL = '/manager/mt5/roles/lookup'

				export type GETResponse = Role[]

			}

			export namespace _Name{
				export const URL = `/manager/mt5/roles`

				export const DeleteURL = (name: string) => `/manager/mt5/roles/${name}`

				export type GETResponse = Role

				export type POSTRequest = Role

				export type DELETERequest = Role
			}

		}

	}

}
