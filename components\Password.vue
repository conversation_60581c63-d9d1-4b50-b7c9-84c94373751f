<template>
	<v-text-field
		v-model="modelValue"
		v-bind="{ ...computedProps }"
	>
		<template #append-inner="{}">
			<v-icon
				rounded="circle"
				size="small"
				:icon="icon"
				style="opacity:1"
				@click="toggle"
			/>
		</template>
		<template #loader="{ isActive, color }">
			<v-progress-linear
				height="2"
				:indeterminate="isActive"
				:color="color"
			/>
			<v-progress-linear
				v-if="showStrength && modelValue"
				height="4"
				:model-value="passwordStrengthPercent"
				rounded-bar
				:color="strengthColor"
			/>
		</template>
		<template
			v-for="(_, name) in (slots as Slots)"
			#[name]="slotData"
			:key="name"
		>
			<!-- @vue-ignore -->
			<slot
				:name="name"
				v-bind="slotData"
			/>
		</template>
	</v-text-field>
</template>

<script lang="ts" setup>
import { VTextField } from 'vuetify/components/VTextField'

type VTextFieldPropTypes = Omit<VTextField['$props'], 'type' | 'modelValue'>

type VTextFieldSlots = VTextField['$slots']

interface Props extends /* @vue-ignore */ VTextFieldPropTypes {
	showStrength?: boolean
	disableCapitalCharStrength?: boolean
	disableSmallCharStrength?: boolean
	disableNumberStrength?: boolean
	disableSpecialStrength?: boolean
	disableLengthStrength?: boolean
	minLength?: number
}

const p = withDefaults(defineProps<Props>(), {
	counter: true,
	showStrength: false,
	disableCapitalCharStrength: false,
	disableSmallCharStrength: false,
	disableNumberStrength: false,
	disableSpecialStrength: false,
	disableLengthStrength: false,
	minLength: 8,
})

type Model = string

const modelValue = defineModel<Model>()

type Emit = {
	(e: 'update:strength', value: number): void
}

const emit = defineEmits<Emit>()

const show = ref(false)

const icon = computed(() => (show.value ? 'i-mdi:eye-off' : 'i-mdi:eye'))

const toggle = () => {
	show.value = !show.value
}

const computedProps = computed(() => {
	return {
		...p,
		type: show.value ? 'text' : 'password',
	}
})

const passwordStrengthPercent = computed(() => {
	let strength = 0
	const password = modelValue.value || '' as string
	const length = password.length
	const hasCapitalChar = /[A-Z]/.test(password)
	const hasSmallChar = /[a-z]/.test(password)
	const hasNumber = /\d/.test(password)
	const hasSpecialChar = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+/.test(password)

	if (length >= p.minLength && !p.disableLengthStrength) {
		strength += 20
	}
	if (hasCapitalChar && !p.disableCapitalCharStrength) {
		strength += 20
	}
	if (hasSmallChar && !p.disableSmallCharStrength) {
		strength += 20
	}
	if (hasNumber && !p.disableNumberStrength) {
		strength += 20
	}
	if (hasSpecialChar && !p.disableSpecialStrength) {
		strength += 20
	}

	return strength
})

const strengthColor = computed(() => {
	const percent = passwordStrengthPercent.value
	if (percent <= 50) {
		return 'error'
	} else if (percent <= 80) {
		return 'warning'
	} else if (percent <= 100) {
		return 'success'
	}

	return ''
})

watch(() => passwordStrengthPercent.value, (value) => {
	if (p.showStrength) {
		emit('update:strength', value)
	}
}, {
	immediate: true,
})

type Slots = Partial<Omit<VTextFieldSlots, 'loader'>>

const slots = defineSlots<Slots>()
</script>
