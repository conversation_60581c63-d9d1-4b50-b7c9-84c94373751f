<template>
	<v-container>
		<api-items
			v-slot="props"
			url="/lookups/mt-companies"
		>
			<v-select
				v-model="item.company"
				v-bind="props"
				:label="$t('Groups.MT5.Crud.Company.company')"
				:rules="rules({ required: true })"
			/>
		</api-items>
		<v-text-field
			v-model="item.companyPage"
			:label="$t('Groups.MT5.Crud.Company.company-site')"
		/>
		<v-text-field
			v-model="item.companyEmail"
			:rules="rules({ type: 'email' })"
			:label="$t('Groups.MT5.Crud.Company.company-email')"
		/>
		<v-text-field
			v-model="item.companyDepositPage"
			:rules="rules({ type: 'url' })"
			:label="$t('Groups.MT5.Crud.Company.deposit-url')"
		/>
		<v-text-field
			v-model="item.companyWithdrawalPage"
			:rules="rules({ type: 'url' })"
			:label="$t('Groups.MT5.Crud.Company.withdrawal-url')"
		/>
		<v-text-field
			v-model="item.companySupportPage"
			:rules="rules({ type: 'url' })"
			:label="$t('Groups.MT5.Crud.Company.support-site')"
		/>
		<v-text-field
			v-model="item.companySupportEmail"
			:rules="rules({ type: 'email' })"
			:label="$t('Groups.MT5.Crud.Company.support-email')"
		/>
		<v-text-field
			v-model="item.companyCatalog"
			:label="$t('Groups.MT5.Crud.Company.templates-path')"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
// import type { Groups } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()
</script>
