<template>
	<v-input
		ref="inputRef"
		class="flex-grow-1"
		:rules="p.rules"
		:error-messages="p.errorMessages"
	>
		<div class="flex-grow-1">
			<v-label text="MT Servers" />
			<v-skeleton-loader
				:loading="pending"
				type="list-item-two-line@4"
				class="d-block mt-2"
			>
				<v-expansion-panels elevation="1">
					<trading-servers-select-panel
						v-for="item, i in data"
						:key="i"
						v-model="model"
						:item="item"
					/>
				</v-expansion-panels>
			</v-skeleton-loader>
		</div>
	</v-input>
</template>

<script lang="ts" setup>
import type { VInput } from 'vuetify/components'
import type { Users } from '~/types/Users'
import { TradingServers } from '~/types'

const { errorSnackbar } = useSnackbar()
const { data, error, pending } = useApi<TradingServers.Lookup.GETResponse>(TradingServers.Lookup.URL)

const inputRef = ref<VInput | null>(null)

type Props = {
	errorMessages?: InstanceType<typeof VInput>['$props']['errorMessages']
	rules?: InstanceType<typeof VInput>['$props']['rules']
	modelValue: Users._Id.GETResponse['tradingServers']
}
const p = withDefaults(defineProps<Props>(), {
	errorMessages: () => [],
	rules: () => [],
})

const emit = defineEmits(['update:modelValue'])

const model = computed({
	get: () => p.modelValue,
	set: (value: Users._Id.GETResponse['tradingServers']) => emit('update:modelValue', value),
})

if (error.value) {
	errorSnackbar(error.value.message || 'An error occurred while fetching permissions')
}

defineExpose({
	input: inputRef,
})
</script>
