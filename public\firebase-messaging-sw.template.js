importScripts(`https://www.gstatic.com/firebasejs/10.14.0/firebase-app-compat.js`);
importScripts(`https://www.gstatic.com/firebasejs/10.14.0/firebase-messaging-compat.js`);

const options = JSON.parse('<%= options %>')


// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.

// https://firebase.google.com/docs/web/setup#config-object

firebase.initializeApp(options.fcm.firebaseOptions);


// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();
// console.log("🚀 ~ messaging:", messaging)

messaging.onBackgroundMessage((payload,) => {

	if (!!payload.notification) {
		if (options.isDev) {
			console.warn("[FCM-SW] Skipped background message to prevent duplication", payload)
		}
		return;
	}

	notificationHandler(payload);
});


// messaging.onMessage(function (payload) {
// 	// if (options.isDev) {
// 	console.log('Received message ', payload);
// 	// }

// 	// Use the notification values sent in the payload if they exist
// 	notificationHandler(payload);

// });


const notificationHandler = (payload) => {
	const notificationTitle = payload.notification.title;
	const notificationOptions = {
		body: payload.notification.body,
		icon: payload.notification.icon,
		image: payload.notification.image,
		// fcm doesn't accept actions in foreground message object
	};
	event.waitUntil(
		self.registration.showNotification(notificationTitle, notificationOptions)
	);
}

self.addEventListener('notificationclick', function (event) {
	if (options.isDev) {
		console.log('[Service Worker] Notification click received.', event);
	}

	event.notification.close(); // Close the notification

	const data = event.notification.data.FCM_MSG.data
	// Perform action based on action identifier
	if (event.action === 'open-url') {
		clients.openWindow(data.url);
	} else if (event.action === 'dismiss') {
		// Dismiss action: No further action needed
	} else {
		// Handle default case (e.g., notification body clicked)
		if (data.url) {
			clients.openWindow(data.url);
		}
	}
});

self.addEventListener('push', event => {
	// Trigger some event that should prompt message sending
	clients.matchAll({ includeUncontrolled: true, type: 'window' }).then(clients => {
		clients.forEach(client => {
			console.log("Sending To client:", client)
			client.postMessage({ type: 'UPDATE', message: 'MESSAGE_RECEIVED' });
		});
	});
});
