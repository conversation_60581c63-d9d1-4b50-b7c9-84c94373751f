<template>
	<div>
		<page :title="$t('operations.operations')">
			<template #actions>
				<v-btn
					v-if="can('operations.create')"
					prepend-icon="i-mdi:plus"
					color="primary"
					@click="crud?.create()"
				>
					{{ $t('operations.create-operation') }}
				</v-btn>
			</template>

			<datatable
				ref="table"
				url="/operations"
				:headers="headers"
				search-key="name"
			>
				<template #item.actions="{ item }">
					<v-btn
						v-if="can('operations.edit')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('operations.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</template>
			</datatable>
		</page>
		<operation-crud
			ref="crud"
			@created="table?.refresh()"
			@updated="table?.refresh()"
			@deleted="table?.refresh()"
		/>
	</div>
</template>

<script lang="ts" setup>
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import type OperationCrud from '~/components/OperationCrud.vue'

definePageMeta({
	permission: 'operations.view',
})

const { t } = useI18n()

const table = ref<typeof Datatable | null>(null)
const crud = ref<typeof OperationCrud | null>(null)

const headers: Headers = [
	{
		title: '',
		value: 'index',
	},
	{
		title: t('operations.display-name'),
		value: 'displayName',
	},
	{
		title: t('operations.type'),
		value: 'type',
	},
	{
		title: t('operations.trading-platform'),
		value: 'tradingPlatform.name',
	},
	{
		// title: 'Actions',
		value: 'actions',
	},

]
</script>
