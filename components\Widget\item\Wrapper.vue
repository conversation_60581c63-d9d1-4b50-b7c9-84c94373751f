<template>
	<div
		:id="`widget-wrapper-${i}`"
		class="widget-wrapper"
		:class="{
			'edit-mode': p.editMode,
			'open': menuModel,
			'elevation-10 dragging': inserting,
		}"
	>
		<div class="tools">
			<v-defaults-provider
				:defaults="{
					global: {
						color: 'accent',
					},
				}"
			>
				<div class="toolbar">
					<v-btn
						variant="text"
						icon="i-mdi:trash-can-outline"
						size="small"
						@click="remove"
					/>
					<div class="data" />
					<div class="btn">
						<v-icon
							v-ripple
							class="drag-handle"
							size="small"
							variant="text"
							icon="i-mdi:drag"
						/>
					</div>
				</div>
			</v-defaults-provider>
		</div>

		<slot />
	</div>
</template>

<script lang="ts" setup>
type Emit = {
	(e: 'remove',): void
}
const menuModel = ref(false)

const p = defineProps<{ editMode: boolean, inserting: boolean, i: string }>()

const emit = defineEmits<Emit>()

const { $confirm } = useNuxtApp()

const remove = () => {
	$confirm('Are you sure you want to remove this widget?')
		.then(() => {
			emit('remove')
		})
		.catch(() => {})
}
</script>

<style lang="scss">
	.widget-wrapper{
		height:100%;
		position: relative;
		.tools{
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			bottom: 0;
			z-index: 1;
			// opacity: 0;
			display: flex;
			border-radius: 8px;
			justify-content: flex-end;
			align-items: start;
			border-radius: 8px;;
			// background-color: rgba(0,0,0,0.5);
			visibility: hidden;
			transition: all 0.25s;
			.toolbar{
				display: flex;
				align-items: center;
				// border: 1px solid red;
				margin-inline-end: 8px;
				margin-block-start: 8px;
				gap: 4px;
				.btn{
					width:40px;
					height:40px;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}

			.drag-handle{
					// color: rgb(var(--v-theme-primary));
			}
		}
		&.edit-mode:not(.dragging), &.edit-mode.open:not(.dragging){
			.tools{
				// opacity: 0.5;
				visibility: visible;
				transition: all 0.25s;
				background-color: rgba(var(--v-theme-surface),0.8);
				backdrop-filter: blur(2px);
			}
			&:hover{
				.tools{
					opacity: 1;
					visibility: visible;
					transition: all 0.25s;
					background-color: rgba(var(--v-theme-surface),0.8);
					backdrop-filter: blur(5px);
				}
			}
		}
		&.dragging{
			opacity: 0.5;
		}
		@at-root .vue-draggable-dragging & {
			//.elevation-4
			box-shadow: 0px 2px 4px -1px var(--v-shadow-key-umbra-opacity, rgba(0, 0, 0, 0.2)), 0px 4px 5px 0px var(--v-shadow-key-penumbra-opacity, rgba(0, 0, 0, 0.14)), 0px 1px 10px 0px var(--v-shadow-key-ambient-opacity, rgba(0, 0, 0, 0.12)) !important;
		}
	}
</style>
