export namespace Currencies {
	export namespace _Id {
		export const URL = (_Id: number | string) => `/currencies/${_Id}`

		export interface GETResponse {
			id: number
			name: string
			symbol: string
			createdAt: string
			updatedAt: null
		}

		export interface PUTRequest {
			name: string
			symbol: string
		}

		export interface POSTRequest {
			name: string
			symbol: string
		}

	}

	export namespace Lookup {
		export const URL = '/currencies/lookup'

		export interface SingleRecord {
			id: number
			name: string
			symbol: string
			createdAt: string
			updatedAt: string
		}

		export type GETResponse = SingleRecord[]

	}

}
