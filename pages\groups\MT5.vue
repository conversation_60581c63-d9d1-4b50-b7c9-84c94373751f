<template>
	<page :title="$t('groups.groups')">
		<template #actions>
			<v-btn
				v-if="can('group.create')"
				prepend-icon="i-mdi:plus"
				color="primary"
				@click="crud?.create(undefined, { override: lastOpenedPath?{ group: lastOpenedPath }:undefined })"
			>
				{{ $t('groups.create-group') }}
			</v-btn>
		</template>
		<datatable
			ref="table"
			url="/groups/mt5"
			:headers="headers"
			search-key="name"
			:default-model="{ name: '', path: '' }"
			:lazyload="{ rootMargin: '0px 0px 500px 0px' }"
		>
			<template #toolbar.prepend>
				<v-btn
					:disabled="lastOpenedPathHistory.length < 2"
					icon="i-mdi:arrow-back"
					class="me-1"
					@click="back"
				/>
			</template>
			<template #toolbar.refresh="{ loading, refresh }">
				<v-btn
					v-tooltip:top="'Clear Cache & Refresh'"
					:disabled="isClearingCache || loading"
					icon="i-mdi:refresh"
					@click="clearCacheAndRefresh(refresh)"
				/>
			</template>
			<template #toolbar.middle-start>
				<v-chip
					v-if="lastOpenedPath"
					rounded
					closable
					size="large"
					color="primary"
					@click:close="clearPath"
				>
					{{ lastOpenedPath }}
				</v-chip>
			</template>
			<template #prepend="{ height }">
				<v-sheet
					class="fill-height pe-2 me-2 border-e "
					min-width="300"
				>
					<v-toolbar
						color="surface"
						density="compact"
						class="mb-2"
					>
						<v-text-field
							v-model="searchPathModel"
							clearable
							:placeholder="$t('groups.find-path')"
							append-inner-icon="i-mdi:magnify"
							flat
							variant="solo-filled"
							hide-details
							density="comfortable"
							class="rounded-t-lg"
						/>
					</v-toolbar>
					<v-sheet
						class="overflow-y-scroll"
						:max-height="`calc(${height} + var(--datatable-footer-height) - 6px) `"
					>
						<groups-path-tree
							ref="pathTreeRef"
							v-model="lastOpenedPath"
							v-model:search="debouncedSearchPathModel"
							v-model:opened="openedModel"
							@loaded="pathsData = $event"
						/>
					</v-sheet>
				</v-sheet>
			</template>
			<template #item.icon>
				<v-icon icon="i-ph:currency-circle-dollar" />
			</template>
			<template #item.margin="{ item }">
				{{ item?.marginCall }} / {{ item?.marginStopOut }}%
			</template>
			<template #item.actions="{ item }">
				<div class="d-flex">
					<v-btn
						v-if="can('group.create')"
						size="small"
						variant="text"
						icon="i-mdi:pencil"
						@click="crud?.update(item)"
					/>
					<v-btn
						v-if="can('group.delete')"
						size="small"
						variant="text"
						icon="i-mdi:delete"
						@click="crud?.delete(item)"
					/>
				</div>
			</template>

			<template #body.prepend="{}">
				<tr
					v-for="dir in currentPathChildren"
					:key="dir.value"
					class="cursor-pointer"
					@click="openPathFromTable(dir.value)"
				>
					<td>
						<v-icon
							icon="i-mdi:folder"
							color="yellow-darken-1"
						/>
					</td>
					<td :colspan="headers!.length - 1">
						{{ dir.title }} <span class="text-caption">({{ dir.totalItems }})</span>
					</td>
				</tr>
			</template>
			<template #no-data>
				<div v-if="!currentPathChildren.length">
					{{ $t('$vuetify.noDataText') }}
				</div>
			</template>
			<template #end-of-data>
				<div />
			</template>
		</datatable>
	</page>
	<groups-crud-mt5
		ref="crud"
		@created="table?.refresh()"
		@updated="table?.refresh()"
		@deleted="table?.refresh();pathTreeRef?.refresh()"
	/>
</template>

<script lang="ts" setup>
import type GroupsCrud from '@/components/Groups/Crud/Mt5/index.vue'
import type { Headers } from '@/components/Datatable.vue'
import type Datatable from '@/components/Datatable.vue'
import GroupsPathTree from '@/components/Groups/PathTree/index.vue'
import type { Groups } from '~/types'

const table = ref<InstanceType<typeof Datatable> | null>(null)

const crud = ref<InstanceType<typeof GroupsCrud> | null>(null)

const searchPathModel = ref('')

const debouncedSearchPathModel = useDebounce(searchPathModel, 300)

const lastOpenedPath = ref('')

const { undo: back, history: lastOpenedPathHistory } = useRefHistory(lastOpenedPath)

const openedModel = ref<Groups.MT5.Paths.Lookup.SingleRecord['value'][]>([])

const pathsData = ref<Groups.MT5.Paths.Lookup.GETResponse>([])

const pathTreeRef = ref<InstanceType<typeof GroupsPathTree> | null>(null)

const isClearingCache = ref(false)

watch(() => lastOpenedPath.value, (value) => {
	table.value?.filter({
		path: value ? `${value}` : '',
	})
})

const { t } = useI18n()

const headers: Headers = [
	{
		title: '',
		value: 'icon',
	},
	{
		title: t('groups.groups'),
		value: 'group',
		// nowrap: true,
	},
	// {
	// 	title: t('groups.server'),
	// 	value: 'server',
	// },
	{
		title: t('groups.company'),
		value: 'company',
	},
	{
		title: t('groups.margin'),
		value: 'margin',
	},
	{
		value: 'actions',
	},
]

const openPathFromTable = (path: string) => {
	lastOpenedPath.value = path
	// const openedDirs = pathTreeRef.value?.extractPathArray(path)
	// console.log('🚀 ~ openPathFromTable ~ openedDirs:', openedDirs)
	// if (openedDirs) {
	// 	openedModel.value = openedDirs
	// }
	table.value?.filter({
		path: `${path}`,
	})
}

const clearPath = () => {
	lastOpenedPath.value = ''
	openedModel.value = []
	table.value?.filter({})
}

const currentPathChildren = computed(() => {
	if (!lastOpenedPath.value) {
		return []
	}

	// find the path in all items and its children, once found return the children array

	const findInItems = (items: Groups.MT5.Paths.Lookup.GETResponse, path: string): Groups.MT5.Paths.Lookup.GETResponse => {
		for (const item of items.filter(item => item.type === 'Path')) {
			if (item.value === path) {
				return item.children.filter(item => item.type === 'Path')
			}
			if (item.children.length) {
				const children = findInItems(item.children, path)
				if (children.length) {
					return children
				}
			}
		}
		return []
	}

	const currentChildren = findInItems(pathsData.value, lastOpenedPath.value)

	return currentChildren
})

const clearCacheAndRefresh = (callback: Function) => {
	const { $api } = useNuxtApp()

	isClearingCache.value = true

	$api('/groups/refresh', {
		method: 'POST',
	}).then(() => {
		if (callback) {
			callback()
		}
		pathTreeRef.value!.refresh()
	}).finally(() => {
		isClearingCache.value = false
	})
}
</script>
