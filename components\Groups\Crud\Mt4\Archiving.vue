<template>
	<v-container>
		<v-select
			v-model="item.archivePeriod"
			:error-messages="errors.archivePeriod"
			:label="$t('Groups.MT4.Crud.Archiving.inactivity-period')"
			:items="archivePeriodItems"
			suffix="days"
		/>
		<v-select
			v-model="item.archiveMaxBalance"
			:error-messages="errors.archiveMaxBalance"
			:label="$t('Groups.MT4.Crud.Archiving.maximum-balance')"
			:items="[0, 100, 1000, 10000]"
		/>
		<v-select
			v-model="item.archivePendingPeriod"
			:error-messages="errors.archivePendingPeriod"
			:label="$t('Groups.MT4.Crud.Archiving.archive-deleted-pending')"
			:items="archivePendingPeriodItems"
			suffix="months"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
// import { Groups } from '~/types'

withDefaults(defineProps<Props>(), defaults)

defineEmits<Emit>()

const archivePeriodItems = [
	{
		title: 'disabled',
		value: 0,
	},
	{
		title: '90',
		value: 90,
	},
	{
		title: '180',
		value: 180,
	},
	{
		title: '365',
		value: 365,
	},
]

const archivePendingPeriodItems = [
	{
		title: 'disabled',
		value: 0,
	},
	{
		title: '1',
		value: 1,
	},
	{
		title: '2',
		value: 2,
	},
	{
		title: '3',
		value: 3,
	},
	{
		title: '4',
		value: 4,
	},
	{
		title: '5',
		value: 5,
	},
	{
		title: '6',
		value: 6,
	},
]
</script>
