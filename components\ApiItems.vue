<template>
	<slot v-bind="$bind" />
</template>

<script setup lang="ts">
import dot from 'dot-object'
import type { VInput } from 'vuetify/components'

export type ApiItemsProps = {
	/**
	 * The URL to fetch data from.
	 */
	url: Parameters<typeof useApi>[0]
	/**
	 * Additional options for the API request.
	 */
	apiOptions?: Parameters<typeof useApi>[1]
	/**
	 * The key to access the items in the API response.
	 */
	itemsKey?: string
	/**
	 * Indicates whether server-side rendering should be enabled.
	 */
	ssr?: boolean

	/**
	 * Function that will map the items before binding
	 */
	mapItems?: (items: any[]) => any[]
	/**
	 * append your error messages
	 */
	errorMessages?: VInput['$props']['errorMessages']
}
type Emit = {
	(e: 'loaded', items: any[]): void
}

const emit = defineEmits<Emit>()

const { errorSnackbar } = useSnackbar()
const { t } = useI18n()
const p = withDefaults(defineProps<ApiItemsProps>(),
	{
		itemsKey: '',
		apiOptions: () => ({
			// cache: 'force-cache', //causes issues more than benefits DON'T ENABLE Again
		}),
		ssr: false,
		mapItems: (items: any[]) => items,
		errorMessages: () => [],
	},
)

const apiOptions = computed(() => {
	return {
		...p.apiOptions,
		server: p.ssr,
		lazy: !p.ssr,
	}
})

const { data, error, status, refresh } = useApi<any>(p.url, apiOptions.value)

watchOnce(() => status.value === 'success', () => {
	if (data.value) {
		emit('loaded', data.value)
	}
})

const showError = () => {
	errorSnackbar({
		title: t('api-items.request-error'),
		text: error.value?.message || '',
		dismissible: false,
		action: () => h(
			'v-btn',
			{
				text: true,
				onClick: () => {
					refresh()
				},
			},
			t('crud.retry'),
		),
	})
}

watch(() => error.value, (value) => {
	if (value) {
		showError()
	}
}, {
	immediate: true,
})

type Bind = {
	'items': any[]
	'loading': boolean
	'error': boolean
	'errorMessages': typeof p.errorMessages
	'appendIcon'?: string
	'onClick:append'?: () => void
}

const $bind = computed<Bind>(
	() => {
		const items = computed<any[]>(() => {
			if (p.itemsKey && typeof data.value === 'object') {
				// $bind.items = data.value[p.itemsKey as keyof typeof data.value] as any[]
				const extractedItems = dot.pick(p.itemsKey, data.value)
				if (Array.isArray(extractedItems)) {
					return extractedItems
				}
			}

			if (Array.isArray(data?.value)) {
				return data.value
			}

			return []
		})

		const clonedErrorMessages = useCloned(p.errorMessages).cloned.value

		const errorMessages = Array.isArray(clonedErrorMessages) ? clonedErrorMessages : [clonedErrorMessages]
		let appendIcon = ''
		if (error.value) {
			errorMessages.push(t('api-items.request-error'))
			appendIcon = 'i-mdi:reload'
		}

		return {
			'items': p.mapItems(items.value),
			'loading': status.value === 'pending',
			'error': !!errorMessages.length,
			errorMessages,
			'data': data?.value,
			appendIcon,
			'onClick:append': async () => {
				await refresh()
				// showError()
			},
		}
	})

defineExpose({
	$bind,
	refresh,
})
</script>
