<template>
	<v-container>
		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.limitLogs"
					:label="$t('Manager.MT5.Crud.Permissions.available-logs')"
					:items="limitItems"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.limitReports"
					:label="$t('Manager.MT5.Crud.Permissions.available-reports')"
					:items="limitItems"
				/>
			</v-col>
		</v-row>
		<v-row class="mt-n5">
			<v-col
				cols="12"
				md="6"
			>
				<api-items
					v-slot="props"
					ref="roleLookupRef"
					:url="Manager.MT5.Roles.Lookup.URL"
				>
					<v-text-field
						v-model="selectedRoleModel"
						item-title="name"
						item-value="permissions"
						:class="{ 'v-select--active-menu': !!menuModel }"
						:label="$t('Manager.MT5.Crud.Permissions.roles')"
						:append-inner-icon="`v-select__menu-icon ${$vuetify.icons.aliases?.dropdown}`"
						v-bind="omit(props, ['items'])"
					>
						<v-menu
							v-model="menuModel"
							activator="parent"
						>
							<v-list :items="props.items">
								<v-list-item
									v-for="(item, i) in props.items"
									:key="i"
									:title="item.name"
									:active="item.name === selectedRoleModel"
									@click="selectRole(item)"
								/>
							</v-list>
						</v-menu>
					</v-text-field>
				</api-items>
			</v-col>
			<v-col>
				<v-btn
					variant="text"
					height="48"
					prepend-icon="i-mdi:content-save"
					:disabled="!selectedRoleModel"
					:loading="isSaving"
					@click="saveRightsAsRole"
				>
					{{ $t('Manager.MT5.Crud.Permissions.save') }}
				</v-btn>
				<v-btn
					variant="text"
					height="48"
					prepend-icon="i-mdi:delete"
					:disabled="!canDeleteRole"
					:loading="isDeleting"
					@click="deleteRole"
				>
					{{ $t('Manager.MT5.Crud.Permissions.delete') }}
				</v-btn>
			</v-col>
		</v-row>
		<div class="d-flex justify-space-between">
			<v-label>
				{{ $t('Manager.MT5.Crud.Permissions.permissions') }}
			</v-label>
			<v-btn
				variant="text"
				@click="openAll = !openAll"
			>
				{{ openAll? $t('Manager.MT5.Crud.Permissions.close-all'):$t('Manager.MT5.Crud.Permissions.open-all') }}
			</v-btn>
		</div>
		<v-treeview
			v-model:selected="item.rights"
			:items="permissionsTree"
			max-height="calc(100vh - 370px)"
			class="overflow-y-auto"
			:open-all="openAll"
			selectable
			select-strategy="classic"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import { VTreeview } from 'vuetify/labs/VTreeview'
import omit from 'lodash/omit'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Manager } from '~/types'
import type ApiItems from '~/components/ApiItems.vue'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const { t } = useI18n()

const openAll = ref(false)

const limitItems = enumToItems(Manager.MT5.EnManagerLimit, 'Manager.MT5.EnManagerLimit')

type PermissionsTree = {
	title: string
	props?: Record<string, any>
} & (
	| {
		value: Manager.MT5.EnManagerRights
		children?: never // Ensures no 'children' if 'value' is present
	}
	| {
		children: PermissionsTree[] // Ensures presence of 'children'
		value: string // Ensures no 'value' if 'children' is present
	}
)

const addPropsToChildren = (node: PermissionsTree) => {
	if (node.children) {
		node.children = node.children.map(addPropsToChildren)
	}
	node.props = { class: node.children ? 'ms-8' : 'ms-7' }
	return node
}

const permissionsTree: PermissionsTree[] = [
	{
		title: t('Manager.MT5.Crud.Permissions.connection-types'),
		value: 'connection-types',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.connect-using-metatrader-5-administrator'),
				value: Manager.MT5.EnManagerRights.RIGHT_ADMIN,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.connect-using-metatrader-5-manager'),
				value: Manager.MT5.EnManagerRights.RIGHT_MANAGER,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.configurations-setup'),
		value: 'config-setup',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.configure-network'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_SERVERS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-integrations'),
				value: 'config-integrations',
				children: [
					{
						title: t('Manager.MT5.Crud.Permissions.configure-sponsored-vps'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_VPS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.configure-mail-server'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_MAILS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.configure-sms-gateways-messenger-channels'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_MESSENGERS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.configure-kyc'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_KYC,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.configure-payments'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_PAYMENTS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.configure-web-services'),
						value: Manager.MT5.EnManagerRights.RIGHT_CFG_WEB_SERVICES,
					},
				],
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-ip-access-list'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_ACCESS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-automation'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_AUTOMATIONS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-operation-time'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_TIME,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-holidays'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_HOLIDAYS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-leverages'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_LEVERAGE,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-groups'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_GROUPS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-allocations'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_ALLOCATIONS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-corporate-links'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_CORPORATES,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-managers-permissions'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_MANAGERS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-request-routing'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_REQUESTS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-gateways'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_GATEWAYS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-plugins'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_PLUGINS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-data-feeds'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_DATAFEEDS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-reports'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_REPORTS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-symbols'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_SYMBOLS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-history-charts-synchronization'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_HST_SYNC,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-ecn'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_ECN,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.configure-funds-etf'),
				value: Manager.MT5.EnManagerRights.RIGHT_CFG_FUNDS,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.administration'),
		value: 'admin',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.access-server-logs'),
				value: Manager.MT5.EnManagerRights.RIGHT_SRV_JOURNALS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.receive-automatic-server-reports'),
				value: Manager.MT5.EnManagerRights.RIGHT_SRV_REPORTS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-charts'),
				value: Manager.MT5.EnManagerRights.RIGHT_CHARTS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.send-emails'),
				value: Manager.MT5.EnManagerRights.RIGHT_EMAIL,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.send-news'),
				value: Manager.MT5.EnManagerRights.RIGHT_NEWS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.export-data'),
				value: Manager.MT5.EnManagerRights.RIGHT_EXPORT,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.manage-servers-machines'),
				value: Manager.MT5.EnManagerRights.RIGHT_ADMIN_COMPUTER,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.accounts'),
		value: 'accounts',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.accountant-balance-operations'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACCOUNTANT,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.access-accounts'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_READ,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.view-technical-accounts'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_TECHNICAL,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.manage-technical-accounts'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_TECHNICAL_MODIFY,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.access-the-account-personal-details'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_DETAILS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-accounts'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_MANAGER,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.delete-accounts'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_DELETE,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.view-currently-connected-clients'),
				value: Manager.MT5.EnManagerRights.RIGHT_ACC_ONLINE,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.confirm-dangerous-actions'),
				value: Manager.MT5.EnManagerRights.RIGHT_CONFIRM_ACTIONS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.push-notifications'),
				value: Manager.MT5.EnManagerRights.RIGHT_NOTIFICATIONS,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.dealing'),
		value: 'dealing',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.access-orders-positions'),
				value: Manager.MT5.EnManagerRights.RIGHT_TRADES_READ,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-orders-positions-deals'),
				value: Manager.MT5.EnManagerRights.RIGHT_TRADES_MANAGER,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.delete-orders-positions-deals'),
				value: Manager.MT5.EnManagerRights.RIGHT_TRADES_DELETE,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.dealer'),
				value: Manager.MT5.EnManagerRights.RIGHT_TRADES_DEALER,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.supervisor'),
				value: Manager.MT5.EnManagerRights.RIGHT_TRADES_SUPERVISOR,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.show-raw-quotes-without-spread-difference'),
				value: Manager.MT5.EnManagerRights.RIGHT_QUOTES_RAW,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.throw-in-quotes'),
				value: Manager.MT5.EnManagerRights.RIGHT_QUOTES,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.modify-spread-execution-mode'),
				value: Manager.MT5.EnManagerRights.RIGHT_SYMBOL_DETAILS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.risk-manager'),
				value: Manager.MT5.EnManagerRights.RIGHT_RISK_MANAGER,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-groups-margin-settings'),
				value: Manager.MT5.EnManagerRights.RIGHT_GRP_DETAILS_MARGIN,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-groups-commission-settings'),
				value: Manager.MT5.EnManagerRights.RIGHT_GRP_DETAILS_COMMISSION,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.receive-reports'),
				value: Manager.MT5.EnManagerRights.RIGHT_REPORTS,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.payments'),
		value: 'payments',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.access-payments'),
				value: Manager.MT5.EnManagerRights.RIGHT_PAYMENTS_ACCESS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.process-payments'),
				value: Manager.MT5.EnManagerRights.RIGHT_PAYMENTS_PROCESS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-payments'),
				value: Manager.MT5.EnManagerRights.RIGHT_PAYMENTS_EDIT,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.delete-payments'),
				value: Manager.MT5.EnManagerRights.RIGHT_PAYMENTS_DELETE,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.back-office'),
		value: 'back-office',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.clients'),
				value: 'clients',
				children: [
					{
						title: t('Manager.MT5.Crud.Permissions.access-clients'),
						value: Manager.MT5.EnManagerRights.RIGHT_CLIENTS_ACCESS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.create-clients'),
						value: Manager.MT5.EnManagerRights.RIGHT_CLIENTS_CREATE,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.edit-clients'),
						value: Manager.MT5.EnManagerRights.RIGHT_CLIENTS_EDIT,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.delete-clients'),
						value: Manager.MT5.EnManagerRights.RIGHT_CLIENTS_DELETE,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.kyc-check'),
						value: Manager.MT5.EnManagerRights.RIGHT_CLIENTS_KYC,
					},
				],
			},
			{
				title: t('Manager.MT5.Crud.Permissions.documents'),
				value: 'documents',
				children: [
					{
						title: t('Manager.MT5.Crud.Permissions.access-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_ACCESS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.create-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_CREATE,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.edit-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_EDIT,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.delete-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_DELETE,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.add-files-to-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_FILES_ADD,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.delete-files-from-documents'),
						value: Manager.MT5.EnManagerRights.RIGHT_DOCUMENTS_FILES_DELETE,
					},
				],
			},
			{
				title: t('Manager.MT5.Crud.Permissions.comments'),
				value: 'comments',
				children: [
					{
						title: t('Manager.MT5.Crud.Permissions.access-comments'),
						value: Manager.MT5.EnManagerRights.RIGHT_COMMENTS_ACCESS,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.add-comments'),
						value: Manager.MT5.EnManagerRights.RIGHT_COMMENTS_CREATE,
					},
					{
						title: t('Manager.MT5.Crud.Permissions.delete-comments'),
						value: Manager.MT5.EnManagerRights.RIGHT_COMMENTS_DELETE,
					},
				],
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.finteza'),
		value: 'finteza',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.access-finteza'),
				value: Manager.MT5.EnManagerRights.RIGHT_FINTEZA_ACCESS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.view-website'),
				value: Manager.MT5.EnManagerRights.RIGHT_FINTEZA_WEBSITES,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.view-campaigns'),
				value: Manager.MT5.EnManagerRights.RIGHT_FINTEZA_CAMPAIGNS,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.view-reports'),
				value: Manager.MT5.EnManagerRights.RIGHT_FINTEZA_REPORTS,
			},
		],
	},
	{
		title: t('Manager.MT5.Crud.Permissions.subscriptions'),
		value: 'subscriptions',
		children: [
			{
				title: t('Manager.MT5.Crud.Permissions.view-subscriptions'),
				value: Manager.MT5.EnManagerRights.RIGHT_SUBSCRIPTIONS_VIEW,
			},
			{
				title: t('Manager.MT5.Crud.Permissions.edit-subscriptions'),
				value: Manager.MT5.EnManagerRights.RIGHT_SUBSCRIPTIONS_EDIT,
			},
		],
	},
].map(addPropsToChildren)

const selectedRoleModel = ref<Manager.MT5.Roles.Role['name']>()

const roleLookupRef = ref<InstanceType<typeof ApiItems>>()

const { $api } = useNuxtApp()

const isSaving = ref(false)

const isDeleting = ref(false)

const menuModel = ref(false)

const selectRole = (role: Manager.MT5.Roles.Role) => {
	selectedRoleModel.value = role.name
	setRights(role.permissions)
}

const setRights = (rights: Manager.MT5.Roles.Role['permissions']) => {
	emit('update:item', {
		rights: useCloned(rights).cloned.value,
	})
}

const saveRightsAsRole = () => {
	isSaving.value = true
	$api(Manager.MT5.Roles._Name.URL, {
		method: 'POST',
		body: {
			name: selectedRoleModel.value,
			permissions: p.item.rights,
		},
	})
		.then(() => {
			roleLookupRef.value?.refresh()
		})
		.finally(() => {
			isSaving.value = false
		})
}

const canDeleteRole = computed(() => {
	return !!selectedRoleModel.value && roleLookupRef.value?.$bind.items.some(item => item.name === selectedRoleModel.value)
})

const deleteRole = () => {
	isDeleting.value = true
	$api(Manager.MT5.Roles._Name.DeleteURL(selectedRoleModel.value as string), {
		method: 'DELETE',
	})
		.then(() => {
			roleLookupRef.value?.refresh()
			selectedRoleModel.value = undefined
		})
		.finally(() => {
			isDeleting.value = false
		})
}
</script>
