<template>
	<v-container>
		<v-select
			v-model="item.reportsMode"
			:items="reportsModeItems"
		/>
		<v-defaults-provider
			:defaults="{
				global: {
					disabled: !item.reportsMode,
				},
			}"
		>
			<v-checkbox
				v-model="reportsFlagsModel"
				color="primary"
				class="compact"
				hide-details
				:disabled="!item.reportsMode"
				:true-value="Groups.MT5.EnReportsFlags.REPORTSFLAGS_STATEMENTS"
				:label="$t('Groups.MT5.Crud.Reports.generate-statement-for-clients')"
			/>
			<v-checkbox
				v-model="reportsFlagsModel"
				color="primary"
				class="compact"
				:true-value="Groups.MT5.EnReportsFlags.REPORTSFLAGS_EMAIL"
				:disabled="!item.reportsFlags"
				:label="$t('Groups.MT5.Crud.Reports.send-statement-by-email')"
			/>
			<api-items
				v-slot="props"
				:url="MailServers.Lookup.URL"
				:map-items="mapMailServers"
			>
				<v-select
					v-model="item.reportsEmail"
					v-bind="props"
					:label="$t('Groups.MT5.Crud.Reports.mail-server')"
					:disabled="!reportsFlagsModel.includes(Groups.MT5.EnReportsFlags.REPORTSFLAGS_EMAIL)"
				/>
			</api-items>
			<v-checkbox
				v-model="reportsFlagsModel"
				color="primary"
				class="compact"
				hide-details
				:true-value="Groups.MT5.EnReportsFlags.REPORTSFLAGS_SUPPORT"
				:label="$t('Groups.MT5.Crud.Reports.send-copies-to-support-email')"
			/>
		</v-defaults-provider>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Groups, MailServers } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const reportsModeItems = enumToItems(Groups.MT5.EnReportsMode, 'Groups.MT5.EnReportsMode')

const computedReportsFlags = computed({
	get: () => p.item.reportsFlags,
	set: (value: number) => emit('update:item', { reportsFlags: value }),
})

const { model: reportsFlagsModel } = useMultiSelectEnum(computedReportsFlags, Groups.MT5.EnReportsFlags)

const { t } = useI18n()

const mapMailServers = (items: MailServers.Lookup.GETResponse) => {
	const mappedItems = items.map(item => ({ title: item.name, value: item.name }))
	mappedItems.unshift({ title: t('Groups.MT5.Crud.Reports.default'), value: '' })
	return mappedItems
}
</script>
