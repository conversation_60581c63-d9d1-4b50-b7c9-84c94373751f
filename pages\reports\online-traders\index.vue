<template>
	<page :title="$t('online-traders.online-traders')">
		<template #actions>
			<div class="d-flex">
				<v-spacer />
				<v-badge
					dot
					:color="connectionColor"
					inline
				/>
				<div class="text-subtitle-2">
					<div v-if="onlineSessionsStore.connectionStatus === ConnectionStatus.Connected">
						{{ $t('online-traders.online-traders-users-length', [onlineSessionsStore.data?.data.length || 0]) }}
					</div>
					<div v-else-if="onlineSessionsStore.connectionStatus === ConnectionStatus.Disconnected">
						{{ $t('online-traders.offline') }}
					</div>
					<div v-else>
						{{ $t('online-traders.reconnecting-connection-attempts', [onlineSessionsStore.connectionAttempts]) }}
					</div>
				</div>
			</div>
		</template>
		<datatable
			ref="table"
			:headers="headers"
			:items="items"
			:loading="onlineSessionsStore.connectionStatus === ConnectionStatus.Connecting"
			item-value="index"
			:items-per-page="-1"
			hide-default-footer
			fixed-header
			:group-by="computedGroupBy"
			:row-props="getRowProps"
			@refresh="refreshHandler"
		>
			<template #toolbar.middle-start>
				<div class="fill-width">
					<v-row no-gutters>
						<v-col
							cols="auto"
							class="px-4 flex-shrink-0"
						>
							<v-checkbox
								v-model="statusModel.MultiSessionsCountryMismatch"
								class="compact"
								base-color="error"
								color="error"
								density="compact"
								hide-details
								:label="$t('online-traders.multi-sessions-country-mismatch')"
							/>
							<v-checkbox
								v-model="statusModel.MultiSessions"
								class="compact"
								base-color="info"
								color="info"
								density="compact"
								hide-details
								:label="$t('online-traders.multi-sessions')"
							/>
						</v-col>
						<v-col
							cols="auto"
							class="pe-3 flex-shrink-0"
						>
							<v-checkbox
								v-model="statusModel.CountryMismatch"
								class="compact"
								base-color="warning"
								color="warning"
								density="compact"
								hide-details
								:label="$t('online-traders.country-mismatch')"
							/>
							<v-checkbox
								v-model="statusModel.SingleSession"
								class="compact"
								base-color="success"
								color="success"
								density="compact"
								hide-details
								:label="$t('online-traders.single-session')"
							/>
						</v-col>
						<v-col cols="auto">
							<v-select
								v-model="groupBy"
								label="Group By"
								hide-details
								:items="groupItems"
								:return-object="false"
								min-width="250"
								clearable
								density="comfortable"
								variant="solo-filled"
								rounded="md"
								flat
								@click:clear="groupBy = undefined"
							/>
						</v-col>
					</v-row>
				</div>
			</template>
			<!-- <template #item.index="{index}">
				{{ index + 1 }}
			</template> -->

			<template #group-header="{ item, isExpanded, toggleGroup }">
				<tr v-bind="getRowProps({ item: item.items?.[0]?.columns || {} })">
					<td :colspan="headers?.length">
						<v-list-item>
							<template #prepend="{}">
								<v-btn
									rounded="circle"
									class="me-2"
									variant="text"
									:icon="`i-mdi-chevron-${isExpanded(item.value)? 'up':'down'}`"
									@click="toggleGroup(item)"
								/>
							</template>
							<v-list-item-title v-if="groupBy === 'login'">
								{{ item.items?.[0].columns?.userName }}<small> (#{{ item.value }})</small>
							</v-list-item-title>
							<v-list-item-title v-else>
								{{ item.value }}
							</v-list-item-title>
						</v-list-item>
					</td>
				</tr>
			</template>

			<template #item.userName="{ item }">
				<v-list-item
					max-width="250"
					class="ps-0"
				>
					<v-list-item-title>{{ item.userName }}</v-list-item-title>
					<v-list-item-subtitle>{{ item.group }}</v-list-item-subtitle>
				</v-list-item>
			</template>
			<template #item.ipCountry="{ item }">
				<v-list-item
					max-width="300"
					class="ps-0"
				>
					<v-list-item-title>{{ item.ipCountry }}</v-list-item-title>
					<v-list-item-subtitle>{{ item.ipAddress }}</v-list-item-subtitle>
				</v-list-item>
			</template>

			<!-- <template #item.login="{ value,item }">
				<v-list-item max-width="200" class="ps-0">
					<v-list-item-title>{{ value }}</v-list-item-title>
				</v-list-item>
			</template> -->

			<template #item.time="{ value }">
				{{ dayjs.unix(value).format(DateFormat.App) }}
			</template>

			<template #item.actions="{ item }">
				<v-btn
					v-tooltip:top="'Copy'"
					color="primary"
					size="small"
					variant="text"
					icon="i-mdi:content-copy"
					@click="copyToClipboard(item)"
				/>
				<v-btn
					v-if="can('onlineTraders.block')"
					v-tooltip:top="'Block'"
					color="error"
					size="small"
					variant="text"
					icon="i-mdi:person-block"
					@click="openBlockDialog(item)"
				/>
			</template>
			<template #loading>
				<v-skeleton-loader :type="`table-row@25`" />
			</template>
		</datatable>
		<v-dialog
			v-model="blockDialogModel"
			max-width="500"
			@update:model-value="blockDialogHandler"
		>
			<v-card>
				<v-form
					ref="blockFormRef"
					v-slot="{ isValid }"
					@submit.prevent="blockTrader"
				>
					<v-card-title>
						{{ $t('online-traders.block-session') }}
					</v-card-title>
					<v-card-text>
						<div class="mb-3 ">
							<div class="text-subtitle-1">
								{{ $t('online-traders.you-are-about-to-block-this-session') }}
							</div>
							<div class="ps-4">
								<b> {{ $t('online-traders.user') }} </b> {{ currentBlockUser?.login }}<br>

								<b>{{ $t('online-traders.ip-address') }}</b> {{ currentBlockUser?.ipAddress }}<br>

								<b>{{ $t('online-traders.country') }}  </b> {{ currentBlockUser?.ipCountry }}<br>
							</div>
						</div>
						<v-textarea
							v-model="blockForm.comment"
							rows="3"
							:rules="rules({ required: true })"
							:label="$t('online-traders.comment')"
							:placeholder="$t('online-traders.reason-for-blocking')"
						/>
					</v-card-text>
					<v-card-actions>
						<v-btn
							variant="text"
							@click="blockDialogModel = false"
						>
							{{ $t('online-traders.cancel') }}
						</v-btn>
						<v-spacer />
						<v-btn
							:loading="isBlocking"
							:disabled="isValid.value !== true"
							color="primary"
							variant="text"
							type="submit"
						>
							{{ $t('online-traders.block') }}
						</v-btn>
					</v-card-actions>
				</v-form>
			</v-card>
		</v-dialog>
	</page>
</template>

<script lang="ts" setup>
import type { VForm } from 'vuetify/components'
import type { Headers } from '~/components/Datatable.vue'
import type Datatable from '~/components/Datatable.vue'
import { DateFormat } from '~/types/DateFormat'
import { OnlineTraders } from '~/types'

definePageMeta({
	permission: 'onlineTraders.view',
})

const { t } = useI18n()

enum StatusColor {
	MultiSessionsCountryMismatch = 'error',
	CountryMismatch = 'warning',
	MultiSessions = 'info',
	SingleSession = 'success',
}

const { $api } = useNuxtApp()

type BlockForm = {
	comment: string | null
	ipFrom: string | null
	ipTo: string | null
}
const blockForm = reactive<BlockForm>({
	comment: null,
	ipFrom: null,
	ipTo: null,
})

const currentBlockUser = ref<OnlineTraders.User>()

const blockDialogModel = ref(false)

const isBlocking = ref(false)

const onlineSessionsStore = useOnlineSessionsStore()

const myPref = usePreferencesStore()

onlineSessionsStore.connect().to(myPref.getKey('tradingServerName') as string)

const { successSnackbar, errorSnackbar, infoSnackbar } = useSnackbar()

const statusModel = ref({
	MultiSessionsCountryMismatch: true,
	CountryMismatch: true,
	MultiSessions: true,
	SingleSession: true,
})

const groupBy = ref<any | undefined>()

const computedGroupBy = computed<InstanceType<typeof Datatable>['$props']['groupBy']>(() => {
	if (groupBy.value === undefined) {
		return undefined
	}

	return [
		{
			key: groupBy.value,
			order: 'asc',
		},
	]
})

const groupItems = ref([
	{
		title: 'Login',
		value: 'login',
	},
	{
		title: 'MT Account Country',
		value: 'mtAccountCountry',
	},
])

const dayjs = useDayjs()

const connectionColor = computed(() => {
	if (onlineSessionsStore.connectionStatus === ConnectionStatus.Connected) {
		return 'success'
	}

	if (onlineSessionsStore.connectionStatus === ConnectionStatus.Connecting) {
		return 'warning'
	}

	return 'error'
})

const getRowProps = ({ item }: { item: OnlineTraders.User }) => {
	// get StatusColor from enum by index
	const color = Object.values(StatusColor).filter((_, index) => index === item.status)
	// const color = StatusColor[item.status]

	return {
		style: {
			background: `rgba(var(--v-theme-${color}), var(--v-activated-opacity))`,
			color: `rgb(var(--v-theme-${color}))`,
		},
	}
}

const copyToClipboard = (item: OnlineTraders.User) => {
	// concat object values with a stick
	const text = Object.values(item).join(' | ')

	navigator.clipboard.writeText(text)

	// Show a snackbar
	infoSnackbar({
		text: t('online-traders.copied-to-clipboard', [item.login]),
		duration: 2000,
	})
}

const items = computed(() =>
	onlineSessionsStore.data.data.filter((session) => {
		if (statusModel.value.MultiSessionsCountryMismatch && session.status === OnlineTraders.Status.MultiSessionsCountryMismatch) {
			return true
		}

		if (statusModel.value.CountryMismatch && session.status === OnlineTraders.Status.CountryMismatch) {
			return true
		}

		if (statusModel.value.MultiSessions && session.status === OnlineTraders.Status.MultiSessions) {
			return true
		}

		if (statusModel.value.SingleSession && session.status === OnlineTraders.Status.SingleSession) {
			return true
		}

		return false
	})
		.map((item, index) => {
			item.index = index
			return item
		}) || [])

const table = ref<typeof Datatable | null>(null)

const blockFormRef = ref<InstanceType<typeof VForm> | null>(null)
const currentMtServer = useCurrentMtServer()

const headers = computed<Headers>(() => {
	const h: Headers = [
		// { title: '', key: 'data-table-expand' },
		{
			title: '',
			value: 'index',
		},
		{
			title: t('online-traders.login'),
			value: 'login',
			sortable: true,
			nowrap: true,
		},
		{
			title: t('online-traders.username'),
			value: 'userName',
			sortable: true,
			nowrap: true,
		},

		{
			title: t('online-traders.iP-Country'),
			value: 'ipCountry',
			nowrap: true,
			sortable: true,
		},
		{
			title: t('online-traders.mt-acc-country'),
			value: 'mtAccountCountry',
			align: 'center',
			nowrap: true,
			sortable: true,
		},
		{
			title: t('online-traders.group'),
			value: 'group',
			width: '0px',
			// to allow for search
			cellProps: { class: 'd-none' },
			headerProps: { class: 'd-none' },
		},
		{
			title: 'IP Address',
			value: 'ipAddress',
			// to allow for search
			cellProps: { class: 'd-none' },
			headerProps: { class: 'd-none' },
		},
		{
			title: t('online-traders.sessions'),
			value: 'totalSessions',
			align: 'center',
			sortable: true,
		},
		{
			value: 'status',
			align: 'center',
			sortable: false,
			width: '0px',
			headerProps: { class: 'd-none' },
			cellProps: { class: 'd-none' },
		},
		{
			// title: 'Actions',
			value: 'actions',
			nowrap: true,
		},

	]

	if (currentMtServer.value?.tradingPlatform?.name === 'MT5') {
		(h as Array<any>).splice(h.length - 1, 0, {
			title: t('online-traders.time'),
			value: 'time',
			nowrap: true,
			sortable: true,
		})
	}

	if (groupBy.value) {
		// remove index column in case there is a group
		const index = h.findIndex(header => header.value === 'index')
		if (index > -1) {
			(h as Array<any>).splice(index, 1)
		}
	}

	return h
})

const openBlockDialog = (item: OnlineTraders.User) => {
	currentBlockUser.value = item
	blockForm.ipFrom = item.ipAddress
	blockForm.ipTo = item.ipAddress
	blockDialogModel.value = true
}

const blockDialogHandler = (value: boolean) => {
	if (!value) {
		blockForm.comment = ''
		blockFormRef.value?.resetValidation()
	}
}

const blockTrader = () => {
	if (!can('onlineTraders.block')) {
		return
	}
	isBlocking.value = true
	$api('/firewall/block-ip-address', {
		body: blockForm,
		method: 'POST',
	})
		.then(() => {
			successSnackbar({
				title: t('online-traders.blocked'),
				text: t('online-traders.the-session-has-been-blocked'),
			})
			blockDialogModel.value = false
		})
		.catch((e) => {
			errorSnackbar(e.message)
		})
		.finally(() => {
			blockDialogModel.value = false
			isBlocking.value = false
		})
}
const refreshHandler = () => {
	onlineSessionsStore.refresh()
}
</script>
