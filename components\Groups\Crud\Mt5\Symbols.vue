<template>
	<v-container>
		<v-toolbar
			density="compact"
			color="surface"
		>
			<v-spacer />
			<v-btn
				variant="text"
				prepend-icon="i-mdi:plus"
				@click="setForEdit(-1)"
			>
				{{ $t('Groups.MT5.Crud.Symbols.add-symbol') }}
			</v-btn>
		</v-toolbar>
		<v-table
			class="draggable-rows"
			hover
		>
			<thead>
				<tr>
					<th>{{ $t('Groups.MT5.Crud.Symbols.symbol') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Symbols.spread') }}</th>
					<th>{{ $t('Groups.MT5.Crud.Symbols.trade') }}</th>
					<th />
				</tr>
			</thead>
			<tbody
				ref="tbodyRef"
				v-auto-animate
			>
				<tr
					v-for="(item, index) in item.symbols"
					:key="item.path"
					@dblclick="setForEdit(index)"
				>
					<td>{{ item.path }}</td>
					<td>{{ item.spreadDiffDefault? $t('Groups.MT5.Crud.Symbols.default'): item.spreadDiff }}</td>
					<td>{{ item.tradeModeDefault ? $t('Groups.MT5.Crud.Symbols.default'): getTradeMode(item.tradeMode) }}</td>
					<td>
						<v-btn
							variant="text"
							size="small"
							icon="i-mdi:trash-can"
							@click="remove(index)"
						/>
						<v-btn
							variant="text"
							size="small"
							icon="i-mdi:pencil"
							@click="setForEdit(index)"
						/>
						<v-btn
							variant="text"
							class="handle"
							size="small"
							icon="i-mdi:drag"
						/>
					</td>
				</tr>
			</tbody>
		</v-table>
		<v-dialog
			v-model="editDialogModel"
			persistent
			max-width="700"
			@keydown.esc.stop
		>
			<v-confirm-edit
				v-slot="{ actions, model }"
				v-model="currentSymbol"
				cancel-text="Reset"
				@save="save"
			>
				<v-form ref="editFormRef">
					<v-card :title="dialogTitle">
						<template #text>
							<groups-crud-mt5-symbol-form
								v-model="model.value"
								:errors="errors"
							/>
						</template>

						<template #actions>
							<v-btn @click="editDialogModel = false">
								{{ $t('Groups.MT5.Crud.Symbols.cancel') }}
							</v-btn>
							<v-spacer />
							<component :is="actions" />
						</template>
					</v-card>
				</v-form>
			</v-confirm-edit>
		</v-dialog>
	</v-container>
</template>

<script lang="ts" setup>
import { useSortable } from '@vueuse/integrations/useSortable'
import type { UseSortableOptions } from '@vueuse/integrations/useSortable'
import type { VForm } from 'vuetify/components'
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { defaultSymbol } from './SymbolForm/Shared'

import { Symbols, type Groups } from '~/types'

const confirm = useNuxtApp().$confirm

const p = withDefaults(defineProps<Props>(), defaults)

const currentSymbolIndex = ref<number>(-1)

const emit = defineEmits<Emit>()

const currentSymbol = ref<Groups.MT5.Symbol>()

const editDialogModel = ref(false)

const editFormRef = ref<InstanceType<typeof VForm>>()

// const defaultSymbol: Groups.MT5.Symbol = {
// 	path: '*',
// 	spreadDiff: 2147483647,
// 	spreadDiffDefault: true,
// 	spreadDiffBalance: 2147483647,
// 	spreadDiffBalanceDefault: true,
// 	stopsLevel: 2147483647,
// 	stopsLevelDefault: true,
// 	freezeLevel: 2147483647,
// 	freezeLevelDefault: true,
// 	swap3Day: 2147483647,
// 	swap3DayDefault: true,
// 	swapYearDaysDefault: true,
// 	volumeMin: 18446744073709552000,
// 	volumeMinDefault: true,
// 	volumeMax: 18446744073709552000,
// 	volumeMaxDefault: true,
// 	volumeStep: 18446744073709552000,
// 	volumeStepDefault: true,
// 	volumeLimit: 18446744073709552000,
// 	volumeLimitDefault: true,
// 	ieVolumeMax: 18446744073709552000,
// 	ieVolumeMaxDefault: true,
// 	volumeMinExt: 18446744073709552000,
// 	volumeMinExtDefault: true,
// 	volumeMaxExt: 18446744073709552000,
// 	volumeMaxExtDefault: true,
// 	volumeStepExt: 18446744073709552000,
// 	volumeStepExtDefault: true,
// 	volumeLimitExt: 18446744073709552000,
// 	volumeLimitExtDefault: true,
// 	ieVolumeMaxExt: 18446744073709552000,
// 	ieVolumeMaxExtDefault: true,
// 	marginInitial: 1.7976931348623157e+308,
// 	marginInitialDefault: true,
// 	marginMaintenance: 1.7976931348623157e+308,
// 	marginMaintenanceDefault: true,
// 	marginLong: 0,
// 	marginLongDefault: false,
// 	marginShort: 0,
// 	marginShortDefault: false,
// 	marginLimit: 0,
// 	marginLimitDefault: false,
// 	marginStop: 0,
// 	marginStopDefault: false,
// 	marginStopLimit: 0,
// 	marginStopLimitDefault: false,
// 	swapLong: 1.7976931348623157e+308,
// 	swapLongDefault: true,
// 	swapShort: 1.7976931348623157e+308,
// 	swapShortDefault: true,
// 	marginRateInitial: 1.7976931348623157e+308,
// 	marginRateInitialDefault: true,
// 	marginRateMaintenance: 1.7976931348623157e+308,
// 	marginRateMaintenanceDefault: true,
// 	marginRateLiquidity: 1.7976931348623157e+308,
// 	marginRateLiquidityDefault: true,
// 	marginHedged: 1.7976931348623157e+308,
// 	marginHedgedDefault: true,
// 	marginRateCurrency: 1.7976931348623157e+308,
// 	marginRateCurrencyDefault: true,
// 	swapRateSunday: 1.7976931348623157e+308,
// 	swapRateSundayDefault: true,
// 	swapRateMonday: 1.7976931348623157e+308,
// 	swapRateMondayDefault: true,
// 	swapRateTuesday: 1.7976931348623157e+308,
// 	swapRateTuesdayDefault: true,
// 	swapRateWednesday: 1.7976931348623157e+308,
// 	swapRateWednesdayDefault: true,
// 	swapRateThursday: 1.7976931348623157e+308,
// 	swapRateThursdayDefault: true,
// 	swapRateFriday: 1.7976931348623157e+308,
// 	swapRateFridayDefault: true,
// 	swapRateSaturday: 1.7976931348623157e+308,
// 	swapRateSaturdayDefault: true,
// 	reTimeout: 4294967295,
// 	reTimeoutDefault: true,
// 	ieCheckMode: 4294967295,
// 	ieCheckModeDefault: true,
// 	ieTimeout: 4294967295,
// 	ieTimeoutDefault: true,
// 	ieSlipProfit: 4294967295,
// 	ieSlipProfitDefault: true,
// 	ieSlipLosing: 4294967295,
// 	ieSlipLosingDefault: true,
// 	bookDepthLimit: 0,
// 	ieFlags: 4294967295,
// 	ieFlagsDefault: true,
// 	swapYearDays: 4294967295,
// 	swapFlags: 4294967295,
// 	swapFlagsDefault: true,
// 	marginMaintenanceBuy: 1.7976931348623157e+308,
// 	marginMaintenanceSell: 1.7976931348623157e+308,
// 	marginMaintenanceBuyLimit: 1.7976931348623157e+308,
// 	marginMaintenanceSellLimit: 1.7976931348623157e+308,
// 	marginMaintenanceBuyStop: 1.7976931348623157e+308,
// 	marginMaintenanceSellStop: 1.7976931348623157e+308,
// 	marginMaintenanceBuyStopLimit: 1.7976931348623157e+308,
// 	marginMaintenanceSellStopLimit: 1.7976931348623157e+308,
// 	marginInitialBuy: 1.7976931348623157e+308,
// 	marginInitialSell: 1.7976931348623157e+308,
// 	marginInitialBuyLimit: 1.7976931348623157e+308,
// 	marginInitialSellLimit: 1.7976931348623157e+308,
// 	marginInitialBuyStop: 1.7976931348623157e+308,
// 	marginInitialSellStop: 1.7976931348623157e+308,
// 	marginInitialBuyStopLimit: 1.7976931348623157e+308,
// 	marginInitialSellStopLimit: 1.7976931348623157e+308,
// 	tradeMode: -1,
// 	tradeModeDefault: true,
// 	execMode: -1,
// 	execModeDefault: true,
// 	fillFlags: -1,
// 	fillFlagsDefault: true,
// 	expirFlags: -1,
// 	expirFlagsDefault: true,
// 	marginFlags: -1,
// 	marginFlagsDefault: true,
// 	swapMode: -1,
// 	swapModeDefault: true,
// 	orderFlags: -1,
// 	orderFlagsDefault: true,
// }

const { t } = useI18n()

const { errorSnackbar } = useSnackbar()

const dialogTitle = computed(() => currentSymbolIndex.value === -1 ? t('Groups.MT5.Crud.Symbols.add-symbol-title') : t('Groups.MT5.Crud.Symbols.edit-symbol-currentsymbol-title', [currentSymbol.value?.path]))

const defaultSortableOptions: UseSortableOptions = {
	handle: '.handle',
	animation: false,
	swapThreshold: 0.1,
	ghostClass: 'ghost',
}

const tbodyRef = ref<HTMLElement>()

useSortable(tbodyRef, computed(() => p.item.symbols), defaultSortableOptions)

const tradeModeItems = enumToItems(Symbols.MT5.TradeMode, 'Symbols.MT5.TradeMode')

const setForEdit = (index: number) => {
	currentSymbolIndex.value = index

	if (index === -1) {
		currentSymbol.value = useCloned(defaultSymbol).cloned.value
	} else {
		currentSymbol.value = p.item.symbols[index]
	}

	editDialogModel.value = true
}

const remove = (index: number) => {
	confirm(t('Groups.MT5.Crud.Symbols.are-you-sure-you-want-to-remove-symbol')).then(() => {
		const clonedSymbols = useCloned(p.item.symbols).cloned.value
		clonedSymbols.splice(index, 1)
		emit('update:item', {
			symbols: clonedSymbols,
		})
	}).catch(() => {})
}

const save = async (updatedSymbol: Groups.MT5.Symbol) => {
	const validation = await editFormRef.value?.validate()

	if (!validation?.valid) {
		return
	}

	if (currentSymbolIndex.value === -1) {
		// check if the symbol exists first
		if (p.item.symbols.find(s => s.path === updatedSymbol.path)) {
			errorSnackbar(`Path ${updatedSymbol.path} already exists. please choose another path`)
			return false
		}
		emit('update:item', {
			symbols: [...p.item.symbols, updatedSymbol],
		})
	} else {
		const clonedSymbols = useCloned(p.item.symbols).cloned.value

		clonedSymbols[currentSymbolIndex.value] = updatedSymbol
		emit('update:item', {
			symbols: clonedSymbols,
		})
	}

	editDialogModel.value = false
}

const getTradeMode = (tradeMode: Symbols.MT5.TradeMode) => {
	return tradeModeItems.find(item => item.value === tradeMode)?.title || ''
}
</script>
