import { defu } from 'defu'
/**
 * Custom hook for handling multi-select enums, providing reactive state management.
 *
 * @param initialValue - The initial numeric value representing selected flags.
 * @param enumType - An object where keys are string representations and values are numeric flags.
 * @returnsA computed property for flag values as an array of numbers.
 *
 * Example:
 * const values = useMultiSelectEnum(initialValue, enumType);
 *
 */
type Options = {
	/**
	 * An array of values to exclude from the returned array.
	 */
	exclude: any[]
}
const defaultOptions: Options = {

	exclude: [],
}

export const useBitwiseValues = (value: number | Ref<number> | ComputedRef<number>, enumType: any, options?: Partial<Options>) => {
	const computedValue = computed(() => {
		return typeof value === 'number' ? value : value.value
	})
	const _options = defu(options, defaultOptions)
	return computed(() => {
		const flags: number[] = []
		Object.values(enumType).forEach((flag: any) => {
			// Include flag only if it is a power of two or zero
			if (!_options.exclude.includes(flag) && (flag & (flag - 1)) === 0 && (computedValue.value & flag) === flag) {
				flags.push(flag)
			}
		})
		return [...new Set(flags)]
	})
}
