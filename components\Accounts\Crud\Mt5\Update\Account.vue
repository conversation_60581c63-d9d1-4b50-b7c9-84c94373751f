<template>
	<v-container>
		<!-- <api-items v-slot="props" :url="Groups.Lookup.URL" :error-messages="errors.group">
			<v-autocomplete v-model="item.group" :label="Group" :rules="rules({required:true})" v-bind="props" clearable />
		</api-items> -->
		<groups-tree-select
			v-model="item.group"
			:error-messages="errors.group"
			:label="$t('Accounts.MT5.Crud.groups')"
		/>

		<v-row>
			<v-col
				cols="12"
				md="6"
			>
				<background-color
					v-model:background="item.color"
					:label="$t('Accounts.MT5.Crud.color')"
					:error-messages="errors.color"
				/>
			</v-col>
			<v-col
				cols="12"
				md="6"
			>
				<v-select
					v-model="item.leverage"
					:label="$t('Accounts.MT5.Crud.leverage')"
					:items="leverageItems"
					:error-messages="errors.leverage"
				/>
			</v-col>
		</v-row>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.enable-this-account')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_ENABLED"
		/>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.enable-password-change')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_PASSWORD"
		/>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.enable-one-time-password')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_OTP_ENABLED"
		/>
		<v-checkbox
			v-model="rightsModel"
			color="primary"
			hide-details
			class="compact"
			:label="$t('Accounts.MT5.Crud.change-password-at-next-login')"
			:true-value="Accounts.MT5.EnUsersRights.USER_RIGHT_RESET_PASS"
		/>
	</v-container>
</template>

<script lang="ts" setup>
import { defaults } from './Shared'
import type { Props, Emit } from './Shared'
import { Accounts } from '~/types'
// import { AccountsTrades } from '~/types'

const p = withDefaults(defineProps<Props>(), defaults)

const emit = defineEmits<Emit>()

const leverageItems = [
	{ title: '1:1000', value: 1000 },
	{ title: '1:500', value: 500 },
	{ title: '1:400', value: 400 },
	{ title: '1:300', value: 300 },
	{ title: '1:200', value: 200 },
	{ title: '1:175', value: 175 },
	{ title: '1:150', value: 150 },
	{ title: '1:125', value: 125 },
	{ title: '1:100', value: 100 },
	{ title: '1:75', value: 75 },
	{ title: '1:50', value: 50 },
	{ title: '1:33', value: 33 },
	{ title: '1:25', value: 25 },
	{ title: '1:20', value: 20 },
	{ title: '1:10', value: 10 },
	{ title: '1:5', value: 5 },
	{ title: '1:3', value: 3 },
	{ title: '1:2', value: 2 },
	{ title: '1:1', value: 1 },
]

const computedRights = computed({
	get: () => p.item.rights,
	set: v => emit('update:item', { rights: v }),
})

const { model: rightsModel } = useMultiSelectEnum(computedRights, Accounts.MT5.EnUsersRights)
</script>
